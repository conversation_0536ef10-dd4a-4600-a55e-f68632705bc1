// run by cd /Users/<USER>/Freelance/pathways-2.1 && npx tsx scripts/reset-relationships-v2.ts
import { PrismaClient } from '../src/generated/prisma'

const prisma = new PrismaClient()

async function resetRelationships() {
  console.log('🚀 Starting relationship reset (Optimized Version)...')
  
  try {
    // Step 1: Delete all existing history records (outside transaction for speed)
    console.log('🗑️  Deleting all existing relationship history...')
    
    const deletedMentorHistory = await prisma.mentorMenteeHistory.deleteMany({})
    console.log(`   ✅ Deleted ${deletedMentorHistory.count} MentorMenteeHistory records`)
    
    const deletedSupervisorHistory = await prisma.supervisorTraineeHistory.deleteMany({})
    console.log(`   ✅ Deleted ${deletedSupervisorHistory.count} SupervisorTraineeHistory records`)

    // Step 2: Get all current mentor-mentee relationships from User.mentorId
    console.log('👥 Recreating mentor-mentee relationships...')
    
    const traineesWithMentors = await prisma.user.findMany({
      where: {
        mentorId: { not: null },
        role: 'TRAINEE'
      },
      select: {
        id: true,
        name: true,
        mentorId: true,
        mentor: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    // Create mentor-mentee history records in batch
    const mentorHistoryData = traineesWithMentors
      .filter(trainee => trainee.mentorId)
      .map(trainee => ({
        mentorId: trainee.mentorId!,
        menteeId: trainee.id,
        isActive: true,
        startDate: new Date(),
      }))

    if (mentorHistoryData.length > 0) {
      await prisma.mentorMenteeHistory.createMany({
        data: mentorHistoryData
      })
      traineesWithMentors.forEach(trainee => {
        if (trainee.mentorId) {
          console.log(`   ✅ Created mentor relationship: ${trainee.mentor?.name} -> ${trainee.name}`)
        }
      })
    }

    // Step 3: Get all current supervisor-trainee relationships from active placements
    console.log('👔 Recreating supervisor-trainee relationships...')
    
    const activePlacements = await prisma.placement.findMany({
      where: {
        supervisorId: { not: null },
        // Only active placements (no endDate or endDate in future)
        OR: [
          { endDate: null },
          { endDate: { gte: new Date() } }
        ]
      },
      select: {
        id: true,
        name: true,
        userId: true,
        supervisorId: true,
        user: {
          select: {
            id: true,
            name: true
          }
        },
        supervisor: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    // Remove duplicates (same supervisor-trainee-placement combination)
    const uniqueSupervisorHistoryData = []
    const seen = new Set<string>()

    for (const placement of activePlacements) {
      if (placement.supervisorId) {
        const key = `${placement.supervisorId}-${placement.userId}-${placement.id}`
        if (!seen.has(key)) {
          seen.add(key)
          uniqueSupervisorHistoryData.push({
            supervisorId: placement.supervisorId,
            traineeId: placement.userId,
            placementId: placement.id,
            isActive: true,
            startDate: new Date(),
          })
          console.log(`   ✅ Will create supervisor relationship: ${placement.supervisor?.name} -> ${placement.user.name} (${placement.name})`)
        } else {
          console.log(`   ⚠️  Skipping duplicate: ${placement.supervisor?.name} -> ${placement.user.name} (${placement.name})`)
        }
      }
    }

    // Create supervisor-trainee history records in batch
    if (uniqueSupervisorHistoryData.length > 0) {
      await prisma.supervisorTraineeHistory.createMany({
        data: uniqueSupervisorHistoryData
      })
      console.log(`   ✅ Created ${uniqueSupervisorHistoryData.length} supervisor relationships`)
    }

    // Step 4: Summary
    const finalMentorCount = await prisma.mentorMenteeHistory.count({ where: { isActive: true } })
    const finalSupervisorCount = await prisma.supervisorTraineeHistory.count({ where: { isActive: true } })
    
    console.log('\n📊 Final Summary:')
    console.log(`   👥 Active Mentor-Mentee relationships: ${finalMentorCount}`)
    console.log(`   👔 Active Supervisor-Trainee relationships: ${finalSupervisorCount}`)

    console.log('\n✅ Relationship reset completed successfully!')
    
  } catch (error) {
    console.error('❌ Error during relationship reset:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the script
resetRelationships()
  .catch((error) => {
    console.error('❌ Script failed:', error)
    process.exit(1)
  })
