import { PrismaClient } from '../src/generated/prisma'

const prisma = new PrismaClient()

async function checkRelationships() {
  console.log('🔍 Checking current relationship status...\n')
  
  try {
    // 1. Check Mentor-Mentee relationships
    console.log('👥 MENTOR-MENTEE RELATIONSHIPS:')
    console.log('================================')
    
    // From User.mentorId (current assignment)
    const traineesWithMentors = await prisma.user.findMany({
      where: {
        mentorId: { not: null },
        role: 'TRAINEE'
      },
      select: {
        id: true,
        name: true,
        email: true,
        mentor: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: { name: 'asc' }
    })

    console.log(`📋 Current assignments (User.mentorId): ${traineesWithMentors.length}`)
    traineesWithMentors.forEach(trainee => {
      console.log(`   ${trainee.name} -> ${trainee.mentor?.name}`)
    })

    // From MentorMenteeHistory (active)
    const activeMentorHistory = await prisma.mentorMenteeHistory.findMany({
      where: { isActive: true },
      include: {
        mentor: { select: { name: true } },
        mentee: { select: { name: true } }
      },
      orderBy: { startDate: 'desc' }
    })

    console.log(`\n📋 Active history records: ${activeMentorHistory.length}`)
    activeMentorHistory.forEach(record => {
      console.log(`   ${record.mentee.name} -> ${record.mentor.name} (since ${record.startDate.toLocaleDateString()})`)
    })

    // Past/inactive records
    const inactiveMentorHistory = await prisma.mentorMenteeHistory.findMany({
      where: { isActive: false },
      include: {
        mentor: { select: { name: true } },
        mentee: { select: { name: true } }
      },
      orderBy: { endDate: 'desc' }
    })

    console.log(`\n📋 Past/inactive records: ${inactiveMentorHistory.length}`)
    inactiveMentorHistory.slice(0, 10).forEach(record => {
      console.log(`   ${record.mentee.name} -> ${record.mentor.name} (ended ${record.endDate?.toLocaleDateString() || 'unknown'})`)
    })

    // 2. Check Supervisor-Trainee relationships
    console.log('\n\n👔 SUPERVISOR-TRAINEE RELATIONSHIPS:')
    console.log('====================================')

    // Active placements with supervisors
    const activePlacements = await prisma.placement.findMany({
      where: {
        supervisorId: { not: null },
        OR: [
          { endDate: null },
          { endDate: { gte: new Date() } }
        ]
      },
      select: {
        id: true,
        name: true,
        endDate: true,
        user: { select: { name: true } },
        supervisor: { select: { name: true } }
      },
      orderBy: { createdAt: 'desc' }
    })

    console.log(`📋 Active placements with supervisors: ${activePlacements.length}`)
    activePlacements.forEach(placement => {
      const status = placement.endDate ? `ends ${placement.endDate.toLocaleDateString()}` : 'ongoing'
      console.log(`   ${placement.user.name} -> ${placement.supervisor?.name} (${placement.name} - ${status})`)
    })

    // From SupervisorTraineeHistory (active)
    const activeSupervisorHistory = await prisma.supervisorTraineeHistory.findMany({
      where: { isActive: true },
      include: {
        supervisor: { select: { name: true } },
        trainee: { select: { name: true } },
        placement: { select: { name: true } }
      },
      orderBy: { startDate: 'desc' }
    })

    console.log(`\n📋 Active supervisor history records: ${activeSupervisorHistory.length}`)
    activeSupervisorHistory.forEach(record => {
      console.log(`   ${record.trainee.name} -> ${record.supervisor.name} (${record.placement.name} since ${record.startDate.toLocaleDateString()})`)
    })

    // Past/inactive records
    const inactiveSupervisorHistory = await prisma.supervisorTraineeHistory.findMany({
      where: { isActive: false },
      include: {
        supervisor: { select: { name: true } },
        trainee: { select: { name: true } },
        placement: { select: { name: true } }
      },
      orderBy: { endDate: 'desc' }
    })

    console.log(`\n📋 Past/inactive supervisor records: ${inactiveSupervisorHistory.length}`)
    inactiveSupervisorHistory.slice(0, 10).forEach(record => {
      console.log(`   ${record.trainee.name} -> ${record.supervisor.name} (${record.placement.name} ended ${record.endDate?.toLocaleDateString() || 'unknown'})`)
    })

    // 3. Identify inconsistencies
    console.log('\n\n⚠️  POTENTIAL INCONSISTENCIES:')
    console.log('==============================')

    // Check mentor inconsistencies
    const mentorInconsistencies = []
    for (const trainee of traineesWithMentors) {
      const hasActiveHistory = activeMentorHistory.some(h => 
        h.menteeId === trainee.id && h.mentorId === trainee.mentor?.id
      )
      if (!hasActiveHistory) {
        mentorInconsistencies.push(`${trainee.name} has mentor ${trainee.mentor?.name} but no active history record`)
      }
    }

    if (mentorInconsistencies.length > 0) {
      console.log('\n🔴 Mentor relationship inconsistencies:')
      mentorInconsistencies.forEach(issue => console.log(`   ${issue}`))
    } else {
      console.log('\n✅ Mentor relationships are consistent')
    }

    // Check supervisor inconsistencies
    const supervisorInconsistencies = []
    for (const placement of activePlacements) {
      const hasActiveHistory = activeSupervisorHistory.some(h => 
        h.traineeId === placement.user.name && // Note: this is a simplified check
        h.supervisorId === placement.supervisor?.name && 
        h.placementId === placement.id
      )
      // This is a simplified check - in real implementation you'd compare IDs
    }

    if (supervisorInconsistencies.length === 0) {
      console.log('✅ Supervisor relationships look consistent')
    }

    console.log('\n📊 SUMMARY:')
    console.log('===========')
    console.log(`👥 Current mentor assignments: ${traineesWithMentors.length}`)
    console.log(`👥 Active mentor history: ${activeMentorHistory.length}`)
    console.log(`👥 Past mentor history: ${inactiveMentorHistory.length}`)
    console.log(`👔 Active placements: ${activePlacements.length}`)
    console.log(`👔 Active supervisor history: ${activeSupervisorHistory.length}`)
    console.log(`👔 Past supervisor history: ${inactiveSupervisorHistory.length}`)

  } catch (error) {
    console.error('❌ Error checking relationships:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the check
checkRelationships()
  .catch((error) => {
    console.error('❌ Script failed:', error)
    process.exit(1)
  })
