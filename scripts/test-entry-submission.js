const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testEntrySubmissionFeature() {
  console.log('🧪 Testing Entry Submission Feature...\n');

  try {
    // 1. Find a test trainee with placements
    const trainee = await prisma.user.findFirst({
      where: { role: 'TRAINEE' },
      include: {
        placements: {
          include: {
            supervisor: true,
            mentor: true,
          }
        }
      }
    });

    if (!trainee || !trainee.placements.length) {
      console.log('❌ No trainee with placements found');
      return;
    }

    const placement = trainee.placements[0];
    console.log(`✅ Found trainee: ${trainee.name}`);
    console.log(`✅ Found placement: ${placement.name}`);
    console.log(`✅ Supervisor: ${placement.supervisor?.name || 'None'}`);
    console.log(`✅ Mentor: ${placement.mentor?.name || 'None'}\n`);

    // 2. Find or create a test entry
    let entry = await prisma.entry.findFirst({
      where: {
        placementId: placement.id,
        status: 'draft'
      },
      include: {
        entrySubmissions: {
          include: {
            submission: true
          }
        }
      }
    });

    if (!entry) {
      // Create a test entry
      entry = await prisma.entry.create({
        data: {
          title: 'Test Entry for Submission Feature',
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-01-07'),
          experience: 'Test experience content',
          evidence: 'Test evidence content',
          learnt: 'Test learning content',
          contentiousType: 'NON_CONTENTIOUS',
          practiceAreas: ['Corporate Law'],
          placementId: placement.id,
          status: 'draft'
        },
        include: {
          entrySubmissions: {
            include: {
              submission: true
            }
          }
        }
      });
      console.log(`✅ Created test entry: ${entry.title}`);
    } else {
      console.log(`✅ Found existing entry: ${entry.title}`);
    }

    console.log(`📊 Entry has ${entry.entrySubmissions.length} submissions\n`);

    // 3. Test sending to supervisor (if exists)
    if (placement.supervisor) {
      console.log('🔄 Testing submission to supervisor...');
      
      // Check if already sent to supervisor
      const existingSupSubmission = entry.entrySubmissions.find(es => 
        es.submission.reviewerId === placement.supervisor.id
      );

      if (!existingSupSubmission) {
        // Create submission to supervisor
        const submission = await prisma.submission.create({
          data: {
            title: 'Test Submission to Supervisor',
            startDate: entry.startDate,
            endDate: entry.endDate,
            status: 'pending',
            reviewerRole: 'supervisor',
            placementId: placement.id,
            traineeId: trainee.id,
            reviewerId: placement.supervisor.id,
            supervisorId: placement.supervisor.id
          }
        });

        // Create EntrySubmission record
        await prisma.entrySubmission.create({
          data: {
            entryId: entry.id,
            submissionId: submission.id,
            status: 'submitted'
          }
        });

        console.log(`✅ Created submission to supervisor: ${submission.id}`);
      } else {
        console.log(`ℹ️  Entry already submitted to supervisor`);
      }
    }

    // 4. Test sending to mentor (if exists)
    if (placement.mentor) {
      console.log('🔄 Testing submission to mentor...');
      
      // Check if already sent to mentor
      const existingMentorSubmission = entry.entrySubmissions.find(es => 
        es.submission.reviewerId === placement.mentor.id
      );

      if (!existingMentorSubmission) {
        // Create submission to mentor
        const submission = await prisma.submission.create({
          data: {
            title: 'Test Submission to Mentor',
            startDate: entry.startDate,
            endDate: entry.endDate,
            status: 'pending',
            reviewerRole: 'mentor',
            placementId: placement.id,
            traineeId: trainee.id,
            reviewerId: placement.mentor.id
          }
        });

        // Create EntrySubmission record
        await prisma.entrySubmission.create({
          data: {
            entryId: entry.id,
            submissionId: submission.id,
            status: 'submitted'
          }
        });

        console.log(`✅ Created submission to mentor: ${submission.id}`);
      } else {
        console.log(`ℹ️  Entry already submitted to mentor`);
      }
    }

    // 5. Verify final state
    const updatedEntry = await prisma.entry.findUnique({
      where: { id: entry.id },
      include: {
        entrySubmissions: {
          include: {
            submission: {
              include: {
                reviewer: true
              }
            }
          }
        }
      }
    });

    console.log('\n📊 Final Entry State:');
    console.log(`Entry ID: ${updatedEntry.id}`);
    console.log(`Status: ${updatedEntry.status}`);
    console.log(`Submissions: ${updatedEntry.entrySubmissions.length}`);
    
    updatedEntry.entrySubmissions.forEach((es, index) => {
      console.log(`  ${index + 1}. To ${es.submission.reviewer.name} (${es.submission.reviewerRole}) - Status: ${es.status}`);
    });

    // 6. Test API-like logic
    console.log('\n🔍 Testing availability logic:');
    
    if (placement.supervisor) {
      const hasSubmissionToSupervisor = updatedEntry.entrySubmissions.some(es => 
        es.submission.reviewerId === placement.supervisor.id
      );
      console.log(`Available for supervisor: ${!hasSubmissionToSupervisor ? '✅ Yes' : '❌ No'}`);
    }

    if (placement.mentor) {
      const hasSubmissionToMentor = updatedEntry.entrySubmissions.some(es => 
        es.submission.reviewerId === placement.mentor.id
      );
      console.log(`Available for mentor: ${!hasSubmissionToMentor ? '✅ Yes' : '❌ No'}`);
    }

    console.log('\n🎉 Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testEntrySubmissionFeature();
