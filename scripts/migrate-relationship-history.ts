import { PrismaClient } from '../src/generated/prisma';

const prisma = new PrismaClient();

async function migrateMentorMenteeHistory() {
  console.log('Starting mentor-mentee history migration...');
  
  // Get all current mentor-mentee relationships
  const currentRelationships = await prisma.user.findMany({
    where: {
      mentorId: { not: null }
    },
    select: {
      id: true,
      mentorId: true,
      createdAt: true
    }
  });

  console.log(`Found ${currentRelationships.length} current mentor-mentee relationships`);

  // Create history records for current relationships
  for (const relationship of currentRelationships) {
    if (relationship.mentorId) {
      await prisma.mentorMenteeHistory.create({
        data: {
          mentorId: relationship.mentorId,
          menteeId: relationship.id,
          startDate: relationship.createdAt,
          isActive: true
        }
      });
    }
  }

  console.log('Mentor-mentee history migration completed');
}

async function migrateSupervisorTraineeHistory() {
  console.log('Starting supervisor-trainee history migration...');
  
  // Get all placements with supervisors
  const placementsWithSupervisors = await prisma.placement.findMany({
    where: {
      supervisorId: { not: null }
    },
    select: {
      id: true,
      supervisorId: true,
      userId: true,
      startDate: true,
      endDate: true
    }
  });

  console.log(`Found ${placementsWithSupervisors.length} placements with supervisors`);

  // Create history records for placements
  for (const placement of placementsWithSupervisors) {
    if (placement.supervisorId) {
      await prisma.supervisorTraineeHistory.create({
        data: {
          supervisorId: placement.supervisorId,
          traineeId: placement.userId,
          placementId: placement.id,
          startDate: placement.startDate,
          endDate: placement.endDate,
          isActive: !placement.endDate // Active if no end date
        }
      });
    }
  }

  console.log('Supervisor-trainee history migration completed');
}

async function main() {
  try {
    await migrateMentorMenteeHistory();
    await migrateSupervisorTraineeHistory();
    console.log('All migrations completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
