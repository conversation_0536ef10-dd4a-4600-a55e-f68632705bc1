stages:
  - build
  - deploy

build-frontend:
  stage: build
  tags:
    - shell
  script:
    - nvm install 18 && nvm use 18
    - npm i -g yarn vercel
    - yarn install
    - vercel build
  artifacts:
    paths:
      - .next
      - .vercel/output
    expire_in: 1 day

deploy to preview:
  stage: deploy
  tags:
    - shell
  except:
    - main
  script:
    - export NEXT_WORKER_THREADS=2
    - nvm install 18 && nvm use 18
    - npm i -g yarn vercel
    - yarn install
    - URL=$(vercel deploy --prebuilt --skip-domain --archive=tgz --token=$VERCEL)
    - echo "DEPLOY DONE"
    - echo $URL
  dependencies:
    - build-frontend

deploy to production:
  stage: deploy
  tags:
    - shell
  only:
    - main
  script:
    - nvm install 18 && nvm use 18
    - npm i -g yarn vercel
    - yarn install
    - URL=$(vercel deploy --prebuilt --prod --archive=tgz --token=$VERCEL)
  dependencies:
    - build-frontend
