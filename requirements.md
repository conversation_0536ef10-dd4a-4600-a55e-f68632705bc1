# 🧾 Project Brief: Placement Management Web App (Next.js + Ant Design)

## 🔧 Tech Stack

- **Frontend**: [Next.js](https://nextjs.org/) (TypeScript)
- **UI Framework**: [Ant Design](https://ant.design/)
- **State Management**: Zustand
- **Authentication**: Email/password via NextAuth.js or JWT
- **Backend API**: Next.js API Routes or Express API
- **Database**: PostgreSQL
- **ORM**: Prisma

## 🗄️ Database Setup
- **Database**: PostgreSQL via Docker container
- **Container**: `pathway21-db` (port 54778)
- **Credentials**:
  - Username: `pathway21`
  - Password: `pathway21`
  - Database: `pathway21`

## 📊 Project Flow
1. **Client** creates **Manager** accounts
2. **Manager**:
   - Adds **Candidates** (Trainees)
   - Assigns **Mentors** to Trainees

3. For each **Trainee**:
   - Assigned 1 **Mentor**
   - Can have multiple **Supervisors** (based on placements)

## 📝 Entry Review Flow
1. **Trainee** creates entry
2. Submission options:
   - To **Supervisor** (workplace verification)
   - To **Mentor** (competency assessment)

3. Review process:
   - **Supervisor**: Verifies work done
   - **Mentor**: Assesses competencies

## 🔄 Data Relationships
1. **Trainee & Placement**:
   ```
   TRAINEE
   └── Has many PLACEMENTS
       └── Each Placement:
           ├── One SUPERVISOR (required)
           ├── Start Date (required)
           ├── End Date (optional)
           ├── Full/Part time status
           └── Collection of ENTRIES
   ```

2. **Entry Submission Flow**:
   - Trainee can submit entries to:
     1. Supervisor of that placement
     2. Mentor (Training Principal)
   - Rules:
     - Entries must belong to the same placement
     - Cannot submit to both Supervisor and Mentor simultaneously
     - Limit of 20 entries per submission

## 🔑 Sample Accounts
- **Admin**: <EMAIL> / admin123
- **Mentor**: <EMAIL> / admin123
- **Supervisor**: <EMAIL> / admin123
- **Trainee**: <EMAIL> / admin123

## 🛠️ Useful Commands
1. **Reset database and apply schema**:
   ```
   npx prisma db push --force-reset
   ```

2. **Recreate sample data**:
   ```
   npx prisma db seed
   ```

3. **View database structure**:
   ```
   npx prisma studio
   ```

4. **View table data**:
   ```
   docker exec pathway21-db psql -U pathway21 -c "SELECT * FROM \"User\";"
   ```

---

## 👥 User Roles & Permissions

### 1. Admin
- [ coded - in testing ] Create and manage: Clients, Supervisors, Mentors, Candidates
- [ coded - in testing ] Modify profile info and reset passwords
- [ coded - in testing ] Create clients
- [ coded - in testing ] Create placements for each client
- [ coded - in testing ] Assign supervisors/mentors to placements
- [ coded - in testing ] Manage skill sets
- [ coded - in testing ] View and manage all entries

### 2. Supervisor
- [ coded - in testing ] Review candidate placements
- [ coded - in testing ] Approve/reject entries

### 3. Mentor
Core User Stories / Flows for Mentor:
1, View Assigned Trainee Progress:
As a Mentor, I want to access a Mentor Dashboard so that I can get a real-time overview of the progress of the Trainees assigned to me.
Flow: Mentor logs in -> Navigates to Dashboard -> Sees a summary view of their Trainees and potentially key stats or recent activity.
2, Review Trainee Entries:
As a Mentor, I want to view entries submitted by my Trainees ("Sent for Review" status) so that I can evaluate their work.
As a Mentor, I want to provide feedback directly on Trainee entries so that I can guide their development and suggest improvements.
As a Mentor, I want to update the status of a Trainee's entry (e.g., "Approved", "Reviewed with Feedback", "Rejected/Flagged") so that the Trainee and Supervisor are aware of the review outcome.
Flow: Mentor receives notification or checks dashboard/review page -> Selects an entry "Sent for Review" -> Views the entry content (file/details) -> Adds feedback comments -> Selects and saves a new status (Approved, Reviewed with Feedback, etc.) -> System updates status and potentially notifies Trainee/Supervisor.
3, Manage Mentoring Sessions:
As a Mentor, I want to define my availability for mentoring sessions so that Trainees can request meetings. (Similar to Connect interview slots).
As a Mentor, I want to receive and manage meeting requests from Trainees so that I can schedule and confirm sessions.
As a Mentor, I want to conduct mentoring sessions and maintain documentation related to them (The spec mentions this but is light on how documentation is stored - potentially notes linked to the meeting).
Flow: Mentor sets availability -> Trainee requests meeting -> Mentor confirms/declines -> Session occurs -> Mentor potentially adds session notes to the system.
4, Escalate Issues to Supervisor:
As a Mentor, I want the ability to refer specific issues or entries to the relevant Supervisor when necessary (e.g., complex problems, entries needing higher-level review after mentor feedback).
Flow: During entry review or session, Mentor identifies an issue -> Mentor flags the entry or triggers an escalation action -> Supervisor is notified or sees the flagged item.
5, Receive Notifications:
As a Mentor, I want to receive automated notifications for key events like new entries submitted for review, meeting requests/confirmations, or feedback acknowledgments so that I can stay informed and responsive.

### 4. Candidate (Trainee)
- [coded] View assigned placement
- [coded] Upload entry documents with tagged skills
- [coded] Submit entries for approval
- [coded] Flow submit entries:
  1. Select Placement
  2. Select Reviewer (Mentor or Supervisor of the placement)
  3. Select Date Range
  4. Select Entries to submit (must belong to the selected placement)
  5. Submit Entries

---

## 🏗️ Entities & Relationships

- **Client**
  - Fields: name, address, etc.
  - Has many Supervisors

- **Placement**
  - Belongs to Client
  - Assigned: 1 Candidate, 1 Supervisor, 1 Mentor
  - Has many Entries

- **Entry**
  - Belongs to Placement
  - Contains:
    - Uploaded document
    - Selected skills (tags)
    - Status: `Pending | Approved | Rejected`
    - Timestamp

- **Skill Set**
  - Created by Admin
  - Assigned to Candidates
  - Consists of many Skills
  - Hierarchical structure:
    - A. Advocacy
    - B. Dispute Resolution
    - C. Case and Transaction Management
    - D. Communication Skills
    - E. Client Relationships

---

## 🔐 Authentication & Access Control

- Email/password login
- Role-based routing and authorization (HOC/middleware)
- Session persistence with cookie/JWT

---

## 📄 Page Structure (Routes)

### `/login`
- Email/password login form

### `/dashboard`
- Redirect based on role

---

### `/admin`

#### Navigation
- Clients
- Users (Supervisors / Mentors / Candidates)
- Placements
- Skill Sets
- Entries

#### Pages

- `/admin/clients`
  - Create/update/delete clients

- `/admin/users`
  - Create/update/delete users
  - Filter by role

- `/admin/placements`
  - Assign Supervisor & Mentor
  - Modify candidate assignments

- `/admin/skills`
  - Create/manage skill sets
  - Assign skill sets to candidates

- `/admin/entries`
  - View all submitted entries
  - Filter by status, skills, client

---

### `/supervisor` and `/mentor`
- `/entries`
  - View list of candidate entries
  - Approve or reject via modal confirmation

---

### `/candidate`

- `/placement`
  - View placement info, supervisor/mentor info

- `/submit-entry`
  - Upload document
  - Select skills (from assigned set)
  - Submit for approval

---

## 🎨 UI/UX Design Guidelines

- **Brand Color**: `rgb(199, 100, 27)` (Ant Design custom theme)
- **Logo**:
  ```jsx
  <img src="https://admin.accutraineepw.com/images/logos/logo--dark.svg" alt="Logo" height="40" />
  ```

- **Components**:
  - `antd` Layout, Menu, Form, Table, Tabs, Modal, Upload.Dragger, Checkbox.Group, Tag, etc.
  - Role-based component rendering

---

## ✅ Optional Enhancements

- Notifications for entry approvals
- Email triggers (via nodemailer)
- File versioning for re-uploads
- Audit logs for admin actions
- Real-time dashboard with placement progress

---

## 📁 Suggested Folder Structure

```
/pages
  /admin
    clients.tsx
    users.tsx
    placements.tsx
    skills.tsx
    entries.tsx
  /supervisor
    entries.tsx
  /mentor
    entries.tsx
  /candidate
    placement.tsx
    submit-entry.tsx
  /login.tsx
/components
  Layout.tsx
  ProtectedRoute.tsx
  EntryForm.tsx
  SkillSelector.tsx
  UserForm.tsx
/context
  AuthContext.tsx
/lib
  auth.ts
  api.ts
/styles
  theme.less
```

---

Would you like code scaffolding or component examples as a follow-up?
