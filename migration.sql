-- CreateEnum
CREATE TYPE "UserRole" AS ENUM ('ADMIN', 'SUPERVISOR', 'MENTOR', 'TRAINEE');

-- CreateEnum
CREATE TYPE "EntryStatus" AS ENUM ('draft', 'submitted', 'signedoff', 'rejected');

-- CreateEnum
CREATE TYPE "PracticeSubSkillType" AS ENUM ('sqe', 'tc', 'both');

-- CreateEnum
CREATE TYPE "QualificationType" AS ENUM ('A_LEVEL', 'DEGREE', 'GDL', 'LPC', 'PSC', 'SQE1', 'SQE2', 'QUALIFICATION_CERT', 'PRACTICING_CERT', 'OTHER');

-- CreateEnum
CREATE TYPE "QualificationStatus" AS ENUM ('PENDING', 'VERIFIED', 'REJECTED');

-- CreateEnum
CREATE TYPE "MeetingType" AS ENUM ('MONTHLY', 'TPM', 'APPRAISAL');

-- <PERSON>reate<PERSON>num
CREATE TYPE "MeetingStatus" AS ENUM ('PENDING', 'ACCEPTED', 'DECLINED', 'COMPLETED');

-- CreateEnum
CREATE TYPE "MeetingFormat" AS ENUM ('IN_PERSON', 'VIRTUAL');

-- CreateEnum
CREATE TYPE "DocumentType" AS ENUM ('MONTHLY_REVIEW', 'FEEDBACK_FORM', 'OTHER');

-- CreateEnum
CREATE TYPE "FeedbackFormStatus" AS ENUM ('DRAFT', 'SUBMITTED', 'APPROVED');

-- CreateTable
CREATE TABLE "Account" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "providerAccountId" TEXT NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" TEXT,
    "scope" TEXT,
    "id_token" TEXT,
    "session_state" TEXT,

    CONSTRAINT "Account_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Session" (
    "id" TEXT NOT NULL,
    "sessionToken" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Session_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "email" TEXT,
    "emailVerified" TIMESTAMP(3),
    "image" TEXT,
    "password" TEXT,
    "role" "UserRole" NOT NULL DEFAULT 'TRAINEE',
    "traineeLevel" INTEGER,
    "phoneNumber" TEXT,
    "address" TEXT,
    "qualificationRoute" TEXT,
    "mentorId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "skillGroupType" TEXT DEFAULT 'TRADITIONAL',

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VerificationToken" (
    "identifier" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL
);

-- CreateTable
CREATE TABLE "Client" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "phone" TEXT,
    "address" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Client_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ClientSupervisor" (
    "clientId" TEXT NOT NULL,
    "supervisorId" TEXT NOT NULL,
    "assignedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ClientSupervisor_pkey" PRIMARY KEY ("clientId","supervisorId")
);

-- CreateTable
CREATE TABLE "Placement" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "name" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3),
    "orgPosition" TEXT,
    "orgSraNumber" TEXT,
    "supervisorName" TEXT NOT NULL,
    "supervisorEmail" TEXT NOT NULL,
    "isFullTime" BOOLEAN NOT NULL DEFAULT true,
    "partTimeDays" INTEGER[],
    "daysMissed" INTEGER NOT NULL DEFAULT 0,
    "documentKey" TEXT,
    "documentName" TEXT,
    "userId" TEXT NOT NULL,
    "clientId" TEXT NOT NULL,
    "supervisorId" TEXT,
    "mentorId" TEXT,

    CONSTRAINT "Placement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PracticeArea" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PracticeArea_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PracticeSkill" (
    "id" TEXT NOT NULL,
    "practiceAreaId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PracticeSkill_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PracticeSkillGroup" (
    "id" TEXT NOT NULL,
    "practiceSkillId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PracticeSkillGroup_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PracticeSubSkill" (
    "id" TEXT NOT NULL,
    "practiceSkillGroupId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "practiceSubSkillType" "PracticeSubSkillType" NOT NULL,
    "minSuggestedEntryCount" INTEGER NOT NULL,
    "order" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PracticeSubSkill_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EntrySubSkill" (
    "entryId" TEXT NOT NULL,
    "subSkillId" TEXT NOT NULL,

    CONSTRAINT "EntrySubSkill_pkey" PRIMARY KEY ("entryId","subSkillId")
);

-- CreateTable
CREATE TABLE "Entry" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "title" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "experience" TEXT NOT NULL,
    "evidence" TEXT NOT NULL,
    "learnt" TEXT NOT NULL,
    "moreExperience" TEXT,
    "needMoreExperience" TEXT,
    "contentiousType" TEXT NOT NULL,
    "taskedBy" TEXT,
    "documentKey" TEXT,
    "documentName" TEXT,
    "practiceAreas" TEXT[],
    "submittedAt" TIMESTAMP(3),
    "reviewedAt" TIMESTAMP(3),
    "feedback" TEXT,
    "placementId" TEXT NOT NULL,
    "reviewerId" TEXT,
    "submissionId" TEXT,
    "status" TEXT NOT NULL DEFAULT 'draft',

    CONSTRAINT "Entry_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Submission" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "submittedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "traineeId" TEXT NOT NULL,
    "reviewerId" TEXT,
    "supervisorId" TEXT,

    CONSTRAINT "Submission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TraineeSkill" (
    "id" TEXT NOT NULL,
    "doneEntryCount" INTEGER NOT NULL DEFAULT 0,
    "minSuggestedEntryCount" INTEGER NOT NULL DEFAULT 1,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "traineeId" TEXT NOT NULL,
    "subSkillId" TEXT NOT NULL,

    CONSTRAINT "TraineeSkill_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Qualification" (
    "id" TEXT NOT NULL,
    "type" "QualificationType" NOT NULL,
    "name" TEXT NOT NULL,
    "fileUrl" TEXT,
    "fileName" TEXT,
    "startDate" TIMESTAMP(3),
    "endDate" TIMESTAMP(3),
    "grade" TEXT,
    "status" "QualificationStatus" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT NOT NULL,

    CONSTRAINT "Qualification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CharacterSuitability" (
    "id" TEXT NOT NULL,
    "rules" TEXT NOT NULL,
    "infoForQualifiedSolicitors" TEXT NOT NULL,
    "externalLinks" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CharacterSuitability_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CharacterSuitabilityCompletion" (
    "id" TEXT NOT NULL,
    "completed" BOOLEAN NOT NULL DEFAULT false,
    "completedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT NOT NULL,
    "characterSuitabilityId" TEXT NOT NULL,

    CONSTRAINT "CharacterSuitabilityCompletion_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MentorAvailability" (
    "id" TEXT NOT NULL,
    "mentorId" TEXT NOT NULL,
    "startTime" TIMESTAMP(3) NOT NULL,
    "endTime" TIMESTAMP(3) NOT NULL,
    "isBooked" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "format" "MeetingFormat" NOT NULL DEFAULT 'VIRTUAL',

    CONSTRAINT "MentorAvailability_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Meeting" (
    "id" TEXT NOT NULL,
    "type" "MeetingType" NOT NULL,
    "candidateId" TEXT NOT NULL,
    "mentorId" TEXT NOT NULL,
    "availabilityId" TEXT,
    "status" "MeetingStatus" NOT NULL DEFAULT 'PENDING',
    "proposedTime" TIMESTAMP(3) NOT NULL,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Meeting_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FeedbackForm" (
    "id" TEXT NOT NULL,
    "monthlyReviewId" TEXT NOT NULL,
    "coveringMonth" TEXT,
    "menteeName" TEXT NOT NULL,
    "meetingDate" TIMESTAMP(3) NOT NULL,
    "pointsToDiscuss" TEXT,
    "wasGoodWork" BOOLEAN NOT NULL DEFAULT false,
    "goodWorkComments" TEXT,
    "hasFeltResponsible" BOOLEAN NOT NULL DEFAULT false,
    "responsibilityComments" TEXT,
    "workConcerns" TEXT,
    "isWorkingWellSupervisor" BOOLEAN NOT NULL DEFAULT false,
    "workingWellSupervisorComments" TEXT,
    "hadInformalMonthlyFeedback" BOOLEAN NOT NULL DEFAULT false,
    "informalMonthlyFeedbackComments" TEXT,
    "skillStandardsComments" TEXT,
    "anyConcerns" TEXT,
    "furtherTraining" TEXT,
    "appraisalMeeting" TEXT,
    "qualifyingArea" TEXT,
    "mentorNotes" TEXT,
    "furtherAction" TEXT,
    "pointsToNote" TEXT,
    "signatureKey" TEXT,
    "signatureName" TEXT,
    "printName" TEXT,
    "status" "FeedbackFormStatus" NOT NULL DEFAULT 'DRAFT',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "FeedbackForm_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MonthlyReview" (
    "id" TEXT NOT NULL,
    "documentName" TEXT,
    "completionDate" TIMESTAMP(3) NOT NULL,
    "action" TEXT,
    "s3Key" TEXT,
    "documentType" "DocumentType" NOT NULL DEFAULT 'MONTHLY_REVIEW',
    "referredToTP" BOOLEAN NOT NULL DEFAULT false,
    "candidateId" TEXT NOT NULL,
    "mentorId" TEXT,
    "meetingId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MonthlyReview_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Account_provider_providerAccountId_key" ON "Account"("provider", "providerAccountId");

-- CreateIndex
CREATE UNIQUE INDEX "Session_sessionToken_key" ON "Session"("sessionToken");

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "VerificationToken_token_key" ON "VerificationToken"("token");

-- CreateIndex
CREATE UNIQUE INDEX "VerificationToken_identifier_token_key" ON "VerificationToken"("identifier", "token");

-- CreateIndex
CREATE UNIQUE INDEX "TraineeSkill_traineeId_subSkillId_key" ON "TraineeSkill"("traineeId", "subSkillId");

-- CreateIndex
CREATE UNIQUE INDEX "CharacterSuitabilityCompletion_userId_characterSuitabilityI_key" ON "CharacterSuitabilityCompletion"("userId", "characterSuitabilityId");

-- CreateIndex
CREATE UNIQUE INDEX "FeedbackForm_monthlyReviewId_key" ON "FeedbackForm"("monthlyReviewId");

-- CreateIndex
CREATE UNIQUE INDEX "MonthlyReview_meetingId_key" ON "MonthlyReview"("meetingId");

-- AddForeignKey
ALTER TABLE "Account" ADD CONSTRAINT "Account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Session" ADD CONSTRAINT "Session_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_mentorId_fkey" FOREIGN KEY ("mentorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ClientSupervisor" ADD CONSTRAINT "ClientSupervisor_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "Client"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ClientSupervisor" ADD CONSTRAINT "ClientSupervisor_supervisorId_fkey" FOREIGN KEY ("supervisorId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Placement" ADD CONSTRAINT "Placement_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "Client"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Placement" ADD CONSTRAINT "Placement_mentorId_fkey" FOREIGN KEY ("mentorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Placement" ADD CONSTRAINT "Placement_supervisorId_fkey" FOREIGN KEY ("supervisorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Placement" ADD CONSTRAINT "Placement_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PracticeSkill" ADD CONSTRAINT "PracticeSkill_practiceAreaId_fkey" FOREIGN KEY ("practiceAreaId") REFERENCES "PracticeArea"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PracticeSkillGroup" ADD CONSTRAINT "PracticeSkillGroup_practiceSkillId_fkey" FOREIGN KEY ("practiceSkillId") REFERENCES "PracticeSkill"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PracticeSubSkill" ADD CONSTRAINT "PracticeSubSkill_practiceSkillGroupId_fkey" FOREIGN KEY ("practiceSkillGroupId") REFERENCES "PracticeSkillGroup"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntrySubSkill" ADD CONSTRAINT "EntrySubSkill_entryId_fkey" FOREIGN KEY ("entryId") REFERENCES "Entry"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntrySubSkill" ADD CONSTRAINT "EntrySubSkill_subSkillId_fkey" FOREIGN KEY ("subSkillId") REFERENCES "PracticeSubSkill"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Entry" ADD CONSTRAINT "Entry_placementId_fkey" FOREIGN KEY ("placementId") REFERENCES "Placement"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Entry" ADD CONSTRAINT "Entry_reviewerId_fkey" FOREIGN KEY ("reviewerId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Entry" ADD CONSTRAINT "Entry_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Submission" ADD CONSTRAINT "Submission_reviewerId_fkey" FOREIGN KEY ("reviewerId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Submission" ADD CONSTRAINT "Submission_supervisorId_fkey" FOREIGN KEY ("supervisorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Submission" ADD CONSTRAINT "Submission_traineeId_fkey" FOREIGN KEY ("traineeId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TraineeSkill" ADD CONSTRAINT "TraineeSkill_subSkillId_fkey" FOREIGN KEY ("subSkillId") REFERENCES "PracticeSubSkill"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TraineeSkill" ADD CONSTRAINT "TraineeSkill_traineeId_fkey" FOREIGN KEY ("traineeId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Qualification" ADD CONSTRAINT "Qualification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CharacterSuitabilityCompletion" ADD CONSTRAINT "CharacterSuitabilityCompletion_characterSuitabilityId_fkey" FOREIGN KEY ("characterSuitabilityId") REFERENCES "CharacterSuitability"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CharacterSuitabilityCompletion" ADD CONSTRAINT "CharacterSuitabilityCompletion_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MentorAvailability" ADD CONSTRAINT "MentorAvailability_mentorId_fkey" FOREIGN KEY ("mentorId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Meeting" ADD CONSTRAINT "Meeting_availabilityId_fkey" FOREIGN KEY ("availabilityId") REFERENCES "MentorAvailability"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Meeting" ADD CONSTRAINT "Meeting_candidateId_fkey" FOREIGN KEY ("candidateId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Meeting" ADD CONSTRAINT "Meeting_mentorId_fkey" FOREIGN KEY ("mentorId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FeedbackForm" ADD CONSTRAINT "FeedbackForm_monthlyReviewId_fkey" FOREIGN KEY ("monthlyReviewId") REFERENCES "MonthlyReview"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MonthlyReview" ADD CONSTRAINT "MonthlyReview_candidateId_fkey" FOREIGN KEY ("candidateId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MonthlyReview" ADD CONSTRAINT "MonthlyReview_meetingId_fkey" FOREIGN KEY ("meetingId") REFERENCES "Meeting"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MonthlyReview" ADD CONSTRAINT "MonthlyReview_mentorId_fkey" FOREIGN KEY ("mentorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

