// eslint-disable-next-line @typescript-eslint/no-require-imports
const { PrismaClient } = require('../src/generated/prisma');
// eslint-disable-next-line @typescript-eslint/no-require-imports
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  try {
    // Hash the password
    const hashedPassword = await bcrypt.hash('admin123', 10);

    // Create admin user
    const admin = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Admin User',
        password: hashedPassword,
        role: 'ADMIN',
      },
    });

    // Create mentor user
    const mentor = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: '<PERSON><PERSON> User',
        password: hashedPassword,
        role: 'MENTOR',
      },
    });

    // Create supervisor user
    const supervisor = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Supervisor User',
        password: hashedPassword,
        role: 'SUPERVISOR',
      },
    });

    // Create TC trainee user
    const trainee = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'TC Trainee User',
        password: hashedPassword,
        role: 'TRAINEE',
        traineeLevel: 1,
        qualificationRoute: 'TC', // TC or SQE
        mentorId: mentor.id,
      },
    });

    // Create SQE trainee user
    const sqeTrainee = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'SQE Trainee User',
        password: hashedPassword,
        role: 'TRAINEE',
        traineeLevel: 1,
        qualificationRoute: 'SQE', // TC or SQE
        mentorId: mentor.id,
      },
    });

    // Create client
    const client = await prisma.client.create({
      data: {
        name: 'Example Client',
        email: '<EMAIL>',
        phone: '************',
        address: '123 Main St, City, Country',
      },
    });

    // Create client-supervisor relationship
    await prisma.clientSupervisor.create({
      data: {
        clientId: client.id,
        supervisorId: supervisor.id,
      },
    });

    // Create placement
    const placement = await prisma.placement.create({
      data: {
        name: 'Software Development Internship',
        startDate: new Date('2023-01-01'),
        endDate: new Date('2023-12-31'),
        isFullTime: true,
        supervisorName: supervisor.name || 'Supervisor User',
        supervisorEmail: supervisor.email || '<EMAIL>',
        orgPosition: 'Junior Developer',
        orgSraNumber: 'SRA12345',
        userId: trainee.id,
        clientId: client.id,
        supervisorId: supervisor.id,
        mentorId: mentor.id,
      },
    });

    console.log({
      admin,
      mentor,
      supervisor,
      trainee,
      sqeTrainee,
      client,
      placement,
    });

    console.log('Seed data created successfully with full flow for testing');
  } catch (error) {
    console.error('Error during seeding:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });