-- CreateExtension
CREATE EXTENSION IF NOT EXISTS "plpgsql";

-- Add<PERSON><PERSON><PERSON><PERSON>okenFields
DO $$ 
BEGIN
    -- Check and add resetToken column
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'User' 
        AND column_name = 'resetToken'
    ) THEN
        ALTER TABLE "User" ADD COLUMN "resetToken" TEXT;
        -- Add unique constraint only if column was just created
        ALTER TABLE "User" ADD CONSTRAINT "User_resetToken_key" UNIQUE ("resetToken");
    END IF;

    -- Check and add resetTokenExpiry column
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'User' 
        AND column_name = 'resetTokenExpiry'
    ) THEN
        ALTER TABLE "User" ADD COLUMN "resetTokenExpiry" TIMESTAMP(3);
    END IF;
END $$; 