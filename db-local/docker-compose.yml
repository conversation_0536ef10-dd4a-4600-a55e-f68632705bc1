version: '3.7'
services:
  api_db:
    image: postgres:15
    container_name: pathway21-db
    ports:
      - '54778:5432'
    environment:
      POSTGRES_USER: pathway21
      POSTGRES_PASSWORD: pathway21
      POSTGRES_DB: pathway21

  # backend:
  #   build:
  #     context: backend-server
  #     dockerfile: 'Dockerfile'
  #   container_name: quizza-backend
  #   command: /bin/sh -c "while sleep 1000; do :; done"
  #   ports:
  #     - '3000:3000'
  #   depends_on:
  #     - api_db
  #   volumes:
  #     - ./:/app
  #     - ./backend-server/node_modules:/app/backend-server/node_modules
