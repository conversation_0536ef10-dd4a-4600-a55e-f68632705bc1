'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { UserRole } from '@/types';

export const useAnalytics = () => {
  const { data: session } = useSession();
  const isTrainee = session?.user?.role === UserRole.TRAINEE;
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Check if analytics is loaded
    if (isTrainee && window.pathwaysAnalytics) {
      setIsLoaded(true);
    }

    // Poll for analytics to be loaded (in case component mounts before analytics is ready)
    const checkInterval = setInterval(() => {
      if (window.pathwaysAnalytics) {
        setIsLoaded(true);
        clearInterval(checkInterval);
      }
    }, 500);

    return () => {
      clearInterval(checkInterval);
    };
  }, [isTrainee]);

  // Return a no-op function if analytics is not available
  const noOp = () => {};

  return {
    isEnabled: isTrainee && isLoaded,
    trackPageView: isTrainee && isLoaded && window.pathwaysAnalytics 
      ? window.pathwaysAnalytics.trackPageView 
      : noOp,
    trackButtonClick: isTrainee && isLoaded && window.pathwaysAnalytics 
      ? window.pathwaysAnalytics.trackButtonClick 
      : noOp,
    trackMenuClick: isTrainee && isLoaded && window.pathwaysAnalytics 
      ? window.pathwaysAnalytics.trackMenuClick 
      : noOp,
    trackCtaClick: isTrainee && isLoaded && window.pathwaysAnalytics 
      ? window.pathwaysAnalytics.trackCtaClick 
      : noOp,
    trackTextSelection: isTrainee && isLoaded && window.pathwaysAnalytics 
      ? window.pathwaysAnalytics.trackTextSelection 
      : noOp,
    trackTitleSelection: isTrainee && isLoaded && window.pathwaysAnalytics 
      ? window.pathwaysAnalytics.trackTitleSelection 
      : noOp,
  };
};
