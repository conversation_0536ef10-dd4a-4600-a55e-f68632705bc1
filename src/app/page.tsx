'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { UserRole } from '@/types';

export default function Home() {
  const router = useRouter();
  const { data: session } = useSession();

  useEffect(() => {
    const role = session?.user?.role;
    let redirectUrl = '/auth/signin';

    switch (role) {
      case UserRole.ADMIN:
        redirectUrl = '/admin/dashboard';
        break;
      case UserRole.SUPERVISOR:
        redirectUrl = '/supervisor/trainees';
        break;
      case UserRole.MENTOR:
        redirectUrl = '/mentor/mentees';
        break;
      case UserRole.TRAINEE:
        redirectUrl = '/trainee/dashboard';
        break;
    }

    router.push(redirectUrl);
  }, [router, session]);

  return null;
}
