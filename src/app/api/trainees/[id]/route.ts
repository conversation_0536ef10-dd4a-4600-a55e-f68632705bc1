import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/db';
import { UserRole } from '@/types';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession();
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supervisor = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!supervisor || supervisor.role !== UserRole.SUPERVISOR) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const trainee = await prisma.user.findFirst({
      where: {
        id: id,
        role: UserRole.TRAINEE,
        placements: {
          some: {
            supervisorId: supervisor.id,
          },
        },
      },
      select: {
        id: true,
        name: true,
        email: true,
      },
    });

    if (!trainee) {
      return NextResponse.json({ error: 'Trainee not found' }, { status: 404 });
    }

    return NextResponse.json(trainee);
  } catch (error) {
    console.error('Error fetching trainee:', error);
    return NextResponse.json(
      { error: 'Failed to fetch trainee' },
      { status: 500 }
    );
  }
} 