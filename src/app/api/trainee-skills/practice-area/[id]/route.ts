import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }>; }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: userId, role } = session.user;
    const practiceAreaId = id;
    
    // Get traineeId (if mentor/supervisor viewing trainee's progress)
    let traineeId = userId;
    if (role !== 'TRAINEE') {
      const { searchParams } = new URL(request.url);
      const queryTraineeId = searchParams.get('traineeId');
      if (!queryTraineeId) {
        return NextResponse.json({ error: 'Trainee ID is required' }, { status: 400 });
      }
      traineeId = queryTraineeId;
    }

    // Get practice area with its full hierarchy
    const practiceArea = await prisma.practiceArea.findUnique({
      where: { id: practiceAreaId },
      include: {
        practiceSkills: {
          include: {
            practiceSkillGroups: {
              include: {
                practiceSubSkills: true
              }
            }
          }
        }
      }
    });

    if (!practiceArea) {
      return NextResponse.json({ error: 'Practice area not found' }, { status: 404 });
    }

    // Get all subskill IDs for this practice area
    const subSkillIds = practiceArea.practiceSkills.flatMap(skill => 
      skill.practiceSkillGroups.flatMap(group => 
        group.practiceSubSkills.map(subSkill => subSkill.id)
      )
    );

    // Get trainee skills for all subskills in this practice area
    const traineeSkills = await prisma.traineeSkill.findMany({
      where: {
        traineeId,
        subSkillId: {
          in: subSkillIds
        }
      },
      include: {
        subSkill: true
      }
    });

    // Get trainee information
    const trainee = await prisma.user.findUnique({
      where: { id: traineeId },
      select: {
        name: true,
        email: true,
        qualificationRoute: true
      }
    });

    // Calculate overall progress
    const totalSubSkills = subSkillIds.length;
    const completedSkills = traineeSkills.filter(
      ts => ts.doneEntryCount >= ts.minSuggestedEntryCount
    ).length;
    
    const areaProgress = totalSubSkills > 0 ? (completedSkills / totalSubSkills) * 100 : 0;

    // Format the response with the full hierarchy and progress
    const formattedSkills = practiceArea.practiceSkills.map(skill => {
      const groups = skill.practiceSkillGroups.map(group => {
        const subSkills = group.practiceSubSkills.map(subSkill => {
          const traineeSkill = traineeSkills.find(ts => ts.subSkillId === subSkill.id) || {
            doneEntryCount: 0,
            minSuggestedEntryCount: subSkill.minSuggestedEntryCount
          };

          return {
            id: subSkill.id,
            name: subSkill.name,
            doneEntryCount: traineeSkill.doneEntryCount,
            minSuggestedEntryCount: traineeSkill.minSuggestedEntryCount,
            progress: Math.min((traineeSkill.doneEntryCount / traineeSkill.minSuggestedEntryCount) * 100, 100),
            isCompleted: traineeSkill.doneEntryCount >= traineeSkill.minSuggestedEntryCount,
            order: subSkill.order
          };
        });

        const groupProgress = subSkills.reduce((sum, skill) => sum + skill.progress, 0) / subSkills.length;

        return {
          id: group.id,
          name: group.name,
          subSkills,
          progress: groupProgress,
          isCompleted: subSkills.every(skill => skill.isCompleted)
        };
      });

      const skillProgress = groups.reduce((sum, group) => sum + group.progress, 0) / groups.length;

      return {
        id: skill.id,
        name: skill.name,
        groups,
        progress: skillProgress,
        isCompleted: groups.every(group => group.isCompleted)
      };
    });

    return NextResponse.json({
      trainee,
      practiceArea: {
        id: practiceArea.id,
        name: practiceArea.name
      },
      progress: areaProgress,
      totalSubSkills,
      completedSkills,
      skills: formattedSkills
    });

  } catch (error) {
    console.error('Error fetching practice area progress:', error);
    return NextResponse.json({ error: 'Failed to fetch practice area progress' }, { status: 500 });
  }
}
