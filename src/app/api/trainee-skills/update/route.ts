import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function PUT(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { entryId } = await request.json();
    
    // Get entry information
    const entry = await prisma.entry.findUnique({
      where: { id: entryId },
      include: {
        entrySubSkills: {
          include: {
            subSkill: true
          }
        },
        placement: {
          select: {
            userId: true
          }
        }
      }
    });

    if (!entry) {
      return NextResponse.json({ error: 'Entry not found' }, { status: 404 });
    }

    // Only update if entry is approved
    if (entry.status !== 'signedoff') {
      return NextResponse.json({ error: 'Entry is not approved' }, { status: 400 });
    }

    const traineeId = entry.placement.userId;
    
    // Update TraineeSkill for each sub-skill in the entry
    const updatePromises = entry.entrySubSkills.map(({ subSkill }) =>
      prisma.traineeSkill.upsert({
        where: {
          traineeId_subSkillId: {
            traineeId,
            subSkillId: subSkill.id
          }
        },
        update: {
          doneEntryCount: {
            increment: 1
          },
          updatedAt: new Date()
        },
        create: {
          traineeId,
          subSkillId: subSkill.id,
          doneEntryCount: 1,
          minSuggestedEntryCount: subSkill.minSuggestedEntryCount
        }
      })
    );
    
    const updatedSkills = await Promise.all(updatePromises);

    return NextResponse.json({ 
      success: true,
      updatedSkills: updatedSkills.length
    });
  } catch (error) {
    console.error('Error updating trainee skills:', error);
    return NextResponse.json({ error: 'Failed to update trainee skills' }, { status: 500 });
  }
}
