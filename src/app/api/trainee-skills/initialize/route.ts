import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function POST() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: userId, role } = session.user;
    
    // Only allow TRAINEE role
    if (role !== 'TRAINEE') {
      return NextResponse.json({ error: 'Only trainees can have skill progress tracking' }, { status: 403 });
    }

    // Check if trainee already has skill records
    const existingSkills = await prisma.traineeSkill.findFirst({
      where: { traineeId: userId }
    });

    if (existingSkills) {
      return NextResponse.json({ error: 'Skill progress tracking already exists' }, { status: 400 });
    }

    // Get all sub-skills
    const subSkills = await prisma.practiceSubSkill.findMany({
      include: {
        practiceSkillGroup: {
          include: {
            practiceSkill: {
              include: {
                practiceArea: true
              }
            }
          }
        }
      }
    });

    // Create TraineeSkill records for each sub-skill
    const traineeSkills = await Promise.all(
      subSkills.map(subSkill => 
        prisma.traineeSkill.create({
          data: {
            traineeId: userId,
            subSkillId: subSkill.id,
            doneEntryCount: 0,
            minSuggestedEntryCount: subSkill.minSuggestedEntryCount
          }
        })
      )
    );

    return NextResponse.json({ 
      success: true, 
      count: traineeSkills.length 
    });
  } catch (error) {
    console.error('Error initializing trainee skills:', error);
    return NextResponse.json({ error: 'Failed to initialize trainee skills' }, { status: 500 });
  }
}
