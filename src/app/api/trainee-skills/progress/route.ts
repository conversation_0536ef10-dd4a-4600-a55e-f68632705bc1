import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: userId, role } = session.user;
    
    // Get traineeId (if mentor/supervisor viewing trainee's progress)
    let traineeId = userId;
    if (role !== 'TRAINEE') {
      const { searchParams } = new URL(request.url);
      const queryTraineeId = searchParams.get('traineeId');
      if (!queryTraineeId) {
        return NextResponse.json({ error: 'Trainee ID is required' }, { status: 400 });
      }
      traineeId = queryTraineeId;
    }

    // Get trainee information
    const trainee = await prisma.user.findUnique({
      where: { id: traineeId },
      select: {
        name: true,
        email: true,
        qualificationRoute: true
      }
    });

    // Get practice areas with full hierarchy
    const practiceAreas = await prisma.practiceArea.findMany({
      include: {
        practiceSkills: {
          include: {
            practiceSkillGroups: {
              include: {
                practiceSubSkills: true
              }
            }
          }
        }
      }
    });

    // Get trainee skills
    const traineeSkills = await prisma.traineeSkill.findMany({
      where: { traineeId },
      include: {
        subSkill: {
          include: {
            practiceSkillGroup: {
              include: {
                practiceSkill: {
                  include: {
                    practiceArea: true
                  }
                }
              }
            }
          }
        }
      }
    });

    // If no skills found, return 404 with initialization flag
    if (traineeSkills.length === 0) {
      return NextResponse.json({ 
        error: 'No skill progress found',
        needsInitialization: true 
      }, { status: 404 });
    }

    // Calculate overall progress
    const totalSkills = traineeSkills.length;
    const completedSkills = traineeSkills.filter(
      ts => ts.doneEntryCount >= ts.minSuggestedEntryCount
    ).length;
    
    const overallProgress = totalSkills > 0 ? (completedSkills / totalSkills) * 100 : 0;

    // Format practice areas with progress
    const formattedAreas = practiceAreas.map(area => {
      const areaSkills = area.practiceSkills.map(skill => {
        const groups = skill.practiceSkillGroups.map(group => {
          const subSkills = group.practiceSubSkills.map(subSkill => {
            const traineeSkill = traineeSkills.find(ts => ts.subSkillId === subSkill.id) || {
              doneEntryCount: 0,
              minSuggestedEntryCount: subSkill.minSuggestedEntryCount
            };

            return {
              id: subSkill.id,
              name: subSkill.name,
              doneEntryCount: traineeSkill.doneEntryCount,
              minSuggestedEntryCount: traineeSkill.minSuggestedEntryCount,
              progress: Math.min((traineeSkill.doneEntryCount / traineeSkill.minSuggestedEntryCount) * 100, 100),
              isCompleted: traineeSkill.doneEntryCount >= traineeSkill.minSuggestedEntryCount
            };
          });

          const groupProgress = subSkills.reduce((sum, skill) => sum + skill.progress, 0) / subSkills.length;

          return {
            id: group.id,
            name: group.name,
            subSkills,
            progress: groupProgress,
            isCompleted: subSkills.every(skill => skill.isCompleted)
          };
        });

        const skillProgress = groups.reduce((sum, group) => sum + group.progress, 0) / groups.length;

        return {
          id: skill.id,
          name: skill.name,
          groups,
          progress: skillProgress,
          isCompleted: groups.every(group => group.isCompleted)
        };
      });

      const areaProgress = areaSkills.reduce((sum, skill) => sum + skill.progress, 0) / areaSkills.length;
      const areaCompletedSkills = areaSkills.filter(skill => skill.isCompleted).length;

      return {
        id: area.id,
        name: area.name,
        skills: areaSkills,
        progress: areaProgress,
        totalSkills: areaSkills.length,
        completedSkills: areaCompletedSkills
      };
    });

    return NextResponse.json({
      trainee,
      overallProgress,
      totalSkills,
      completedSkills,
      practiceAreas: formattedAreas
    });
  } catch (error) {
    console.error('Error fetching trainee progress:', error);
    return NextResponse.json({ error: 'Failed to fetch trainee progress' }, { status: 500 });
  }
}
