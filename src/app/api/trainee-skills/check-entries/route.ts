import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { UserRole } from '@/types';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Only trainees can check their entries
    if (session.user.role !== UserRole.TRAINEE) {
      return new NextResponse('Forbidden', { status: 403 });
    }

    // Get user's trainee skills with their entry counts
    const traineeSkills = await prisma.traineeSkill.findMany({
      where: {
        traineeId: session.user.id,
      },
      select: {
        doneEntryCount: true
      }
    });

    // Check if any skill has completed entries
    const hasCompletedEntries = traineeSkills.some(skill => 
      skill.doneEntryCount > 0
    );

    return NextResponse.json({ hasCompletedEntries });
  } catch (error) {
    console.error('Error checking trainee entries:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
