import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { UserRole } from '@/types';

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admin can check progress for other users
    if (session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const traineeId = searchParams.get('traineeId');

    if (!traineeId) {
      return NextResponse.json({ error: 'Missing traineeId parameter' }, { status: 400 });
    }

    // Check if trainee exists and is actually a trainee
    const trainee = await prisma.user.findUnique({
      where: { id: traineeId },
      select: { role: true }
    });

    if (!trainee) {
      return NextResponse.json({ error: 'Trainee not found' }, { status: 404 });
    }

    if (trainee.role !== 'TRAINEE') {
      return NextResponse.json({ error: 'User is not a trainee' }, { status: 400 });
    }

    // Check if trainee has any skills with progress
    const traineeSkills = await prisma.traineeSkill.findMany({
      where: { 
        traineeId,
        doneEntryCount: { gt: 0 } // Only find skills with progress
      }
    });

    const hasProgress = traineeSkills.length > 0;

    return NextResponse.json({
      hasProgress,
      progressCount: traineeSkills.length
    });
  } catch (error) {
    console.error('Error checking trainee progress:', error);
    return NextResponse.json(
      { error: 'Failed to check trainee progress' },
      { status: 500 }
    );
  }
} 