import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";
import { UserRole } from "@/types";
import { sendMentorAvailabilityNotificationWithNotifications } from "@/lib/email";

// Get mentor availability slots
export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== UserRole.MENTOR) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const availability = await prisma.mentorAvailability.findMany({
      where: {
        mentorId: session.user.id,
      },
      orderBy: {
        startTime: 'desc',
      },
    });

    return NextResponse.json(availability);
  } catch (error) {
    console.error("Error fetching availability:", error);
    return NextResponse.json(
      { error: "Failed to fetch availability" },
      { status: 500 }
    );
  }
}

// Create new availability slot
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== UserRole.MENTOR) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { startTime, endTime, meetingMethod, duration, bufferTime, meetingLocation, meetingMessage } = await request.json();

    // Validate input
    if (!startTime || !endTime || !meetingMethod) {
      return NextResponse.json(
        { error: "Start time, end time, and meeting method are required" },
        { status: 400 }
      );
    }
    
    // We're now using the new schema fields directly
    // meetingMethod and meetingLocation

    // Check for overlapping slots
    const overlappingSlot = await prisma.mentorAvailability.findFirst({
      where: {
        mentorId: session.user.id,
        AND: [
          {
            startTime: {
              lte: new Date(endTime),
            },
          },
          {
            endTime: {
              gte: new Date(startTime),
            },
          },
        ],
      },
    });

    if (overlappingSlot) {
      return NextResponse.json(
        { error: "Time slot overlaps with existing availability" },
        { status: 400 }
      );
    }

    // Get mentor's trainees
    const trainees = await prisma.user.findMany({
      where: {
        mentorId: session.user.id,
        role: UserRole.TRAINEE,
      },
      select: {
        name: true,
        email: true,
      },
    });

    const availability = await prisma.mentorAvailability.create({
      data: {
        mentorId: session.user.id,
        startTime: new Date(startTime),
        endTime: new Date(endTime),
        meetingMethod: meetingMethod, // This is the new field name in the schema
        isBooked: false,
        duration: duration || 60,
        bufferTime: bufferTime || 10,
        meetingLocation: meetingLocation || null, // This is the new field name in the schema
        meetingMessage: meetingMessage || null,
      },
    });

    // Send notification emails to all trainees
    if (trainees.length > 0) {
      await sendMentorAvailabilityNotificationWithNotifications(
        trainees.map((trainee) => ({
          name: trainee.name || '',
          email: trainee.email || '',
        })),
        `${process.env.NEXTAUTH_URL}/trainee/meetings`
      );
    }

    return NextResponse.json(availability);
  } catch (error) {
    console.error("Error creating availability:", error);
    return NextResponse.json(
      { error: "Failed to create availability" },
      { status: 500 }
    );
  }
}

// Delete availability slot
export async function DELETE(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== UserRole.MENTOR) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { error: "Availability ID is required" },
        { status: 400 }
      );
    }

    // Check if slot is already booked
    const slot = await prisma.mentorAvailability.findUnique({
      where: { id },
      include: { meetings: true },
    });

    if (!slot) {
      return NextResponse.json(
        { error: "Availability slot not found" },
        { status: 404 }
      );
    }

    if (slot.isBooked || slot.meetings.length > 0) {
      return NextResponse.json(
        { error: "Cannot delete a booked availability slot" },
        { status: 400 }
      );
    }

    await prisma.mentorAvailability.delete({
      where: { id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting availability:", error);
    return NextResponse.json(
      { error: "Failed to delete availability" },
      { status: 500 }
    );
  }
} 