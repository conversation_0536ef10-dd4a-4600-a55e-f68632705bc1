import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";
import { UserRole } from "@/types";

export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== UserRole.MENTOR) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { isBooked, meetingMethod, meetingLocation, duration, bufferTime, startTime, endTime } = await request.json();

    const existingAvailability = await prisma.mentorAvailability.findUnique({
      where: { id },
    });

    if (!existingAvailability) {
      return NextResponse.json(
        { error: "Availability not found" },
        { status: 404 }
      );
    }

    if (existingAvailability.mentorId !== session.user.id) {
      return NextResponse.json(
        { error: "Unauthorized to update this availability" },
        { status: 403 }
      );
    }

    const updatedAvailability = await prisma.mentorAvailability.update({
      where: { id },
      data: {
        ...(isBooked !== undefined && { isBooked }),
        ...(meetingMethod && { meetingMethod }),
        ...(meetingLocation !== undefined && { meetingLocation }),
        ...(duration && { duration }),
        ...(bufferTime !== undefined && { bufferTime }),
        ...(startTime && { startTime: new Date(startTime) }),
        ...(endTime && { endTime: new Date(endTime) }),
      },
    });

    return NextResponse.json(updatedAvailability);
  } catch (error) {
    console.error("Error updating availability:", error);
    return NextResponse.json(
      { error: "Failed to update availability" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== UserRole.MENTOR) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const existingAvailability = await prisma.mentorAvailability.findUnique({
      where: { id },
      include: { meetings: true },
    });

    if (!existingAvailability) {
      return NextResponse.json(
        { error: "Availability not found" },
        { status: 404 }
      );
    }

    if (existingAvailability.mentorId !== session.user.id) {
      return NextResponse.json(
        { error: "Unauthorized to delete this availability" },
        { status: 403 }
      );
    }

    if (existingAvailability.isBooked || existingAvailability.meetings.length > 0) {
      return NextResponse.json(
        { error: "Cannot delete a booked availability slot" },
        { status: 400 }
      );
    }

    await prisma.mentorAvailability.delete({
      where: { id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting availability:", error);
    return NextResponse.json(
      { error: "Failed to delete availability" },
      { status: 500 }
    );
  }
} 