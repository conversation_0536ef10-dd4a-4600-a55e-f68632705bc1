import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";
import { UserRole } from "@/types";
import dayjs from "dayjs";
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { sendMentorAvailabilityNotificationWithNotifications } from "@/lib/email";

// Configure dayjs to use timezone
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.tz.setDefault('Europe/London');

// Update availability slots for a specific date
export async function PUT(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== UserRole.MENTOR) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { date, slots } = await request.json();

    if (!date || !Array.isArray(slots)) {
      return NextResponse.json(
        { error: "Date and slots array are required" },
        { status: 400 }
      );
    }

    // Convert date to start and end of day in Europe/London timezone
    const startOfDay = dayjs(date).tz('Europe/London').startOf('day').toDate();
    const endOfDay = dayjs(date).tz('Europe/London').endOf('day').toDate();

    // Get all existing slots for this date
    const existingSlots = await prisma.mentorAvailability.findMany({
      where: {
        mentorId: session.user.id,
        startTime: {
          gte: startOfDay,
          lte: endOfDay,
        },
        isBooked: false, // Only consider unbooked slots
      },
    });

    // Get IDs of slots that should be kept (those with keys in the incoming slots array)
    const slotsToKeep = slots
      .filter(slot => slot.key)
      .map(slot => slot.key);

    // Delete slots that are not in the incoming array
    await prisma.mentorAvailability.deleteMany({
      where: {
        mentorId: session.user.id,
        startTime: {
          gte: startOfDay,
          lte: endOfDay,
        },
        isBooked: false,
        id: {
          notIn: slotsToKeep
        }
      },
    });

    // Create new slots (those without keys)
    const newSlots = slots.filter(slot => !slot.key);
    const createdSlots = await Promise.all(
      newSlots.map(async (slot) => {
        // Check for overlapping slots
        const overlappingSlot = await prisma.mentorAvailability.findFirst({
          where: {
            mentorId: session.user.id,
            AND: [
              {
                startTime: {
                  lte: new Date(slot.endTime),
                },
              },
              {
                endTime: {
                  gte: new Date(slot.startTime),
                },
              },
            ],
          },
        });

        if (overlappingSlot) {
          // Format times for better error message using Europe/London timezone
          const startTimeFormatted = dayjs(slot.startTime).tz('Europe/London').format('HH:mm');
          const endTimeFormatted = dayjs(slot.endTime).tz('Europe/London').format('HH:mm');
          throw new Error(`Time slot overlaps with existing availability: ${startTimeFormatted} - ${endTimeFormatted}`);
        }

        return prisma.mentorAvailability.create({
          data: {
            mentorId: session.user.id,
            startTime: new Date(slot.startTime),
            endTime: new Date(slot.endTime),
            meetingMethod: slot.meetingMethod,
            isBooked: false,
            duration: slot.duration || 60,
            bufferTime: slot.bufferTime || 10,
            meetingLocation: slot.meetingLocation || null,
            meetingMessage: slot.meetingMessage || null,
          },
        });
      })
    );

    // Get mentor's trainees for notification
    const trainees = await prisma.user.findMany({
      where: {
        mentorId: session.user.id,
        role: UserRole.TRAINEE,
      },
      select: {
        name: true,
        email: true,
      },
    });

    // Send notification emails to all trainees if there are new slots
    if (trainees.length > 0 && createdSlots.length > 0) {
      await sendMentorAvailabilityNotificationWithNotifications(
        trainees.map((trainee) => ({
          name: trainee.name || '',
          email: trainee.email || '',
        })),
        `${process.env.NEXTAUTH_URL}`
      );
    }

    return NextResponse.json(createdSlots);
  } catch (error) {
    console.error("Error updating availability:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to update availability" },
      { status: 500 }
    );
  }
} 