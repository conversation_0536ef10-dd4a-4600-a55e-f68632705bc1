import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const mentorId = (await params).id;

    // Check if the user is authorized to view this mentor's availability
    // Either the user is the mentor, or the mentor is assigned to this trainee
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        mentor: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    if (user.id !== mentorId && user.mentorId !== mentorId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const availability = await prisma.mentorAvailability.findMany({
      where: {
        mentorId: mentorId,
      },
      orderBy: {
        startTime: "asc",
      },
    });

    return NextResponse.json(availability);
  } catch (error) {
    console.error("Error fetching mentor availability:", error);
    return NextResponse.json(
      { error: "Failed to fetch availability" },
      { status: 500 }
    );
  }
} 