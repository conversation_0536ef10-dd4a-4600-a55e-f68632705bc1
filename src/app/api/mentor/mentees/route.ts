import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { UserRole } from '@/types';

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Only mentors can access this endpoint
    if (session.user.role !== UserRole.MENTOR) {
      return new NextResponse('Forbidden', { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'all'; // 'current', 'past', or 'all'

    const whereClause: any = { mentorId: session.user.id };
    
    if (type === 'current') {
      whereClause.isActive = true;
    } else if (type === 'past') {
      whereClause.isActive = false;
    }

    // Get mentees from history table
    const mentees = await prisma.mentorMenteeHistory.findMany({
      where: whereClause,
      include: {
        mentee: {
          select: {
            id: true,
            name: true,
            email: true,
            qualificationRoute: true,
            traineeLevel: true,
            updatedAt: true,
            traineeSkills: {
              select: {
                doneEntryCount: true,
                minSuggestedEntryCount: true,
                updatedAt: true
              }
            },
            reviewedEntries: {
              orderBy: {
                updatedAt: 'desc'
              },
              take: 1,
              select: {
                updatedAt: true
              }
            },
            submissions: {
              orderBy: {
                updatedAt: 'desc'
              },
              take: 1,
              select: {
                updatedAt: true
              }
            },
            placements: {
              orderBy: {
                updatedAt: 'desc'
              },
              take: 1,
              select: {
                updatedAt: true
              }
            }
          }
        }
      },
      orderBy: type === 'past' ? { endDate: 'desc' } : { startDate: 'desc' }
    });

    // Calculate competency progress for each mentee
    const menteesWithProgress = mentees.map(history => {
      const mentee = history.mentee;
      const totalDone = mentee.traineeSkills.reduce((sum, skill) => sum + skill.doneEntryCount, 0);
      const totalRequired = mentee.traineeSkills.reduce((sum, skill) => sum + skill.minSuggestedEntryCount, 0);
      const competencyProgress = totalRequired > 0 
        ? Math.round((totalDone / totalRequired) * 100)
        : 0;

      // Get the latest activity time by comparing all activity timestamps
      const activityTimes = [
        mentee.updatedAt,
        ...mentee.traineeSkills.map(skill => skill.updatedAt),
        mentee.submissions[0]?.updatedAt,
        mentee.reviewedEntries[0]?.updatedAt,
        mentee.placements[0]?.updatedAt,
      ].filter(Boolean);

      const lastActive = activityTimes.length > 0
        ? new Date(Math.max(...activityTimes.map(date => date.getTime())))
        : mentee.updatedAt;

      return {
        id: mentee.id,
        name: mentee.name || 'Unknown',
        email: mentee.email || '',
        qualificationRoute: mentee.qualificationRoute,
        traineeLevel: mentee.traineeLevel,
        lastActive,
        competencyProgress,
        relationshipStartDate: history.startDate,
        relationshipEndDate: history.endDate,
        isActive: history.isActive,
      };
    });

    if (type === 'all') {
      const current = menteesWithProgress.filter(mentee => mentee.isActive);
      const past = menteesWithProgress.filter(mentee => !mentee.isActive);
      return NextResponse.json({ current, past });
    }

    return NextResponse.json(menteesWithProgress);
  } catch (error) {
    console.error('Error fetching mentees:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
