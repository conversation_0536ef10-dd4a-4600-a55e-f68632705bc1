import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { UserRole } from '@/types';

export async function GET() {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only mentors can access this endpoint
    if (session.user.role !== UserRole.MENTOR) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get submissions sent specifically to this mentor
    const submissions = await prisma.submission.findMany({
      where: {
        AND: [
          // Only submissions sent to this mentor as reviewer
          { reviewerId: session.user.id },
          // Only from current mentees
          {
            trainee: {
              mentorMenteeHistory: {
                some: {
                  mentorId: session.user.id,
                  isActive: true
                }
              }
            }
          }
        ]
      },
      include: {
        trainee: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        entries: {
          select: {
            id: true,
            title: true,
            startDate: true,
            endDate: true,
            status: true,
            documentKey: true,
            documentName: true,
            feedback: true,
            entrySubSkills: {
              include: {
                subSkill: true,
              },
            },
          },
        },
        entrySubmissions: {
          include: {
            entry: {
              select: {
                id: true,
                title: true,
                startDate: true,
                endDate: true,
                status: true,
                documentKey: true,
                documentName: true,
                feedback: true,
                entrySubSkills: {
                  include: {
                    subSkill: true,
                  },
                },
              },
            },
          },
        },
        placement: {
          select: {
            id: true,
            name: true,
            client: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return NextResponse.json(submissions);
  } catch (error) {
    console.error('Error fetching mentor submissions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch submissions' },
      { status: 500 }
    );
  }
}
