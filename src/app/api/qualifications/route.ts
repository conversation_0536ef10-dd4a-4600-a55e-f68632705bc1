import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth';
import { UserRole } from '@/generated/prisma/index';

// Get all qualifications for the current user or specific user (admin only)
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    let targetUserId = session.user.id;

    if (userId && userId !== session.user.id) {
      if (session.user.role !== UserRole.ADMIN) {
        return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
      }
      targetUserId = userId;
    }

    const qualifications = await prisma.qualification.findMany({
      where: { userId: targetUserId },
      orderBy: { createdAt: 'desc' },
      include: userId ? {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      } : undefined
    });

    return NextResponse.json(qualifications);
  } catch (error) {
    console.error('Failed to fetch qualifications:', error);
    return NextResponse.json(
      { error: 'Failed to fetch qualifications' },
      { status: 500 }
    );
  }
}

// Create a new qualification
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { type, name, fileUrl, fileName, startDate, endDate, grade } = body;

    if (!type || !name) {
      return NextResponse.json(
        { error: 'Type and name are required' },
        { status: 400 }
      );
    }

    const qualification = await prisma.qualification.create({
      data: {
        type,
        name,
        fileUrl,
        fileName,
        startDate: startDate ? new Date(startDate) : null,
        endDate: endDate ? new Date(endDate) : null,
        grade,
        userId: session.user.id
      }
    });

    return NextResponse.json(qualification, { status: 201 });
  } catch (error) {
    console.error('Failed to create qualification:', error);
    return NextResponse.json(
      { error: 'Failed to create qualification' },
      { status: 500 }
    );
  }
} 