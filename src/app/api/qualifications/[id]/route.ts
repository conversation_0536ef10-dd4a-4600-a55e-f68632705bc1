import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth';

// Get a specific qualification
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const qualification = await prisma.qualification.findUnique({
      where: { 
        id,
        userId: session.user.id 
      }
    });

    if (!qualification) {
      return NextResponse.json(
        { error: 'Qualification not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(qualification);
  } catch (error) {
    console.error('Failed to fetch qualification:', error);
    return NextResponse.json(
      { error: 'Failed to fetch qualification' },
      { status: 500 }
    );
  }
}

// Update a qualification
export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { type, name, fileUrl, fileName, startDate, endDate, grade } = body;

    // Verify ownership
    const existingQualification = await prisma.qualification.findUnique({
      where: { 
        id,
        userId: session.user.id 
      }
    });

    if (!existingQualification) {
      return NextResponse.json(
        { error: 'Qualification not found' },
        { status: 404 }
      );
    }

    const qualification = await prisma.qualification.update({
      where: { id: id },
      data: {
        type,
        name,
        fileUrl,
        fileName,
        startDate: startDate ? new Date(startDate) : null,
        endDate: endDate ? new Date(endDate) : null,
        grade
      }
    });

    return NextResponse.json(qualification);
  } catch (error) {
    console.error('Failed to update qualification:', error);
    return NextResponse.json(
      { error: 'Failed to update qualification' },
      { status: 500 }
    );
  }
}

// Delete a qualification
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const { id } = await params;
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify ownership
    const existingQualification = await prisma.qualification.findUnique({
      where: { 
        id,
        userId: session.user.id 
      }
    });

    if (!existingQualification) {
      return NextResponse.json(
        { error: 'Qualification not found' },
        { status: 404 }
      );
    }

    await prisma.qualification.delete({
      where: { id: id }
    });

    return NextResponse.json(
      { message: 'Qualification deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Failed to delete qualification:', error);
    return NextResponse.json(
      { error: 'Failed to delete qualification' },
      { status: 500 }
    );
  }
} 