import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { UserRole } from '@/types';

export async function PATCH(
  request: Request,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session || session.user.role !== UserRole.MENTOR) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const params = await context.params;
    const { meetingId } = await request.json();

    if (!meetingId) {
      return NextResponse.json(
        { error: 'meetingId is required' },
        { status: 400 }
      );
    }

    // Check if MonthlyReview exists and belongs to the mentor
    const monthlyReview = await prisma.monthlyReview.findUnique({
      where: { id: params.id },
      include: {
        feedbackForm: true
      }
    });

    if (!monthlyReview) {
      return NextResponse.json(
        { error: 'Monthly review not found' },
        { status: 404 }
      );
    }

    if (monthlyReview.mentorId !== session.user.id) {
      return NextResponse.json(
        { error: 'Unauthorized access to this monthly review' },
        { status: 403 }
      );
    }

    // Check if meeting exists
    const meeting = await prisma.meeting.findUnique({
      where: { id: meetingId }
    });

    if (!meeting) {
      return NextResponse.json(
        { error: 'Meeting not found' },
        { status: 404 }
      );
    }

    // Update MonthlyReview with meetingId
    const updatedMonthlyReview = await prisma.monthlyReview.update({
      where: { id: params.id },
      data: {
        meetingId: meetingId
      },
      include: {
        feedbackForm: true
      }
    });

    return NextResponse.json({
      success: true,
      monthlyReview: updatedMonthlyReview
    });

  } catch (error) {
    console.error('Error linking meeting to monthly review:', error);
    return NextResponse.json(
      { error: 'Failed to link meeting to monthly review' },
      { status: 500 }
    );
  }
} 