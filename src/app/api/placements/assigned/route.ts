import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user?.email || '' },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    let placements;
    if (user.role === 'SUPERVISOR') {
      placements = await prisma.placement.findMany({
        where: { supervisorId: user.id },
        include: {
          client: true,
          user: true,
          supervisor: true,
          mentor: true,
        },
        orderBy: { createdAt: 'desc' },
      });
    } else if (user.role === 'MENTOR') {
      placements = await prisma.placement.findMany({
        where: { mentorId: user.id },
        include: {
          client: true,
          user: true,
          supervisor: true,
          mentor: true,
        },
        orderBy: { createdAt: 'desc' },
      });
    } else {
      return NextResponse.json(
        { error: 'Only supervisors and mentors can view assigned placements' },
        { status: 403 }
      );
    }

    return NextResponse.json(placements);
  } catch (error) {
    console.error('Error fetching assigned placements:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 