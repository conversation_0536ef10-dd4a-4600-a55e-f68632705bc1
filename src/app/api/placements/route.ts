import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { UserRole } from '@/types';
import { generatePassword } from '@/lib/utils';
import { sendSupervisorWelcomeEmailWithNotification } from '@/lib/email';
import bcrypt from 'bcryptjs';

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const offset = parseInt(searchParams.get('offset') || '0');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const whereClause: any = {};
    
    if (session.user.role === UserRole.TRAINEE) {
      whereClause.userId = session.user.id;
    } else if (session.user.role === UserRole.SUPERVISOR) {
      whereClause.supervisorId = session.user.id;
    } else if (session.user.role === UserRole.MENTOR) {
      whereClause.mentorId = session.user.id;
    }

    if (search) {
      whereClause.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { client: { name: { contains: search, mode: 'insensitive' } } },
        { user: { name: { contains: search, mode: 'insensitive' } } },
      ];
    }

    const [total, placements] = await Promise.all([
      prisma.placement.count({ where: whereClause }),
      prisma.placement.findMany({
        where: whereClause,
        include: {
          client: true,
          user: true,
          supervisor: true,
          mentor: true,
        },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
      }),
    ]);

    return NextResponse.json({
      placements: {
        edges: placements,
        pageInfo: {
          total,
          offset,
          limit,
        },
      },
    });
  } catch (error) {
    console.error('Error fetching placements:', error);
    return NextResponse.json(
      { error: 'Failed to fetch placements' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();

    if (session.user.role === UserRole.TRAINEE) {
      data.userId = session.user.id;
    }

    const { 
      orgName,
      orgSraNumber,
      startDate,
      endDate,
      isFullTime,
      supervisorName,
      supervisorEmail,
      qweConfirmingPerson, 
      qweSraNumber, 
      qweEmail,
      documentKey,
      documentName,
      userId
    } = data;

    if (!orgName || !startDate || !supervisorName || !supervisorEmail) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const existingSupervisor = await prisma.user.findUnique({
      where: { email: supervisorEmail },
      include: {
        supervisedClients: {
          include: {
            client: true
          }
        }
      }
    });

    const existingClient = await prisma.client.findFirst({
      where: { name: orgName }
    });

    // Case 1: Supervisor exists + Client name matches
    if (existingSupervisor && existingClient && existingSupervisor.supervisedClients.some(sc => sc.client.id === existingClient.id)) {
      const placement = await prisma.placement.create({
        data: {
          name: orgName,
          orgSraNumber,
          startDate,
          endDate,
          isFullTime,
          supervisorName,
          supervisorEmail,
          qweConfirmingPerson,
          qweSraNumber,
          qweEmail,
          documentKey,
          documentName,
          user: { connect: { id: userId } },
          client: { connect: { id: existingClient.id } },
          supervisor: { connect: { id: existingSupervisor.id } }
        },
        include: {
          client: true,
          user: true,
          supervisor: true
        }
      });

      // Create supervisor-trainee history record
      await prisma.supervisorTraineeHistory.create({
        data: {
          supervisorId: existingSupervisor.id,
          traineeId: userId,
          placementId: placement.id,
          startDate: startDate,
          isActive: true
        }
      });

      return NextResponse.json(placement);
    }

    // Case 2: Supervisor exists + Client name doesn't match
    if (existingSupervisor && (!existingClient || !existingSupervisor.supervisedClients.some(sc => sc.client.id === existingClient?.id))) {
      return NextResponse.json(
        { 
          error: 'This supervisor is already registered with a different organisation. Please check the organisation name or contact admin for assistance.',
          code: 'SUPERVISOR_CLIENT_MISMATCH'
        },
        { status: 400 }
      );
    }

    // Case 3: Supervisor doesn't exist + Client exists
    if (!existingSupervisor && existingClient) {
      try {
        const password = generatePassword();
        const hashedPassword = await bcrypt.hash(password, 10);
        const newSupervisor = await prisma.user.create({
          data: {
            name: supervisorName,
            email: supervisorEmail,
            password: hashedPassword,
            role: UserRole.SUPERVISOR,
            supervisedClients: {
              create: {
                clientId: existingClient.id
              }
            }
          }
        });

        // Send welcome email to supervisor
        await sendSupervisorWelcomeEmailWithNotification(supervisorName, supervisorEmail, password);

        const placement = await prisma.placement.create({
          data: {
            name: orgName,
            orgSraNumber,
            startDate,
            endDate,
            isFullTime,
            supervisorName,
            supervisorEmail,
            qweConfirmingPerson,
            qweSraNumber,
            qweEmail,
            documentKey,
            documentName,
            user: { connect: { id: userId } },
            client: { connect: { id: existingClient.id } },
            supervisor: { connect: { id: newSupervisor.id } }
          },
          include: {
            client: true,
            user: true,
            supervisor: true
          }
        });

        // Create supervisor-trainee history record
        await prisma.supervisorTraineeHistory.create({
          data: {
            supervisorId: newSupervisor.id,
            traineeId: userId,
            placementId: placement.id,
            startDate: startDate,
            isActive: true
          }
        });

        return NextResponse.json(placement);
      } catch (error) {
        console.error('Error in Case 3:', error);
        return NextResponse.json(
          { 
            error: 'Failed to create supervisor account. Please try again or contact admin.',
            code: 'SUPERVISOR_CREATION_FAILED'
          },
          { status: 500 }
        );
      }
    }

    // Case 4: Neither supervisor nor client exists
    if (!existingSupervisor && !existingClient) {
      try {
        // Create new client
        const newClient = await prisma.client.create({
          data: {
            name: orgName,
            email: supervisorEmail
          }
        });

        // Create new supervisor
        const password = generatePassword();
        const hashedPassword = await bcrypt.hash(password, 10);
        const newSupervisor = await prisma.user.create({
          data: {
            name: supervisorName,
            email: supervisorEmail,
            password: hashedPassword,
            role: UserRole.SUPERVISOR,
            supervisedClients: {
              create: {
                clientId: newClient.id
              }
            }
          }
        });

        // Send welcome email to supervisor
        await sendSupervisorWelcomeEmailWithNotification(supervisorName, supervisorEmail, password);

        const placement = await prisma.placement.create({
          data: {
            name: orgName,
            orgSraNumber,
            startDate,
            endDate,
            isFullTime,
            supervisorName,
            supervisorEmail,
            qweConfirmingPerson,
            qweSraNumber,
            qweEmail,
            documentKey,
            documentName,
            user: { connect: { id: userId } },
            client: { connect: { id: newClient.id } },
            supervisor: { connect: { id: newSupervisor.id } }
          },
          include: {
            client: true,
            user: true,
            supervisor: true
          }
        });

        // Create supervisor-trainee history record
        await prisma.supervisorTraineeHistory.create({
          data: {
            supervisorId: newSupervisor.id,
            traineeId: userId,
            placementId: placement.id,
            startDate: startDate,
            isActive: true
          }
        });

        return NextResponse.json(placement);
      } catch (error) {
        console.error('Error in Case 4:', error);
        return NextResponse.json(
          { 
            error: 'Failed to create client and supervisor accounts. Please try again or contact admin.',
            code: 'CLIENT_SUPERVISOR_CREATION_FAILED'
          },
          { status: 500 }
        );
      }
    }

  } catch (error) {
    console.error('Error creating placement:', error);
    return NextResponse.json(
      { 
        error: 'An unexpected error occurred. Please try again or contact admin.',
        code: 'UNKNOWN_ERROR'
      },
      { status: 500 }
    );
  }
}
