import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { UserRole } from '@/types';
import { generatePassword } from '@/lib/utils';
import { sendSupervisorWelcomeEmail } from '@/lib/email';
import bcrypt from 'bcryptjs';

// GET a specific placement
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }>; }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check access permission for the placement
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const whereClause: any = {
      id: id,
    };

    // If not ADMIN, only allow viewing own placements or placements where user is supervisor/mentor
    if (session.user.role !== UserRole.ADMIN) {
      whereClause.OR = [
        { userId: session.user.id },
        { supervisorId: session.user.id },
        { mentorId: session.user.id },
      ];
    }

    const placement = await prisma.placement.findFirst({
      where: whereClause,
      include: {
        client: true,
        user: true,
        supervisor: true,
        mentor: true,
      },
    });

    if (!placement) {
      return NextResponse.json(
        { error: 'Placement not found or you do not have permission to access it' },
        { status: 404 }
      );
    }

    // Convert partTimeDays from numbers to day strings for UI
    const dayMap = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    const partTimeDaysAsStrings = placement.partTimeDays?.map(day => dayMap[day - 1]) || [];

    // Transform data for compatibility with frontend
    const transformedPlacement = {
      ...placement,
      // For backward compatibility with existing components
      position: placement.name,
      fullTime: placement.isFullTime,
      partTimeDays: partTimeDaysAsStrings,
    };

    return NextResponse.json(transformedPlacement);
  } catch (error) {
    console.error('Error fetching placement:', error);
    return NextResponse.json(
      { error: 'Failed to fetch placement' },
      { status: 500 }
    );
  }
}

// PATCH update a placement
export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ id: string }>; }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();

    // Format dates to ISO-8601
    const formattedData = {
      ...data,
      startDate: data.startDate ? new Date(data.startDate).toISOString() : null,
      endDate: data.endDate ? new Date(data.endDate).toISOString() : null,
    };

    // Validate the placement exists first
    const existingPlacement = await prisma.placement.findUnique({
      where: { id: id },
    });

    if (!existingPlacement) {
      return NextResponse.json(
        { error: 'Placement not found' },
        { status: 404 }
      );
    }

    // Check for supervisor and organization changes
    if (formattedData.supervisorEmail || formattedData.orgName) {
      const existingSupervisor = await prisma.user.findUnique({
        where: { email: formattedData.supervisorEmail },
        include: {
          supervisedClients: {
            include: {
              client: true
            }
          }
        }
      });

      const existingClient = await prisma.client.findFirst({
        where: { name: formattedData.orgName }
      });

      // Case 1: Supervisor exists + Client name matches - update the placement
      if (existingSupervisor && existingClient && existingSupervisor.supervisedClients.some(sc => sc.client.id === existingClient.id)) {
        // Check if supervisor is changing
        if (existingPlacement.supervisorId !== existingSupervisor.id) {
          // Mark current supervisor relationship as inactive
          if (existingPlacement.supervisorId) {
            await prisma.supervisorTraineeHistory.updateMany({
              where: {
                supervisorId: existingPlacement.supervisorId,
                traineeId: existingPlacement.userId,
                placementId: id,
                isActive: true
              },
              data: {
                isActive: false,
                endDate: new Date()
              }
            });
          }

          // Create new supervisor-trainee history record
          await prisma.supervisorTraineeHistory.create({
            data: {
              supervisorId: existingSupervisor.id,
              traineeId: existingPlacement.userId,
              placementId: id,
              startDate: formattedData.startDate || existingPlacement.startDate,
              isActive: true
            }
          });
        }

        const updateData = {
          name: formattedData.orgName,
          orgSraNumber: formattedData.orgSraNumber,
          startDate: formattedData.startDate,
          endDate: formattedData.endDate,
          isFullTime: formattedData.isFullTime,
          supervisorName: formattedData.supervisorName,
          supervisorEmail: formattedData.supervisorEmail,
          qweConfirmingPerson: formattedData.qweConfirmingPerson,
          qweSraNumber: formattedData.qweSraNumber,
          qweEmail: formattedData.qweEmail,
          documentKey: formattedData.documentKey,
          documentName: formattedData.documentName,
          client: { connect: { id: existingClient.id } },
          supervisor: { connect: { id: existingSupervisor.id } }
        };

        const placement = await prisma.placement.update({
          where: { id },
          data: updateData,
          include: {
            client: true,
            user: true,
            supervisor: true,
            mentor: true,
          },
        });

        return NextResponse.json(placement);
      }

      // Case 2: Supervisor exists + Client name doesn't match
      if (existingSupervisor && (!existingClient || !existingSupervisor.supervisedClients.some(sc => sc.client.id === existingClient?.id))) {
        return NextResponse.json(
          { 
            error: 'This supervisor is already registered with a different organisation. Please check the organisation name or contact admin for assistance.',
            code: 'SUPERVISOR_CLIENT_MISMATCH'
          },
          { status: 400 }
        );
      }

      // Case 3: New supervisor + Existing client
      if (!existingSupervisor && existingClient) {
        try {
          const password = generatePassword();
          const hashedPassword = await bcrypt.hash(password, 10);
          const newSupervisor = await prisma.user.create({
            data: {
              name: formattedData.supervisorName,
              email: formattedData.supervisorEmail,
              password: hashedPassword,
              role: UserRole.SUPERVISOR,
              supervisedClients: {
                create: {
                  clientId: existingClient.id
                }
              }
            }
          });

          // Send welcome email to supervisor
          await sendSupervisorWelcomeEmail(formattedData.supervisorName, formattedData.supervisorEmail, password);

          const placement = await prisma.placement.update({
            where: { id },
            data: {
              name: formattedData.orgName,
              orgSraNumber: formattedData.orgSraNumber,
              startDate: formattedData.startDate,
              endDate: formattedData.endDate,
              isFullTime: formattedData.isFullTime,
              supervisorName: formattedData.supervisorName,
              supervisorEmail: formattedData.supervisorEmail,
              qweConfirmingPerson: formattedData.qweConfirmingPerson,
              qweSraNumber: formattedData.qweSraNumber,
              qweEmail: formattedData.qweEmail,
              documentKey: formattedData.documentKey,
              documentName: formattedData.documentName,
              client: { connect: { id: existingClient.id } },
              supervisor: { connect: { id: newSupervisor.id } }
            },
            include: {
              client: true,
              user: true,
              supervisor: true,
              mentor: true,
            },
          });

          return NextResponse.json(placement);
        } catch (error) {
          console.error('Error in Case 3:', error);
          return NextResponse.json(
            { 
              error: 'Failed to create supervisor account. Please try again or contact admin.',
              code: 'SUPERVISOR_CREATION_FAILED'
            },
            { status: 500 }
          );
        }
      }

      // Case 4: Neither supervisor nor client exists
      if (!existingSupervisor && !existingClient) {
        try {
          // Create new client
          const newClient = await prisma.client.create({
            data: {
              name: formattedData.orgName,
              email: formattedData.supervisorEmail
            }
          });

          // Create new supervisor
          const password = generatePassword();
          const hashedPassword = await bcrypt.hash(password, 10);
          const newSupervisor = await prisma.user.create({
            data: {
              name: formattedData.supervisorName,
              email: formattedData.supervisorEmail,
              password: hashedPassword,
              role: UserRole.SUPERVISOR,
              supervisedClients: {
                create: {
                  clientId: newClient.id
                }
              }
            }
          });

          // Send welcome email to supervisor
          await sendSupervisorWelcomeEmail(formattedData.supervisorName, formattedData.supervisorEmail, password);

          const placement = await prisma.placement.update({
            where: { id },
            data: {
              name: formattedData.orgName,
              orgSraNumber: formattedData.orgSraNumber,
              startDate: formattedData.startDate,
              endDate: formattedData.endDate,
              isFullTime: formattedData.isFullTime,
              supervisorName: formattedData.supervisorName,
              supervisorEmail: formattedData.supervisorEmail,
              qweConfirmingPerson: formattedData.qweConfirmingPerson,
              qweSraNumber: formattedData.qweSraNumber,
              qweEmail: formattedData.qweEmail,
              documentKey: formattedData.documentKey,
              documentName: formattedData.documentName,
              client: { connect: { id: newClient.id } },
              supervisor: { connect: { id: newSupervisor.id } }
            },
            include: {
              client: true,
              user: true,
              supervisor: true,
              mentor: true,
            },
          });

          return NextResponse.json(placement);
        } catch (error) {
          console.error('Error in Case 4:', error);
          return NextResponse.json(
            { 
              error: 'Failed to create client and supervisor accounts. Please try again or contact admin.',
              code: 'CLIENT_SUPERVISOR_CREATION_FAILED'
            },
            { status: 500 }
          );
        }
      }
    }

    // If no supervisor/org changes or validation passed, update normally
    const updateData = {
      name: formattedData.name || '',
      orgSraNumber: formattedData.orgSraNumber,
      startDate: formattedData.startDate,
      endDate: formattedData.endDate,
      isFullTime: formattedData.isFullTime,
      supervisorName: formattedData.supervisorName,
      supervisorEmail: formattedData.supervisorEmail,
      qweConfirmingPerson: formattedData.qweConfirmingPerson,
      qweSraNumber: formattedData.qweSraNumber,
      qweEmail: formattedData.qweEmail,
      documentKey: formattedData.documentKey,
      documentName: formattedData.documentName,
    };

    const placement = await prisma.placement.update({
      where: { id },
      data: updateData,
      include: {
        client: true,
        user: true,
        supervisor: true,
        mentor: true,
      },
    });

    return NextResponse.json(placement);
  } catch (error) {
    console.error('Error updating placement:', error);

    // Handle specific Prisma errors
    if (error instanceof Error) {
      if (error.message.includes('Unique constraint')) {
        return NextResponse.json(
          { error: 'A placement with these details already exists' },
          { status: 400 }
        );
      }
      if (error.message.includes('Foreign key constraint')) {
        return NextResponse.json(
          { error: 'Invalid client, user, supervisor, or mentor ID' },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to update placement', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// DELETE a placement
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }>; }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await prisma.placement.delete({
      where: {
        id: id,
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting placement:', error);
    return NextResponse.json(
      { error: 'Failed to delete placement' },
      { status: 500 }
    );
  }
}