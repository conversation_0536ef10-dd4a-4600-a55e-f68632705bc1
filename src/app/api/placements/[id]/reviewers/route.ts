import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { UserRole } from '@/types';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }>; }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Get the placement ID from the URL
    const placementId = id;
    
    // Fetch the placement to get supervisor and mentor IDs
    const placement = await prisma.placement.findUnique({
      where: { id: placementId },
      include: {
        supervisor: true,
        mentor: true,
        user: true,
      },
    });
    
    if (!placement) {
      return NextResponse.json({ error: 'Placement not found' }, { status: 404 });
    }
    
    // Check if the user is the trainee of this placement
    if (placement.userId !== session.user.id && session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    
    // Create an array of reviewers (supervisor and mentor)
    const reviewers = [];
    
    // Add supervisor if exists
    if (placement.supervisor) {
      reviewers.push({
        id: placement.supervisor.id,
        name: placement.supervisor.name,
        email: placement.supervisor.email,
        role: 'SUPERVISOR',
      });
    }
    
    // Add mentor if exists
    if (placement.mentor) {
      reviewers.push({
        id: placement.mentor.id,
        name: placement.mentor.name,
        email: placement.mentor.email,
        role: 'MENTOR',
      });
    }
    
    return NextResponse.json(reviewers);
  } catch (error) {
    console.error('Error fetching reviewers:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reviewers' },
      { status: 500 }
    );
  }
}
