import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// Simulate uploading file to S3 (in production, we would use AWS SDK)
async function uploadToS3(file: File, placementId: string): Promise<string> {
  // In production, this would be the actual S3 upload code
  // Return the S3 key of the uploaded file
  const fileExtension = file.name.split('.').pop();
  const s3Key = `placements/${placementId}/${Date.now()}.${fileExtension}`;

  // Simulate successful upload
  console.log(`Uploaded file ${file.name} to ${s3Key}`);

  return s3Key;
}

export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }>; }
) {
  try {
    const { id } = await params;
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check access permission for the placement
    const placement = await prisma.placement.findFirst({
      where: {
        id: id,
        userId: session.user.id,
      },
    });

    if (!placement) {
      return NextResponse.json(
        { error: 'Placement not found or you do not have permission to access it' },
        { status: 404 }
      );
    }

    // Process form data
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Check file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'File size exceeds 10MB limit' },
        { status: 400 }
      );
    }

    // Check file type
    const allowedTypes = [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'File type not allowed. Allowed types: PDF, JPG, PNG, DOC, DOCX' },
        { status: 400 }
      );
    }

    // Upload file to S3 (simulated)
    const s3Key = await uploadToS3(file, id);

    // Update placement with document key
    const updatedPlacement = await prisma.placement.update({
      where: { id: id },
      data: {
        documentKey: s3Key,
        documentName: file.name,
      },
    });

    return NextResponse.json({
      success: true,
      document: {
        id: updatedPlacement.id,
        name: updatedPlacement.documentName,
        fileType: file.type,
        s3Key: updatedPlacement.documentKey,
      },
    });
  } catch (error) {
    console.error('Error uploading document:', error);
    return NextResponse.json(
      { error: 'Failed to upload document' },
      { status: 500 }
    );
  }
}
