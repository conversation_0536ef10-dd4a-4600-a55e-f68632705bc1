import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { UserRole } from '@/types';

export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }>; }
) {
  const session = await getServerSession(authOptions);
  const { id } = await params;

  if (!session?.user || session.user.role !== UserRole.ADMIN) {
    return new NextResponse('Unauthorized', { status: 401 });
  }

  try {
    const { newClientId } = await request.json();

    if (!newClientId) {
      return NextResponse.json(
        { error: 'New client ID is required' },
        { status: 400 }
      );
    }

    // Check if placement exists
    const placement = await prisma.placement.findUnique({
      where: { id },
      include: {
        client: true
      }
    });

    if (!placement) {
      return NextResponse.json(
        { error: 'Placement not found' },
        { status: 404 }
      );
    }

    // Check if new client exists
    const newClient = await prisma.client.findUnique({
      where: { id: newClientId }
    });

    if (!newClient) {
      return NextResponse.json(
        { error: 'New client not found' },
        { status: 404 }
      );
    }

    // Move placement to new client
    const updatedPlacement = await prisma.placement.update({
      where: { id },
      data: {
        clientId: newClientId
      },
      include: {
        client: true,
        entries: true
      }
    });

    return NextResponse.json(updatedPlacement);
  } catch (error) {
    console.error('Error moving placement:', error);
    return NextResponse.json(
      { error: 'Failed to move placement' },
      { status: 500 }
    );
  }
} 