import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// GET /api/character-suitability
export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const characterSuitability = await prisma.characterSuitability.findFirst({
      include: {
        userCompletions: {
          where: {
            userId: session.user.id
          }
        }
      }
    });

    return NextResponse.json(characterSuitability);
  } catch (error) {
    console.error('Error fetching character suitability:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// POST /api/character-suitability (Admin only)
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await req.json();
    const { rules, infoForQualifiedSolicitors, externalLinks } = data;

    const characterSuitability = await prisma.characterSuitability.create({
      data: {
        rules,
        infoForQualifiedSolicitors,
        externalLinks
      }
    });

    return NextResponse.json(characterSuitability);
  } catch (error) {
    console.error('Error creating character suitability:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// PUT /api/character-suitability (Admin only)
export async function PUT(req: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await req.json();
    const { id, rules, infoForQualifiedSolicitors, externalLinks } = data;

    let characterSuitability;
    
    if (id) {
      // Update existing record
      characterSuitability = await prisma.characterSuitability.update({
        where: { id },
        data: {
          rules,
          infoForQualifiedSolicitors,
          externalLinks
        }
      });
    } else {
      // Create new record if none exists
      const existingRecord = await prisma.characterSuitability.findFirst();
      
      if (existingRecord) {
        // If a record exists but no ID was provided, update the first record
        characterSuitability = await prisma.characterSuitability.update({
          where: { id: existingRecord.id },
          data: {
            rules,
            infoForQualifiedSolicitors,
            externalLinks
          }
        });
      } else {
        // Create new record if none exists
        characterSuitability = await prisma.characterSuitability.create({
          data: {
            rules,
            infoForQualifiedSolicitors,
            externalLinks
          }
        });
      }
    }

    return NextResponse.json(characterSuitability);
  } catch (error) {
    console.error('Error updating character suitability:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
} 