import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { UserRole } from '@/types';

// POST /api/character-suitability/completion
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await req.json();
    const { characterSuitabilityId, completed } = data;

    const completion = await prisma.characterSuitabilityCompletion.upsert({
      where: {
        userId_characterSuitabilityId: {
          userId: session.user.id,
          characterSuitabilityId
        }
      },
      update: {
        completed,
        completedAt: completed ? new Date() : null
      },
      create: {
        userId: session.user.id,
        characterSuitabilityId,
        completed,
        completedAt: completed ? new Date() : null
      }
    });

    return NextResponse.json(completion);
  } catch (error) {
    console.error('Error updating completion status:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// GET /api/character-suitability/completion (Admin only)
export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get all trainees
    const trainees = await prisma.user.findMany({
      where: {
        role: UserRole.TRAINEE
      },
      select: {
        id: true,
        name: true,
        email: true,
        skillGroupType: true,
        characterSuitabilityCompletions: {
          select: {
            completed: true,
            completedAt: true
          }
        }
      }
    });

    // Transform data to match expected format
    const formattedData = trainees.map(trainee => ({
      id: trainee.id,
      user: {
        id: trainee.id,
        name: trainee.name,
        email: trainee.email,
        skillGroupType: trainee.skillGroupType
      },
      completed: trainee.characterSuitabilityCompletions[0]?.completed || false,
      completedAt: trainee.characterSuitabilityCompletions[0]?.completedAt || null
    }));

    return NextResponse.json(formattedData);
  } catch (error) {
    console.error('Error fetching completions:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
} 