import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { UserRole } from '@/types';

export async function GET() {
  try {
    // Get all supervisors that are not assigned to any client
    const availableSupervisors = await prisma.user.findMany({
      where: {
        role: UserRole.SUPERVISOR,
        supervisedClients: {
          none: {}  // No client assignments
        }
      },
      select: {
        id: true
      }
    });

    return NextResponse.json(availableSupervisors.map(sup => sup.id));
  } catch (error) {
    console.error('Error fetching available supervisors:', error);
    return NextResponse.json(
      { error: 'Failed to fetch available supervisors' },
      { status: 500 }
    );
  }
}