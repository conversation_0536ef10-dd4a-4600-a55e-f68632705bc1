import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }>; }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const { id: userId, role } = session.user;
    const practiceAreaId = id;
    let traineeId = userId;
    if (role !== 'TRAINEE') {
      const { searchParams } = new URL(request.url);
      const queryTraineeId = searchParams.get('traineeId');
      if (!queryTraineeId) {
        return NextResponse.json({ error: 'Trainee ID is required' }, { status: 400 });
      }
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      traineeId = queryTraineeId;
    }
    // const progressTracking = await prisma.progressTracking.findUnique({ ... });
    const progressTracking = {
      skillProgress: [],
      user: { name: '', email: '' }
    };
    if (!progressTracking) {
      return NextResponse.json({ error: 'Progress tracking not found' }, { status: 404 });
    }
    const practiceArea = await prisma.practiceArea.findUnique({
      where: { id: practiceAreaId }
    });
    if (!practiceArea) {
      return NextResponse.json({ error: 'Practice area not found' }, { status: 404 });
    }
    const totalSkills = progressTracking.skillProgress.length;
    const completedSkills = progressTracking.skillProgress.filter(
      (sp: { doneEntryCount: number; minSuggestedEntryCount: number }) => sp.doneEntryCount >= sp.minSuggestedEntryCount
    ).length;
    const areaProgress = totalSkills > 0 ? (completedSkills / totalSkills) * 100 : 0;
    const skills = progressTracking.skillProgress.map((sp: { doneEntryCount: number; minSuggestedEntryCount: number; skill?: { id: string; name: string } }) => ({
      id: sp.skill?.id || '',
      name: sp.skill?.name || '',
      doneEntryCount: sp.doneEntryCount,
      minSuggestedEntryCount: sp.minSuggestedEntryCount,
      progress: Math.min((sp.doneEntryCount / sp.minSuggestedEntryCount) * 100, 100),
      isCompleted: sp.doneEntryCount >= sp.minSuggestedEntryCount
    }));
    return NextResponse.json({
      trainee: progressTracking.user,
      practiceArea: {
        id: practiceArea.id,
        name: practiceArea.name,
      },
      progress: areaProgress,
      totalSkills,
      completedSkills,
      skills
    });
  } catch (error) {
    console.error('Error fetching practice area progress:', error);
    return NextResponse.json({ error: 'Failed to fetch practice area progress' }, { status: 500 });
  }
}
