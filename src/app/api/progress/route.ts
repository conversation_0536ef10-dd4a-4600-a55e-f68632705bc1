import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const { id: userId, role } = session.user;
    let traineeId = userId;
    if (role !== 'TRAINEE') {
      const { searchParams } = new URL(request.url);
      const queryTraineeId = searchParams.get('traineeId');
      if (!queryTraineeId) {
        return NextResponse.json({ error: 'Trainee ID is required' }, { status: 400 });
      }
      traineeId = queryTraineeId;
    }
    // const progressTracking = await prisma.progressTracking.findUnique({ ... });
    const progressTracking = {
      skillProgress: [],
      user: { name: '', email: '', qualificationRoute: '' }
    };
    if (!progressTracking) {
      return NextResponse.json({ 
        error: 'Progress tracking not found',
        needsInitialization: true 
      }, { status: 404 });
    }
    const totalSkills = progressTracking.skillProgress.length;
    const completedSkills = progressTracking.skillProgress.filter(
      (sp: any) => sp.doneEntryCount >= sp.minSuggestedEntryCount
    ).length;
    const overallProgress = totalSkills > 0 ? (completedSkills / totalSkills) * 100 : 0;
    const practiceAreas: any = {};
    progressTracking.skillProgress.forEach((sp: any) => {
      const skillSetId = sp.skill?.skillSet?.id || '';
      const skillSetName = sp.skill?.skillSet?.name || '';
      if (!practiceAreas[skillSetId]) {
        practiceAreas[skillSetId] = {
          id: skillSetId,
          name: skillSetName,
          totalSkills: 0,
          completedSkills: 0,
          progress: 0,
          skills: []
        };
      }
      const isCompleted = sp.doneEntryCount >= sp.minSuggestedEntryCount;
      practiceAreas[skillSetId].totalSkills++;
      if (isCompleted) {
        practiceAreas[skillSetId].completedSkills++;
      }
      practiceAreas[skillSetId].skills.push({
        id: sp.skill?.id || '',
        name: sp.skill?.name || '',
        doneEntryCount: sp.doneEntryCount,
        minSuggestedEntryCount: sp.minSuggestedEntryCount,
        progress: Math.min((sp.doneEntryCount / sp.minSuggestedEntryCount) * 100, 100)
      });
    });
    Object.values(practiceAreas).forEach((area: any) => {
      area.progress = area.totalSkills > 0 ? (area.completedSkills / area.totalSkills) * 100 : 0;
    });
    return NextResponse.json({
      trainee: progressTracking.user,
      overallProgress,
      totalSkills,
      completedSkills,
      practiceAreas: Object.values(practiceAreas)
    });
  } catch (error) {
    console.error('Error fetching progress:', error);
    return NextResponse.json({ error: 'Failed to fetch progress' }, { status: 500 });
  }
}
