import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { PracticeSubSkillType } from '@/generated/prisma';

export async function POST() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: userId, role } = session.user;
    
    if (role !== 'TRAINEE') {
      return NextResponse.json({ error: 'Only trainees can have progress tracking' }, { status: 403 });
    }

    // const existingTracking = await prisma.progressTracking.findUnique({ where: { userId } });
    const existingTracking = null;

    if (existingTracking) {
      return NextResponse.json({ error: 'Progress tracking already exists' }, { status: 400 });
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { qualificationRoute: true }
    });

    const qualificationRoute = user?.qualificationRoute || 'TC';
    const skills = await prisma.practiceSkill.findMany({
      where: {
        practiceSkillGroups: {
          some: {
            practiceSubSkills: {
              some: {
                practiceSubSkillType: qualificationRoute as PracticeSubSkillType
              }
            }
          }
        }
      }
    });

    // const progressTracking = await prisma.progressTracking.create({ ... });
    const progressTracking = {
      userId,
      skillProgress: skills.map((skill: { id: string }) => ({
        skillId: skill.id,
        doneEntryCount: 0,
        minSuggestedEntryCount: 1
      }))
    };

    return NextResponse.json(progressTracking);
  } catch (error) {
    console.error('Error initializing progress tracking:', error);
    return NextResponse.json({ error: 'Failed to initialize progress tracking' }, { status: 500 });
  }
}
