import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function PUT(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const { entryId } = await request.json();
    const entry = await prisma.entry.findUnique({
      where: { id: entryId },
      include: {
        entrySubSkills: {
          include: {
            subSkill: true,
          },
        },
        placement: {
          select: {
            userId: true
          }
        }
      }
    });
    if (!entry) {
      return NextResponse.json({ error: 'Entry not found' }, { status: 404 });
    }
    if (entry.status !== 'signedoff') {
      return NextResponse.json({ error: 'Entry is not approved' }, { status: 400 });
    }
    // const progressTracking = await prisma.progressTracking.findUnique({ ... });
    // const updatePromises = entry.skills.map(...)
    // await Promise.all(updatePromises.filter(Boolean));
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error updating progress:', error);
    return NextResponse.json({ error: 'Failed to update progress' }, { status: 500 });
  }
}
