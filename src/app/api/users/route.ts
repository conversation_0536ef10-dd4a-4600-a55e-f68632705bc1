import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { UserRole } from '@/types';
import bcrypt from 'bcryptjs';

// GET all users with optional role filtering
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const role = searchParams.get('role');
    const isTrainingPrincipalParam = searchParams.get('isTrainingPrincipal');
    const isTrainingPrincipal = isTrainingPrincipalParam === 'true';
    const forAppraisals = searchParams.get('forAppraisals') === 'true';
    if (forAppraisals) {
      const userPlacements = await prisma.placement.findMany({
        where: {
          userId: session.user.id,
        },
        select: {
          supervisor: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
              isTrainingPrincipal: true
            }
          }
        }
      });
      
      const trainingPrincipals = await prisma.user.findMany({
        where: {
          role: UserRole.MENTOR,
          isTrainingPrincipal: true
        },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          isTrainingPrincipal: true
        }
      });

      // Get unique supervisors (excluding null values)
      const supervisors = Array.from(
        new Set(
          userPlacements
            .map(p => p.supervisor)
            .filter((s): s is NonNullable<typeof s> => s !== null)
            .map(s => JSON.stringify({ ...s, isTrainingPrincipal: false }))
        )
      ).map(s => JSON.parse(s));

      return NextResponse.json([...supervisors, ...trainingPrincipals]);
    }

    const whereClause: any = {};
    
    if (role && Object.values(UserRole).includes(role as UserRole)) {
      whereClause.role = role;
    }
    
    if (role === UserRole.MENTOR && isTrainingPrincipalParam !== null) {
      whereClause.isTrainingPrincipal = isTrainingPrincipal;
    }
    
    const users = await prisma.user.findMany({
      where: whereClause,
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isTrainingPrincipal: true,
        address: true,
        createdAt: true,
        traineeLevel: true,
        qualificationRoute: true
      }
    });

    return NextResponse.json(users);
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

// POST create a new user
export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  console.log('Session in user creation:', session);

  if (!session?.user || session.user.role !== UserRole.ADMIN) {
    console.log('Unauthorized attempt to create user:', {
      hasSession: !!session,
      userRole: session?.user?.role,
    });
    return new NextResponse('Unauthorized', { status: 401 });
  }

  try {
    const body = await request.json();
    console.log('Received user creation data:', body);
    const { name, email, password, role, address, traineeLevel, isTrainingPrincipal } = body;

    // Validate required fields
    if (!name || !email || !password || !role) {
      console.log('Missing required fields:', { name, email, password, role });
      return NextResponse.json(
        { error: 'Name, email, password, and role are required' },
        { status: 400 }
      );
    }

    // Check if user with the same email already exists
    const existingUser = await prisma.user.findFirst({
      where: { email },
    });

    if (existingUser) {
      console.log('User already exists with email:', email);
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 400 }
      );
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create user with traineeLevel if role is TRAINEE
    const user = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        role: role as UserRole,
        address: address,
        ...(role === 'TRAINEE' && traineeLevel ? { traineeLevel: Number(traineeLevel) } : {}),
        ...(role === 'MENTOR' ? { isTrainingPrincipal: isTrainingPrincipal || false } : {}),
      },
    });

    console.log('Successfully created user:', {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      traineeLevel: user.traineeLevel,
    });

    // Remove password from response
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password: _, ...userWithoutPassword } = user;
    return NextResponse.json(userWithoutPassword);
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 