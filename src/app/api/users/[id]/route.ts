import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { UserRole } from '@/types';

export async function PUT(
  request: Request,
  context: { params: Promise<{ id: string }>; }
) {
  const session = await getServerSession(authOptions);
  const params = await context.params;

  if (!session?.user || session.user.role !== UserRole.ADMIN) {
    return new NextResponse('Unauthorized', { status: 401 });
  }

  try {
    const body = await request.json();
    const { mentorId, menteeIds, ...userData } = body;

    // Update the user's basic information
    const updatedUser = await prisma.user.update({
      where: { id: params.id },
      data: {
        ...userData,
        mentorId: mentorId || null,
      },
      include: {
        mentor: true,
        mentees: true,
      },
    });

    // If this user is a mentor and has mentees assigned
    if (menteeIds && userData.role === 'MENTOR') {
      // Get current mentees before changing
      const currentMentees = await prisma.user.findMany({
        where: { mentorId: params.id },
        select: { id: true }
      });

      // Mark current relationships as inactive
      if (currentMentees.length > 0) {
        await prisma.mentorMenteeHistory.updateMany({
          where: {
            mentorId: params.id,
            menteeId: { in: currentMentees.map(m => m.id) },
            isActive: true
          },
          data: {
            isActive: false,
            endDate: new Date()
          }
        });
      }

      // Remove any existing mentee relationships
      await prisma.user.updateMany({
        where: { mentorId: params.id },
        data: { mentorId: null },
      });

      // Set the new mentee relationships
      await prisma.user.updateMany({
        where: { id: { in: menteeIds } },
        data: { mentorId: params.id },
      });

      // Create new relationship history records
      await prisma.mentorMenteeHistory.createMany({
        data: menteeIds.map((menteeId: string) => ({
          mentorId: params.id,
          menteeId: menteeId,
          startDate: new Date(),
          isActive: true
        }))
      });
    }

    return NextResponse.json(updatedUser);
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: Request,
  context: { params: Promise<{ id: string }>; }
) {
  const session = await getServerSession(authOptions);
  const params = await context.params;

  if (!session?.user || session.user.role !== UserRole.ADMIN) {
    return new NextResponse('Unauthorized', { status: 401 });
  }

  try {
    const userId = params.id;
    const body = await request.json();
    const { name, email, role, traineeLevel, mentorId, phoneNumber, address, qualificationRoute, isTrainingPrincipal } = body;

    // Check if user with the same email already exists (excluding the current user)
    const existingUser = await prisma.user.findFirst({
      where: {
        email,
        NOT: {
          id: userId,
        },
      },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'Email already in use' },
        { status: 400 }
      );
    }

    // Get current user data to check if qualification route is being changed
    const currentUser = await prisma.user.findUnique({
      where: { id: userId },
      select: { 
        role: true,
        qualificationRoute: true,
        mentorId: true
      }
    });

    // If the user is a trainee and qualification route is being changed
    if (
      currentUser?.role === 'TRAINEE' && 
      qualificationRoute && 
      (!currentUser.qualificationRoute || currentUser.qualificationRoute !== qualificationRoute)
    ) {
      // Check if trainee has any skills with progress
      const traineeSkills = await prisma.traineeSkill.findMany({
        where: { 
          traineeId: userId,
          doneEntryCount: { gt: 0 } // Only find skills with progress
        }
      });

      // If trainee has progress, don't allow changing qualification route
      if (traineeSkills.length > 0) {
        return NextResponse.json(
          { error: 'Cannot change qualification route once trainee has progress on skills' },
          { status: 400 }
        );
      }

      // If trainee has initialized skills but no progress, delete them to allow reinitializing
      const allTraineeSkills = await prisma.traineeSkill.findMany({
        where: { traineeId: userId }
      });

      if (allTraineeSkills.length > 0) {
        await prisma.traineeSkill.deleteMany({
          where: { traineeId: userId }
        });
      }

      // Get all skills based on the new qualification route
      const subSkills = await prisma.practiceSubSkill.findMany({
        where: {
          practiceSubSkillType: qualificationRoute.toLowerCase() as 'tc' | 'sqe'
        }
      });

      // Create TraineeSkill records for each skill of the new qualification route
      if (subSkills.length > 0) {
        await Promise.all(
          subSkills.map(subSkill => 
            prisma.traineeSkill.create({
              data: {
                traineeId: userId,
                subSkillId: subSkill.id,
                doneEntryCount: 0,
                minSuggestedEntryCount: subSkill.minSuggestedEntryCount
              }
            })
          )
        );
      }
    }

    // Handle mentor change for trainee
    if (currentUser?.role === 'TRAINEE' && mentorId !== currentUser.mentorId) {
      // Mark current mentor relationship as inactive
      if (currentUser.mentorId) {
        await prisma.mentorMenteeHistory.updateMany({
          where: {
            mentorId: currentUser.mentorId,
            menteeId: userId,
            isActive: true
          },
          data: {
            isActive: false,
            endDate: new Date()
          }
        });
      }

      // Create new mentor relationship if mentorId is provided
      if (mentorId) {
        await prisma.mentorMenteeHistory.create({
          data: {
            mentorId: mentorId,
            menteeId: userId,
            startDate: new Date(),
            isActive: true
          }
        });
      }
    }

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        name,
        email,
        role,
        traineeLevel,
        mentorId,
        phoneNumber,
        address,
        qualificationRoute,
        ...(role === 'MENTOR' ? { isTrainingPrincipal } : {}),
      },
      include: {
        mentor: true,
        mentees: true,
      },
    });

    return NextResponse.json(updatedUser);
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  context: { params: Promise<{ id: string }>; }
) {
  const session = await getServerSession(authOptions);
  const params = await context.params;

  if (!session?.user || session.user.role !== UserRole.ADMIN) {
    return new NextResponse('Unauthorized', { status: 401 });
  }

  try {
    const userId = params.id;

    await prisma.$transaction(async (tx) => {
      await tx.traineeSkill.deleteMany({
        where: { traineeId: userId }
      });

      await tx.entry.deleteMany({
        where: { 
          placement: {
            userId: userId
          }
        }
      });

      await tx.placement.deleteMany({
        where: { userId: userId }
      });

      await tx.user.updateMany({
        where: { mentorId: userId },
        data: { mentorId: null }
      });

      await tx.user.delete({
        where: { id: userId }
      });
    });

    return NextResponse.json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: Request,
  context: { params: Promise<{ id: string }>; }
) {
  try {
    const params = await context.params;
    const userId = params.id;

    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        placements: {
          include: {
            entries: true,
            client: true,
            supervisor: true,
            mentor: true,
          },
        },
        mentor: true,
        mentees: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user' },
      { status: 500 }
    );
  }
} 