import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const traineeWithMentor = await prisma.user.findUnique({
      where: { id },
      include: {
        mentor: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      }
    });

    if (!traineeWithMentor) {
      return NextResponse.json(
        { error: 'Trainee not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(traineeWithMentor.mentor);
  } catch (error) {
    console.error('Error fetching trainee mentor:', error);
    return NextResponse.json(
      { error: 'Failed to fetch trainee mentor' },
      { status: 500 }
    );
  }
}