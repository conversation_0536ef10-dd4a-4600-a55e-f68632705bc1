import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { UserRole } from '@/types';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: userId } = session.user;

    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        mentor: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        },
        mentees: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        },
      }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    const userWithoutPassword = {
      ...user,
      password: undefined,
    };
    return NextResponse.json(userWithoutPassword);
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json({ error: 'Failed to fetch user' }, { status: 500 });
  }
}

export async function PATCH(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: userId, role } = session.user;
    const body = await request.json();
    
    // Limit which fields can be updated based on role
    const allowedFields = {
      [UserRole.TRAINEE]: [
        'phoneNumber', 
        'address', 
        'qualificationRoute',
        'qualificationRouteDismissed',
        'qualificationRouteUpdatedAt'
      ],
      [UserRole.MENTOR]: ['phoneNumber', 'address'],
      [UserRole.SUPERVISOR]: ['phoneNumber', 'address'],
      [UserRole.ADMIN]: [
        'phoneNumber', 
        'address', 
        'qualificationRoute',
        'qualificationRouteDismissed',
        'qualificationRouteUpdatedAt'
      ],
    };

    // Extract only allowed fields for the current user role
    const allowedUpdates: Record<string, unknown> = {};
    const userAllowedFields = allowedFields[role as UserRole] || [];
    
    for (const field of userAllowedFields) {
      if (field in body) {
        allowedUpdates[field] = body[field];
      }
    }

    // If the user is trying to update qualification route
    if ('qualificationRoute' in allowedUpdates) {
      // Check if trainee has any skills with progress
      if (role === UserRole.TRAINEE) {
        const traineeSkills = await prisma.traineeSkill.findMany({
          where: { 
            traineeId: userId,
            doneEntryCount: { gt: 0 } // Only find skills with progress
          }
        });

        // If trainee has progress, don't allow changing qualification route
        if (traineeSkills.length > 0) {
          return NextResponse.json(
            { error: 'Cannot change qualification route once you have progress on skills' },
            { status: 400 }
          );
        }

        // If trainee has initialized skills but no progress, delete them to allow reinitializing
        const allTraineeSkills = await prisma.traineeSkill.findMany({
          where: { traineeId: userId }
        });

        if (allTraineeSkills.length > 0) {
          await prisma.traineeSkill.deleteMany({
            where: { traineeId: userId }
          });
        }
      }
    }

    if (Object.keys(allowedUpdates).length === 0) {
      return NextResponse.json({ message: 'No valid fields to update' }, { status: 400 });
    }

    // Prepare update data
    const updateData = { ...allowedUpdates };
    
    // If qualification route is being updated, ensure related fields are set
    if ('qualificationRoute' in allowedUpdates) {
      updateData.qualificationRouteDismissed = true;
      updateData.qualificationRouteUpdatedAt = new Date();
    }

    // Update the user with allowed fields
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: updateData,
      include: {
        mentor: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        },
        mentees: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        },
      }
    });

    // Remove sensitive information
    const userWithoutPassword = {
      ...updatedUser,
      password: undefined,
    };
    return NextResponse.json(userWithoutPassword);
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json({ error: 'Failed to update user' }, { status: 500 });
  }
} 