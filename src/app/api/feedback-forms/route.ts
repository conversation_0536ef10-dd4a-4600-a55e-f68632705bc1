import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { UserRole } from '@/types';
// api/feedback-forms
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session || session.user.role !== UserRole.MENTOR) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { menteeName, meetingDate, meetingId, candidateId } = await request.json();

    if (!menteeName || !meetingDate) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Case 1: Has meetingId - existing flow for completing meetings
    if (meetingId) {
      // Get meeting and check existing monthly review
      const meeting = await prisma.meeting.findUnique({
        where: { id: meetingId },
        include: {
          monthlyReview: {
            include: {
              feedbackForm: true
            }
          }
        }
      });

      if (!meeting) {
        return NextResponse.json(
          { error: 'Meeting not found' },
          { status: 404 }
        );
      }

      // If monthly review exists and has a feedback form, return it
      if (meeting.monthlyReview?.feedbackForm) {
        return NextResponse.json(meeting.monthlyReview.feedbackForm);
      }

      // If monthly review exists but no feedback form, create new feedback form
      if (meeting.monthlyReview) {
        const feedbackForm = await prisma.feedbackForm.create({
          data: {
            menteeName,
            meetingDate: new Date(meetingDate),
            coveringMonth: new Date(meetingDate).toLocaleString('default', { 
              month: 'long',
              year: 'numeric'
            }),
            monthlyReviewId: meeting.monthlyReview.id,
            updatedAt: new Date(),
          },
        });
        return NextResponse.json(feedbackForm);
      }

      // If no monthly review exists, create both
      const monthlyReview = await prisma.monthlyReview.create({
        data: {
          completionDate: new Date(),
          candidateId: meeting.candidateId,
          mentorId: meeting.mentorId,
          meetingId: meeting.id,
          feedbackForm: {
            create: {
              menteeName,
              meetingDate: new Date(meetingDate),
              coveringMonth: new Date(meetingDate).toLocaleString('default', { 
                month: 'long',
                year: 'numeric'
              }),
              updatedAt: new Date(),
            }
          }
        },
        include: {
          feedbackForm: true
        }
      });

      return NextResponse.json(monthlyReview.feedbackForm);
    }

    // Case 2: No meetingId - create temp feedback form for create mode
    if (!candidateId) {
      return NextResponse.json(
        { error: 'candidateId is required for temp feedback form' },
        { status: 400 }
      );
    }

    // Create temporary MonthlyReview without meetingId
    const tempMonthlyReview = await prisma.monthlyReview.create({
      data: {
        completionDate: new Date(),
        candidateId: candidateId,
        mentorId: session.user.id,
        // meetingId will be null for now
        feedbackForm: {
          create: {
            menteeName,
            meetingDate: new Date(meetingDate),
            coveringMonth: new Date(meetingDate).toLocaleString('default', { 
              month: 'long',
              year: 'numeric'
            }),
            updatedAt: new Date(),
          }
        }
      },
      include: {
        feedbackForm: true
      }
    });

    return NextResponse.json({
      ...tempMonthlyReview.feedbackForm,
      isTemp: true,
      monthlyReviewId: tempMonthlyReview.id
    });

  } catch (error) {
    console.error('Error creating feedback form:', error);
    return NextResponse.json(
      { error: 'Failed to create feedback form' },
      { status: 500 }
    );
  }
} 