import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { UserRole } from '@/types';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
    });
    // if not mentor or admin, return unauthorized
    console.log('user', user);
    if (!user || (user.role !== UserRole.MENTOR && user.role !== UserRole.ADMIN)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const feedbackForm = await prisma.feedbackForm.findUnique({
      where: { id },
      include: {
        monthlyReview: {
          include: {
            candidate: true,
            mentor: true,
          },
        },
      },
    });

    if (!feedbackForm) {
      return NextResponse.json({ error: 'Feedback form not found' }, { status: 404 });
    }

    // Allow access if user is admin, otherwise must be the assigned mentor
    if (user.role !== UserRole.ADMIN && feedbackForm.monthlyReview?.mentorId !== session.user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    return NextResponse.json(feedbackForm);
  } catch (error) {
    console.error('Error fetching feedback form:', error);
    return NextResponse.json(
      { error: 'Failed to fetch feedback form' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
    });

    if (!user || (user.role !== UserRole.MENTOR && user.role !== UserRole.ADMIN)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const feedbackForm = await prisma.feedbackForm.findUnique({
      where: { id },
      include: {
        monthlyReview: true,
      },
    });

    if (!feedbackForm) {
      return NextResponse.json({ error: 'Feedback form not found' }, { status: 404 });
    }

    if (user.role !== UserRole.ADMIN && feedbackForm.monthlyReview?.mentorId !== session.user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const values = await request.json();

    const updatedFeedbackForm = await prisma.feedbackForm.update({
      where: { id },
      data: {
        ...values,
        meetingDate: new Date(values.meetingDate),
      },
    });

    return NextResponse.json(updatedFeedbackForm);
  } catch (error) {
    console.error('Error updating feedback form:', error);
    return NextResponse.json(
      { error: 'Failed to update feedback form' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
    });

    if (!user || (user.role !== UserRole.MENTOR && user.role !== UserRole.ADMIN)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Find the feedback form with its monthly review
    const feedbackForm = await prisma.feedbackForm.findUnique({
      where: { id },
      include: {
        monthlyReview: true,
      },
    });

    if (!feedbackForm) {
      return NextResponse.json({ error: 'Feedback form not found' }, { status: 404 });
    }

    // Check if the user is authorized to delete this feedback form
    if (user.role !== UserRole.ADMIN && feedbackForm.monthlyReview?.mentorId !== session.user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Delete the feedback form
    await prisma.feedbackForm.delete({
      where: { id },
    });

    // If the monthly review exists and has no other references, delete it too
    if (feedbackForm.monthlyReview) {
      await prisma.monthlyReview.delete({
        where: { id: feedbackForm.monthlyReview.id },
      });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting feedback form:', error);
    return NextResponse.json(
      { error: 'Failed to delete feedback form' },
      { status: 500 }
    );
  }
} 