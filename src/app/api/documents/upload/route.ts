import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { uploadToS3, generateUniqueFileName, getContentType } from '@/lib/s3';
import { prisma } from '@/lib/prisma';

export async function POST(request: Request) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const entityType = formData.get('entityType') as string;
    const entityId = formData.get('entityId') as string;

    if (!file || !entityType || !entityId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate file type
    const contentType = getContentType(file.name);
    if (!contentType) {
      return NextResponse.json(
        { error: 'Invalid file type' },
        { status: 400 }
      );
    }

    // Generate unique filename
    const fileName = generateUniqueFileName(file.name);

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer());

    // Upload to S3
    const s3Key = await uploadToS3(buffer, fileName, contentType);

    // Create document record in database
    let updatedRecord;
    if (entityType === 'entry') {
      updatedRecord = await prisma.entry.update({
        where: { id: entityId },
        data: {
          documentKey: s3Key,
          documentName: file.name,
        },
      });
    } else if (entityType === 'placement') {
      updatedRecord = await prisma.placement.update({
        where: { id: entityId },
        data: {
          documentKey: s3Key,
          documentName: file.name,
        },
      });
    } else {
      return NextResponse.json({ error: 'Invalid entityType' }, { status: 400 });
    }

    return NextResponse.json({ document: updatedRecord });
  } catch (error) {
    console.error('Error uploading document:', error);
    return NextResponse.json(
      { error: 'Failed to upload document' },
      { status: 500 }
    );
  }
} 