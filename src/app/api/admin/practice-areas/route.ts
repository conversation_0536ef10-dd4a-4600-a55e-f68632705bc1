import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    const areas = await prisma.practiceArea.findMany({
      orderBy: { createdAt: 'desc' },
    });
    return NextResponse.json(areas);
  } catch (error) {
    console.error('Error fetching practice areas:', error);
    return NextResponse.json({ error: 'Failed to fetch practice areas' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const { name } = await req.json();
    if (!name || typeof name !== 'string') {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 });
    }
    const area = await prisma.practiceArea.create({
      data: { name },
    });
    return NextResponse.json(area);
  } catch (error) {
    console.error('Error creating practice area:', error);
    return NextResponse.json({ error: 'Failed to create practice area' }, { status: 500 });
  }
}

export async function PUT(req: NextRequest) {
  try {
    const { id, name } = await req.json();
    if (!id || !name) {
      return NextResponse.json({ error: 'ID and name are required' }, { status: 400 });
    }
    const area = await prisma.practiceArea.update({
      where: { id },
      data: { name },
    });
    return NextResponse.json(area);
  } catch (error) {
    console.error('Error updating practice area:', error);
    return NextResponse.json({ error: 'Failed to update practice area' }, { status: 500 });
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { id } = await req.json();
    if (!id) {
      return NextResponse.json({ error: 'ID is required' }, { status: 400 });
    }

    // Delete all related items in the correct order to respect foreign key constraints
    await prisma.$transaction(async (tx) => {
      // 1. First get all related skills to find their groups
      const relatedSkills = await tx.practiceSkill.findMany({
        where: { practiceAreaId: id },
        include: {
          practiceSkillGroups: {
            include: {
              practiceSubSkills: true
            }
          }
        }
      });

      // 2. Delete all sub-skills
      for (const skill of relatedSkills) {
        for (const group of skill.practiceSkillGroups) {
          await tx.practiceSubSkill.deleteMany({
            where: { practiceSkillGroupId: group.id }
          });
        }
      }

      // 3. Delete all skill groups
      for (const skill of relatedSkills) {
        await tx.practiceSkillGroup.deleteMany({
          where: { practiceSkillId: skill.id }
        });
      }

      // 4. Delete all skills
      await tx.practiceSkill.deleteMany({
        where: { practiceAreaId: id }
      });

      // 5. Finally delete the practice area
      await tx.practiceArea.delete({
        where: { id }
      });
    }, {
      timeout: 30000, // 30 seconds timeout
      maxWait: 10000, // 10 seconds max wait
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting practice area:', error);
    return NextResponse.json({ 
      error: 'Failed to delete practice area and associated items',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
