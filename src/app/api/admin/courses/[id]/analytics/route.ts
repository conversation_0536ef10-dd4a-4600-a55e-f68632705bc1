import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { UserRole } from '@/types';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: courseId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');

    const course = await prisma.course.findUnique({
      where: { id: courseId },
      include: {
        category: true,
        _count: {
          select: {
            courseClicks: true
          }
        }
      }
    });

    // Get paginated clicks
    const totalClicks = await prisma.courseClick.count({
      where: { courseId }
    });

    const courseClicks = await prisma.courseClick.findMany({
      where: { courseId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      },
      orderBy: {
        clickedAt: 'desc'
      },
      skip: (page - 1) * pageSize,
      take: pageSize
    });

    if (!course) {
      return NextResponse.json({ error: 'Course not found' }, { status: 404 });
    }

    const clicksByDay = await prisma.$queryRaw`
      SELECT 
        DATE("clickedAt") as date,
        COUNT(*) as clicks
      FROM "CourseClick"
      WHERE "courseId" = ${courseId}
      GROUP BY DATE("clickedAt")
      ORDER BY date DESC
      LIMIT 30
    `;

    const uniqueUsers = await prisma.courseClick.groupBy({
      by: ['userId'],
      where: {
        courseId
      },
      _count: {
        userId: true
      }
    });

    // Convert BigInt values to numbers for JSON serialization
    const serializedClicksByDay = (clicksByDay as any[]).map(item => ({
      date: item.date,
      clicks: Number(item.clicks)
    }));

    return NextResponse.json({
      course,
      analytics: {
        totalClicks: course._count.courseClicks,
        uniqueUsers: uniqueUsers.length,
        clicksByDay: serializedClicksByDay,
        clicks: courseClicks,
        pagination: {
          page,
          pageSize,
          total: totalClicks,
          totalPages: Math.ceil(totalClicks / pageSize)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching course analytics:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 