import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { UserRole } from '@/types';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const courses = await prisma.course.findMany({
      include: {
        category: true,
        secondCategories: {
          include: {
            category: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Get click counts for each course separately
    const coursesWithCounts = await Promise.all(
      courses.map(async (course) => {
        const clickCount = await prisma.courseClick.count({
          where: {
            courseId: course.id
          }
        });
        
        return {
          ...course,
          _count: {
            courseClicks: clickCount
          }
        };
      })
    );

    return NextResponse.json(coursesWithCounts);
  } catch (error) {
    console.error('Error fetching courses:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();

    const course = await prisma.course.create({
      data: {
        name: data.name,
        description: data.description,
        imageUrl: data.imageUrl,
        externalLink: data.externalLink,
        pricing: data.pricing,
        categoryId: data.categoryId,
        secondCategories: {
          create: data.secondCategoryIds?.map((categoryId: string) => ({
            categoryId: categoryId,
          })) || [],
        },
      },
      include: {
        category: true,
        secondCategories: {
          include: {
            category: true,
          },
        },
      },
    });

    return NextResponse.json(course);
  } catch (error) {
    console.error('Error creating course:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 