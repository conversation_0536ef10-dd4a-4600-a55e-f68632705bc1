import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { UserRole } from '@/types';
import { uploadToS3, generateUniqueFileName, getContentType, S3_FOLDERS } from '@/lib/s3';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const templates = await prisma.meetingTemplate.findMany({
      include: {
        uploader: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: [
        { isActive: 'desc' },
        { createdAt: 'desc' }
      ]
    });

    return NextResponse.json(templates);
  } catch (error) {
    console.error('Error fetching meeting templates:', error);
    return NextResponse.json(
      { error: 'Failed to fetch meeting templates' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const name = formData.get('name') as string;
    const description = formData.get('description') as string;
    const setAsActive = formData.get('setAsActive') === 'true';

    if (!file || !name) {
      return NextResponse.json(
        { error: 'Missing required fields: file and name' },
        { status: 400 }
      );
    }

    // Validate file type
    const contentType = getContentType(file.name);
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    if (!allowedTypes.includes(contentType)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only PDF, DOC, and DOCX files are allowed.' },
        { status: 400 }
      );
    }

    // Check file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'File size exceeds 10MB limit' },
        { status: 400 }
      );
    }

    // Generate unique filename for S3
    const fileName = generateUniqueFileName(file.name);
    const buffer = Buffer.from(await file.arrayBuffer());

    // Upload to S3
    const s3Key = await uploadToS3(buffer, fileName, contentType, S3_FOLDERS.TEMPLATES);

    // Get current version number
    const latestTemplate = await prisma.meetingTemplate.findFirst({
      orderBy: { version: 'desc' }
    });
    const nextVersion = (latestTemplate?.version || 0) + 1;

    // If setting as active, deactivate all other templates
    if (setAsActive) {
      await prisma.meetingTemplate.updateMany({
        where: { isActive: true },
        data: { isActive: false }
      });
    }

    // Create new template record
    const newTemplate = await prisma.meetingTemplate.create({
      data: {
        name,
        description,
        fileName: file.name,
        s3Key,
        version: nextVersion,
        isActive: setAsActive || false,
        uploadedBy: session.user.id
      },
      include: {
        uploader: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    return NextResponse.json(newTemplate);
  } catch (error) {
    console.error('Error uploading meeting template:', error);
    return NextResponse.json(
      { error: 'Failed to upload meeting template' },
      { status: 500 }
    );
  }
} 