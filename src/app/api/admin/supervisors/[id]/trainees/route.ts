import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { UserRole } from '@/types';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'all'; // 'current', 'past', or 'all'

    const whereClause: any = { supervisorId: id };
    
    if (type === 'current') {
      whereClause.isActive = true;
    } else if (type === 'past') {
      whereClause.isActive = false;
    }

    const trainees = await prisma.supervisorTraineeHistory.findMany({
      where: whereClause,
      include: {
        trainee: {
          select: {
            id: true,
            name: true,
            email: true,
            qualificationRoute: true,
            traineeLevel: true,
            updatedAt: true,
          }
        },
        placement: {
          select: {
            id: true,
            name: true,
            startDate: true,
            endDate: true,
            client: {
              select: {
                name: true
              }
            }
          }
        }
      },
      orderBy: type === 'past' ? { endDate: 'desc' } : { startDate: 'desc' }
    });

    const traineesWithProgress = trainees.map(async (history) => {
      const trainee = history.trainee;
      const placement = history.placement;
      
      const traineeSkills = await prisma.traineeSkill.findMany({
        where: { 
          traineeId: trainee.id,
          doneEntryCount: { gt: 0 }
        },
        select: {
          doneEntryCount: true,
          minSuggestedEntryCount: true,
          updatedAt: true
        }
      });

      const totalProgress = traineeSkills.reduce((sum, skill) => sum + skill.doneEntryCount, 0);
      const totalRequired = traineeSkills.reduce((sum, skill) => sum + skill.minSuggestedEntryCount, 0);
      const competencyProgress = totalRequired > 0 ? (totalProgress / totalRequired) * 100 : 0;

      const lastActivity = await prisma.entry.findFirst({
        where: {
          placement: {
            userId: trainee.id
          }
        },
        orderBy: {
          updatedAt: 'desc'
        },
        select: {
          updatedAt: true
        }
      });

      return {
        ...trainee,
        competencyProgress: Math.round(competencyProgress),
        lastActivity: lastActivity?.updatedAt || null,
        relationshipStartDate: history.startDate,
        relationshipEndDate: history.endDate,
        isActive: history.isActive,
        placement: placement
      };
    });

    const traineesData = await Promise.all(traineesWithProgress);

    if (type === 'all') {
      const current = traineesData.filter(trainee => trainee.isActive);
      const past = traineesData.filter(trainee => !trainee.isActive);
      return NextResponse.json({ current, past });
    }

    return NextResponse.json(traineesData);
  } catch (error) {
    console.error('Error fetching supervisor trainees:', error);
    return NextResponse.json(
      { error: 'Failed to fetch supervisor trainees' },
      { status: 500 }
    );
  }
}
