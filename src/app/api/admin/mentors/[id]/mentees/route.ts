import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { UserRole } from '@/types';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'all'; // 'current', 'past', or 'all'

    const whereClause: any = { mentorId: id };
    
    if (type === 'current') {
      whereClause.isActive = true;
    } else if (type === 'past') {
      whereClause.isActive = false;
    }

    const mentees = await prisma.mentorMenteeHistory.findMany({
      where: whereClause,
      include: {
        mentee: {
          select: {
            id: true,
            name: true,
            email: true,
            qualificationRoute: true,
            traineeLevel: true,
            updatedAt: true,
          }
        }
      },
      orderBy: type === 'past' ? { endDate: 'desc' } : { startDate: 'desc' }
    });

    const menteesWithProgress = mentees.map(async (history) => {
      const mentee = history.mentee;
      
      const traineeSkills = await prisma.traineeSkill.findMany({
        where: { 
          traineeId: mentee.id,
          doneEntryCount: { gt: 0 }
        },
        select: {
          doneEntryCount: true,
          minSuggestedEntryCount: true,
          updatedAt: true
        }
      });

      const totalProgress = traineeSkills.reduce((sum, skill) => sum + skill.doneEntryCount, 0);
      const totalRequired = traineeSkills.reduce((sum, skill) => sum + skill.minSuggestedEntryCount, 0);
      const competencyProgress = totalRequired > 0 ? (totalProgress / totalRequired) * 100 : 0;

      const lastActivity = await prisma.entry.findFirst({
        where: {
          placement: {
            userId: mentee.id
          }
        },
        orderBy: {
          updatedAt: 'desc'
        },
        select: {
          updatedAt: true
        }
      });

      return {
        ...mentee,
        competencyProgress: Math.round(competencyProgress),
        lastActivity: lastActivity?.updatedAt || null,
        relationshipStartDate: history.startDate,
        relationshipEndDate: history.endDate,
        isActive: history.isActive
      };
    });

    const menteesData = await Promise.all(menteesWithProgress);

    if (type === 'all') {
      const current = menteesData.filter(mentee => mentee.isActive);
      const past = menteesData.filter(mentee => !mentee.isActive);
      return NextResponse.json({ current, past });
    }

    return NextResponse.json(menteesData);
  } catch (error) {
    console.error('Error fetching mentor mentees:', error);
    return NextResponse.json(
      { error: 'Failed to fetch mentor mentees' },
      { status: 500 }
    );
  }
}
