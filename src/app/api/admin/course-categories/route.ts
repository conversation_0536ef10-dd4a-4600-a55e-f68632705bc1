import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { UserRole } from '@/types';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const categories = await prisma.courseCategory.findMany({
      orderBy: {
        name: 'asc',
      },
    });

    return NextResponse.json(categories);
  } catch (error) {
    console.error('Error fetching course categories:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();

    const category = await prisma.courseCategory.create({
      data: {
        name: data.name,
        description: data.description,
        color: data.color,
        imageUrl: data.imageUrl,
        isClassroom: data.isClassroom || false,
        isSecondCategory: data.isSecondCategory || false,
        faqs: data.faqs || [],
      },
    });

    return NextResponse.json(category);
  } catch (error) {
    console.error('Error creating course category:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}