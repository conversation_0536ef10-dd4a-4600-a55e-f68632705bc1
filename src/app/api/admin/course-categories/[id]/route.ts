import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { UserRole } from '@/types';

export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();
    const { id } = await params;

    const category = await prisma.courseCategory.update({
      where: { id },
      data: {
        name: data.name,
        description: data.description,
        color: data.color,
        imageUrl: data.imageUrl,
        isClassroom: data.isClassroom || false,
        isSecondCategory: data.isSecondCategory || false,
        faqs: data.faqs || [],
      },
    });

    return NextResponse.json(category);
  } catch (error) {
    console.error('Error updating course category:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;

    await prisma.courseCategory.delete({
      where: { id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting course category:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}