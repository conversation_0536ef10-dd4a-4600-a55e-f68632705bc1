import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const practiceSkillId = searchParams.get('practiceSkillId');
    const where = practiceSkillId ? { practiceSkillId } : {};
    const groups = await prisma.practiceSkillGroup.findMany({
      where,
      orderBy: { createdAt: 'desc' },
    });
    return NextResponse.json(groups);
  } catch {
    return NextResponse.json({ error: 'Failed to fetch practice skill groups' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const { name, practiceSkillId } = await req.json();
    if (!name || !practiceSkillId) {
      return NextResponse.json({ error: 'Name and practiceSkillId are required' }, { status: 400 });
    }
    const group = await prisma.practiceSkillGroup.create({
      data: { name, practiceSkillId },
    });
    return NextResponse.json(group);
  } catch {
    return NextResponse.json({ error: 'Failed to create practice skill group' }, { status: 500 });
  }
}

export async function PUT(req: NextRequest) {
  try {
    const { id, name } = await req.json();
    if (!id || !name) {
      return NextResponse.json({ error: 'ID and name are required' }, { status: 400 });
    }
    const group = await prisma.practiceSkillGroup.update({
      where: { id },
      data: { name },
    });
    return NextResponse.json(group);
  } catch {
    return NextResponse.json({ error: 'Failed to update practice skill group' }, { status: 500 });
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { id, confirmDelete } = await req.json();
    if (!id) {
      return NextResponse.json({ error: 'ID is required' }, { status: 400 });
    }

    const skillGroup = await prisma.practiceSkillGroup.findUnique({
      where: { id },
      include: {
        practiceSubSkills: {
          include: {
            entrySubSkills: true,
            traineeSkills: true
          }
        }
      }
    });

    if (!skillGroup) {
      return NextResponse.json({ error: 'Practice skill group not found' }, { status: 404 });
    }

    const subSkillCount = skillGroup.practiceSubSkills.length;
    let totalEntryCount = 0;
    let totalTraineeCount = 0;

    skillGroup.practiceSubSkills.forEach(subSkill => {
      totalEntryCount += subSkill.entrySubSkills.length;
      totalTraineeCount += subSkill.traineeSkills.length;
    });

    if (!confirmDelete) {
      return NextResponse.json({ 
        warning: `This action will permanently delete the skill group "${skillGroup.name}" and all ${subSkillCount} sub-skills within it. This will also remove them from ${totalEntryCount} entries and ${totalTraineeCount} trainees. This action cannot be undone.`,
        details: {
          subSkillCount,
          totalEntryCount,
          totalTraineeCount,
          skillGroupName: skillGroup.name
        },
        requiresConfirmation: true
      }, { status: 200 });
    }

    await prisma.$transaction(async (tx) => {
      // Collect all subSkill IDs first
      const subSkillIds = skillGroup.practiceSubSkills.map(subSkill => subSkill.id);

      // Delete all related records in parallel
      if (subSkillIds.length > 0) {
        await Promise.all([
          tx.entrySubSkill.deleteMany({ where: { subSkillId: { in: subSkillIds } } }),
          tx.traineeSkill.deleteMany({ where: { subSkillId: { in: subSkillIds } } })
        ]);
      }

      // Delete sub-skills
      await tx.practiceSubSkill.deleteMany({ where: { practiceSkillGroupId: id } });

      // Finally delete the skill group
      await tx.practiceSkillGroup.delete({ where: { id } });
    }, {
      timeout: 30000, // 30 seconds timeout
      maxWait: 10000, // 10 seconds max wait
    });

    return NextResponse.json({ 
      success: true,
      message: `Practice skill group "${skillGroup.name}" and all associated sub-skills deleted successfully`,
      deletedCounts: {
        subSkills: subSkillCount,
        entries: totalEntryCount,
        trainees: totalTraineeCount
      }
    });
  } catch (error) {
    console.error('Error deleting practice skill group:', error);
    return NextResponse.json({ 
      error: 'Failed to delete practice skill group',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
