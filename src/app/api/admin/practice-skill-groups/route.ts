import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const practiceSkillId = searchParams.get('practiceSkillId');
    const where = practiceSkillId ? { practiceSkillId } : {};
    const groups = await prisma.practiceSkillGroup.findMany({
      where,
      orderBy: { createdAt: 'desc' },
    });
    return NextResponse.json(groups);
  } catch {
    return NextResponse.json({ error: 'Failed to fetch practice skill groups' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const { name, practiceSkillId } = await req.json();
    if (!name || !practiceSkillId) {
      return NextResponse.json({ error: 'Name and practiceSkillId are required' }, { status: 400 });
    }
    const group = await prisma.practiceSkillGroup.create({
      data: { name, practiceSkillId },
    });
    return NextResponse.json(group);
  } catch {
    return NextResponse.json({ error: 'Failed to create practice skill group' }, { status: 500 });
  }
}

export async function PUT(req: NextRequest) {
  try {
    const { id, name } = await req.json();
    if (!id || !name) {
      return NextResponse.json({ error: 'ID and name are required' }, { status: 400 });
    }
    const group = await prisma.practiceSkillGroup.update({
      where: { id },
      data: { name },
    });
    return NextResponse.json(group);
  } catch {
    return NextResponse.json({ error: 'Failed to update practice skill group' }, { status: 500 });
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { id } = await req.json();
    if (!id) {
      return NextResponse.json({ error: 'ID is required' }, { status: 400 });
    }
    await prisma.practiceSkillGroup.delete({ where: { id } });
    return NextResponse.json({ success: true });
  } catch {
    return NextResponse.json({ error: 'Failed to delete practice skill group' }, { status: 500 });
  }
}
