import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session?.user || !session.user.isImpersonated) {
    return new NextResponse('Unauthorized', { status: 401 });
  }

  try {
    let impersonationId = session.user.impersonationId;
    
    try {
      const body = await request.json();
      if (body.impersonationId) {
        impersonationId = body.impersonationId;
      }
    } catch (e) {
      console.error('Error parsing request body:', e);
    }
    
    if (!impersonationId) {
      return NextResponse.json({ error: 'Impersonation ID not found' }, { status: 400 });
    }

    await prisma.adminImpersonation.update({
      where: { id: impersonationId },
      data: { endedAt: new Date() }
    });

    if (!session.user.impersonatedBy) {
      return NextResponse.json({ error: 'Impersonator information not found' }, { status: 400 });
    }

    const admin = await prisma.user.findUnique({
      where: { id: session.user.impersonatedBy.id },
    });

    if (!admin) {
      return NextResponse.json({ error: 'Admin not found' }, { status: 404 });
    }

    return NextResponse.json({ 
      success: true,
      adminId: admin.id
    });
  } catch (error) {
    console.error('Error ending impersonation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
