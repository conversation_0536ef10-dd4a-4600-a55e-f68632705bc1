import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { UserRole } from '@/types';

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session?.user || session.user.role !== UserRole.ADMIN) {
    return new NextResponse('Unauthorized', { status: 401 });
  }

  try {
    const { userId } = await request.json();
    
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    const targetUser = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        mentor: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });
    
    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const userAgent = request.headers.get('user-agent') || undefined;
    const ipAddress = request.headers.get('x-forwarded-for') || 
                      request.headers.get('x-real-ip') || 
                      '127.0.0.1';

    const impersonation = await prisma.adminImpersonation.create({
      data: {
        adminId: session.user.id,
        userId: targetUser.id,
        userRole: targetUser.role,
        reason: 'Admin impersonation',
        ipAddress: ipAddress.split(',')[0].trim(),
        userAgent,
      }
    });

    const impersonatedUser = {
      id: targetUser.id,
      name: targetUser.name || '',
      email: targetUser.email || '',
      role: targetUser.role,
      image: targetUser.image || undefined,
      phoneNumber: targetUser.phoneNumber || undefined,
      address: targetUser.address || undefined,
      qualificationRoute: targetUser.qualificationRoute || undefined,
      mentorId: targetUser.mentorId || undefined,
      mentor: targetUser.mentor ? {
        id: targetUser.mentor.id,
        name: targetUser.mentor.name,
        email: targetUser.mentor.email
      } : undefined,
      isImpersonated: true,
      impersonationId: impersonation.id,
      traineeLevel: targetUser.traineeLevel || undefined,
      isTrainingPrincipal: targetUser.isTrainingPrincipal || false,
      impersonatedBy: {
        id: session.user.id,
        name: session.user.name,
        email: session.user.email
      }
    };

    return NextResponse.json({ user: impersonatedUser });
  } catch (error) {
    console.error('Error impersonating user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
