import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { UserRole } from '@/types';

// Simple in-memory logging for admin activities
interface ActivityLogDetails {
  [key: string]: string | number | boolean | null | undefined;
}

const activityLogs: Array<{
  adminId: string;
  action: string;
  targetUserId?: string;
  details?: ActivityLogDetails;
  timestamp: Date;
}> = [];

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session?.user || session.user.role !== UserRole.ADMIN) {
    if (!session?.user?.isImpersonated || 
        !session.user.impersonatedBy || 
        typeof session.user.impersonatedBy === 'string') {
      return new NextResponse('Unauthorized', { status: 401 });
    }
    
    const impersonator = session.user.impersonatedBy as { id: string; name: string; email: string; role?: string };
    if (impersonator.role !== UserRole.ADMIN) {
      return new NextResponse('Unauthorized', { status: 401 });
    }
  }

  try {
    const body = await request.json();
    const { action, targetUserId, details } = body;

    if (!action) {
      return NextResponse.json({ error: 'Action is required' }, { status: 400 });
    }

    const adminId = session.user.isImpersonated && 
      session.user.impersonatedBy && 
      typeof session.user.impersonatedBy !== 'string' ? 
        session.user.impersonatedBy.id : 
        session.user.id;
        
    const activityLog = {
      adminId,
      action,
      targetUserId,
      details,
      timestamp: new Date()
    };
    
    activityLogs.push(activityLog);
    console.log('Admin activity logged:', activityLog);

    return NextResponse.json({ success: true, activityLog });
  } catch (error) {
    console.error('Error logging admin activity:', error);
    return NextResponse.json({ error: 'Failed to log activity' }, { status: 500 });
  }
}
