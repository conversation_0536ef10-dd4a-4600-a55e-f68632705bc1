import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const practiceAreaId = searchParams.get('practiceAreaId');
    const where = practiceAreaId ? { practiceAreaId } : {};
    const skills = await prisma.practiceSkill.findMany({
      where,
      orderBy: { createdAt: 'desc' },
    });
    return NextResponse.json(skills);
  } catch {
    return NextResponse.json({ error: 'Failed to fetch practice skills' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const { name, practiceAreaId } = await req.json();
    if (!name || !practiceAreaId) {
      return NextResponse.json({ error: 'Name and practiceAreaId are required' }, { status: 400 });
    }
    const skill = await prisma.practiceSkill.create({
      data: { name, practiceAreaId },
    });
    return NextResponse.json(skill);
  } catch {
    return NextResponse.json({ error: 'Failed to create practice skill' }, { status: 500 });
  }
}

export async function PUT(req: NextRequest) {
  try {
    const { id, name } = await req.json();
    if (!id || !name) {
      return NextResponse.json({ error: 'ID and name are required' }, { status: 400 });
    }
    const skill = await prisma.practiceSkill.update({
      where: { id },
      data: { name },
    });
    return NextResponse.json(skill);
  } catch {
    return NextResponse.json({ error: 'Failed to update practice skill' }, { status: 500 });
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { id } = await req.json();
    if (!id) {
      return NextResponse.json({ error: 'ID is required' }, { status: 400 });
    }
    await prisma.practiceSkill.delete({ where: { id } });
    return NextResponse.json({ success: true });
  } catch {
    return NextResponse.json({ error: 'Failed to delete practice skill' }, { status: 500 });
  }
}
