import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const practiceAreaId = searchParams.get('practiceAreaId');
    const where = practiceAreaId ? { practiceAreaId } : {};
    const skills = await prisma.practiceSkill.findMany({
      where,
      orderBy: { createdAt: 'desc' },
    });
    return NextResponse.json(skills);
  } catch {
    return NextResponse.json({ error: 'Failed to fetch practice skills' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const { name, practiceAreaId } = await req.json();
    if (!name || !practiceAreaId) {
      return NextResponse.json({ error: 'Name and practiceAreaId are required' }, { status: 400 });
    }
    const skill = await prisma.practiceSkill.create({
      data: { name, practiceAreaId },
    });
    return NextResponse.json(skill);
  } catch {
    return NextResponse.json({ error: 'Failed to create practice skill' }, { status: 500 });
  }
}

export async function PUT(req: NextRequest) {
  try {
    const { id, name } = await req.json();
    if (!id || !name) {
      return NextResponse.json({ error: 'ID and name are required' }, { status: 400 });
    }
    const skill = await prisma.practiceSkill.update({
      where: { id },
      data: { name },
    });
    return NextResponse.json(skill);
  } catch {
    return NextResponse.json({ error: 'Failed to update practice skill' }, { status: 500 });
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { id, confirmDelete } = await req.json();
    if (!id) {
      return NextResponse.json({ error: 'ID is required' }, { status: 400 });
    }

    const practiceSkill = await prisma.practiceSkill.findUnique({
      where: { id },
      include: {
        practiceSkillGroups: {
          include: {
            practiceSubSkills: {
              include: {
                entrySubSkills: true,
                traineeSkills: true
              }
            }
          }
        }
      }
    });

    if (!practiceSkill) {
      return NextResponse.json({ error: 'Practice skill not found' }, { status: 404 });
    }

    const skillGroupCount = practiceSkill.practiceSkillGroups.length;
    let totalSubSkillCount = 0;
    let totalEntryCount = 0;
    let totalTraineeCount = 0;

    practiceSkill.practiceSkillGroups.forEach(group => {
      totalSubSkillCount += group.practiceSubSkills.length;
      group.practiceSubSkills.forEach(subSkill => {
        totalEntryCount += subSkill.entrySubSkills.length;
        totalTraineeCount += subSkill.traineeSkills.length;
      });
    });

    if (!confirmDelete) {
      return NextResponse.json({ 
        warning: `This action will permanently delete the practice skill "${practiceSkill.name}" and all ${skillGroupCount} skill groups with ${totalSubSkillCount} sub-skills within it. This will also remove them from ${totalEntryCount} entries and ${totalTraineeCount} trainees. This action cannot be undone.`,
        details: {
          skillGroupCount,
          totalSubSkillCount,
          totalEntryCount,
          totalTraineeCount,
          practiceSkillName: practiceSkill.name
        },
        requiresConfirmation: true
      }, { status: 200 });
    }

    await prisma.$transaction(async (tx) => {
      // Collect all subSkill IDs first
      const allSubSkillIds = practiceSkill.practiceSkillGroups.flatMap(group => 
        group.practiceSubSkills.map(subSkill => subSkill.id)
      );

      // Delete all related records in parallel
      if (allSubSkillIds.length > 0) {
        await Promise.all([
          tx.entrySubSkill.deleteMany({ where: { subSkillId: { in: allSubSkillIds } } }),
          tx.traineeSkill.deleteMany({ where: { subSkillId: { in: allSubSkillIds } } })
        ]);
      }

      // Delete sub-skills
      await tx.practiceSubSkill.deleteMany({ 
        where: { 
          practiceSkillGroupId: { 
            in: practiceSkill.practiceSkillGroups.map(group => group.id) 
          } 
        } 
      });

      // Delete skill groups
      await tx.practiceSkillGroup.deleteMany({ where: { practiceSkillId: id } });

      // Finally delete the practice skill
      await tx.practiceSkill.delete({ where: { id } });
    }, {
      timeout: 30000, // 30 seconds timeout
      maxWait: 10000, // 10 seconds max wait
    });

    return NextResponse.json({ 
      success: true,
      message: `Practice skill "${practiceSkill.name}" and all associated skill groups and sub-skills deleted successfully`,
      deletedCounts: {
        skillGroups: skillGroupCount,
        subSkills: totalSubSkillCount,
        entries: totalEntryCount,
        trainees: totalTraineeCount
      }
    });
  } catch (error) {
    console.error('Error deleting practice skill:', error);
    return NextResponse.json({ 
      error: 'Failed to delete practice skill',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
