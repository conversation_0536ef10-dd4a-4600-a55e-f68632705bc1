import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const practiceSkillGroupId = searchParams.get('practiceSkillGroupId');
    
    if (!practiceSkillGroupId) {
      return NextResponse.json({ error: 'practiceSkillGroupId is required' }, { status: 400 });
    }

    const subSkills = await prisma.practiceSubSkill.findMany({
      where: { practiceSkillGroupId },
      orderBy: [
        { order: 'asc' },
        { createdAt: 'desc' }
      ]
    });
    
    return NextResponse.json(subSkills);
  } catch (error) {
    console.error('Error fetching practice sub-skills:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch practice sub-skills',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const { name, practiceSkillGroupId, practiceSubSkillType, minSuggestedEntryCount, order } = await req.json();
    if (!name || !practiceSkillGroupId || !practiceSubSkillType || minSuggestedEntryCount == null || order == null) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }
    const subSkill = await prisma.practiceSubSkill.create({
      data: { name, practiceSkillGroupId, practiceSubSkillType, minSuggestedEntryCount, order },
    });
    return NextResponse.json(subSkill);
  } catch {
    return NextResponse.json({ error: 'Failed to create practice sub-skill' }, { status: 500 });
  }
}

export async function PUT(req: NextRequest) {
  try {
    const { id, name, practiceSubSkillType, minSuggestedEntryCount, order } = await req.json();
    if (!id || !name || !practiceSubSkillType || minSuggestedEntryCount == null || order == null) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }
    const subSkill = await prisma.practiceSubSkill.update({
      where: { id },
      data: { name, practiceSubSkillType, minSuggestedEntryCount, order },
    });
    return NextResponse.json(subSkill);
  } catch {
    return NextResponse.json({ error: 'Failed to update practice sub-skill' }, { status: 500 });
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { id } = await req.json();
    if (!id) {
      return NextResponse.json({ error: 'ID is required' }, { status: 400 });
    }
    await prisma.practiceSubSkill.delete({ where: { id } });
    return NextResponse.json({ success: true });
  } catch {
    return NextResponse.json({ error: 'Failed to delete practice sub-skill' }, { status: 500 });
  }
}
