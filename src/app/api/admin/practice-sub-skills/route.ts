import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const practiceSkillGroupId = searchParams.get('practiceSkillGroupId');
    
    if (!practiceSkillGroupId) {
      return NextResponse.json({ error: 'practiceSkillGroupId is required' }, { status: 400 });
    }

    const subSkills = await prisma.practiceSubSkill.findMany({
      where: { practiceSkillGroupId },
      orderBy: [
        { order: 'asc' },
        { createdAt: 'desc' }
      ]
    });
    
    return NextResponse.json(subSkills);
  } catch (error) {
    console.error('Error fetching practice sub-skills:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch practice sub-skills',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const { name, practiceSkillGroupId, practiceSubSkillType, minSuggestedEntryCount, order } = await req.json();
    if (!name || !practiceSkillGroupId || !practiceSubSkillType || minSuggestedEntryCount == null || order == null) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }
    const subSkill = await prisma.practiceSubSkill.create({
      data: { name, practiceSkillGroupId, practiceSubSkillType, minSuggestedEntryCount, order },
    });
    return NextResponse.json(subSkill);
  } catch {
    return NextResponse.json({ error: 'Failed to create practice sub-skill' }, { status: 500 });
  }
}

export async function PUT(req: NextRequest) {
  try {
    const { id, name, practiceSubSkillType, minSuggestedEntryCount, order } = await req.json();
    if (!id || !name || !practiceSubSkillType || minSuggestedEntryCount == null || order == null) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }
    const subSkill = await prisma.practiceSubSkill.update({
      where: { id },
      data: { name, practiceSubSkillType, minSuggestedEntryCount, order },
    });
    return NextResponse.json(subSkill);
  } catch {
    return NextResponse.json({ error: 'Failed to update practice sub-skill' }, { status: 500 });
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { id, confirmDelete } = await req.json();
    if (!id) {
      return NextResponse.json({ error: 'ID is required' }, { status: 400 });
    }

    const subSkill = await prisma.practiceSubSkill.findUnique({
      where: { id },
      include: {
        entrySubSkills: true,
        traineeSkills: true
      }
    });

    if (!subSkill) {
      return NextResponse.json({ error: 'Practice sub-skill not found' }, { status: 404 });
    }

    const entryCount = subSkill.entrySubSkills.length;
    const traineeCount = subSkill.traineeSkills.length;

    if (!confirmDelete) {
      return NextResponse.json({ 
        warning: `This action will permanently delete the sub-skill and remove it from ${entryCount} entries and ${traineeCount} trainees. This action cannot be undone.`,
        details: {
          entryCount,
          traineeCount,
          subSkillName: subSkill.name
        },
        requiresConfirmation: true
      }, { status: 200 });
    }

    await prisma.$transaction(async (tx) => {
      // Delete all related records in parallel
      await Promise.all([
        tx.entrySubSkill.deleteMany({ where: { subSkillId: id } }),
        tx.traineeSkill.deleteMany({ where: { subSkillId: id } })
      ]);

      // Finally delete the sub-skill
      await tx.practiceSubSkill.delete({ where: { id } });
    }, {
      timeout: 30000, // 30 seconds timeout
      maxWait: 10000, // 10 seconds max wait
    });

    return NextResponse.json({ 
      success: true,
      message: `Practice sub-skill "${subSkill.name}" deleted successfully`,
      deletedCounts: {
        entries: entryCount,
        trainees: traineeCount
      }
    });
  } catch (error) {
    console.error('Error deleting practice sub-skill:', error);
    return NextResponse.json({ 
      error: 'Failed to delete practice sub-skill',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
