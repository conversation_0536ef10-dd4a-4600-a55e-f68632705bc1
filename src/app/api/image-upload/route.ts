import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { generateUniqueFileName, uploadToS3, getContentType, FileAccess, S3_FOLDERS } from "@/lib/s3";

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get("file") as File;
    const folder = formData.get("folder") as string || S3_FOLDERS.GENERAL;

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    const fileName = generateUniqueFileName(file.name);
    const contentType = getContentType(file.name);
    const isPublic = folder === S3_FOLDERS.COURSES || folder === S3_FOLDERS.CATEGORIES || folder === S3_FOLDERS.PROFILES || folder === S3_FOLDERS.AVATARS || folder === S3_FOLDERS.COMPETENCY_PLUS;
    const buffer = await file.arrayBuffer();
    const result = await uploadToS3(
      Buffer.from(buffer),
      fileName,
      contentType,
      folder,
      isPublic ? FileAccess.PUBLIC : FileAccess.PRIVATE
    );

    return NextResponse.json({ 
      success: true, 
      url: result 
    });
  } catch (error) {
    console.error("Error uploading file:", error);
    return NextResponse.json({ error: "Error uploading file" }, { status: 500 });
  }
}
