import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { MeetingStatus, UserRole, MeetingType } from '@/types';

export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    if (!session?.user || (session.user.role !== UserRole.MENTOR && session.user.role !== UserRole.SUPERVISOR)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { documentName, action, s3Key, feedbackFormId, documentType } = await request.json();

    const meeting = await prisma.meeting.findUnique({
      where: { id },
      include: {
        candidate: true,
        mentor: true,
      },
    });

    if (!meeting) {
      return NextResponse.json(
        { error: 'Meeting not found' },
        { status: 404 }
      );
    }

    if (meeting.mentorId !== session.user.id) {
      return NextResponse.json(
        { error: 'Unauthorized to complete this meeting' },
        { status: 403 }
      );
    }

    const existingMonthlyReview = await prisma.monthlyReview.findUnique({
      where: { meetingId: meeting.id }
    });

    const meetingDocumentType = documentType || (
      meeting.type === MeetingType.APPRAISAL ? 'APPRAISAL_FORM' :
      meeting.type === MeetingType.TPM ? 'TPM_MEETING_FORM' :
      'MONTHLY_REVIEW'
    );

    if (!existingMonthlyReview) {
      await prisma.monthlyReview.create({
        data: {
          documentName: documentName || undefined,
          completionDate: new Date(),
          action: action || undefined,
          s3Key: s3Key || undefined,
          candidateId: meeting.candidateId,
          mentorId: meeting.mentorId,
          meetingId: meeting.id,
          documentType: meetingDocumentType,
          referredToTP: action === 'referred',
          feedbackForm: meeting.type === MeetingType.MONTHLY && feedbackFormId ? {
            connect: {
              id: feedbackFormId
            }
          } : undefined
        },
      });
    } else {
      await prisma.monthlyReview.update({
        where: { id: existingMonthlyReview.id },
        data: {
          documentName: documentName || undefined,
          action: action || undefined,
          s3Key: s3Key || undefined,
          documentType: meetingDocumentType,
          referredToTP: action === 'referred',
          updatedAt: new Date(),
          feedbackForm: meeting.type === MeetingType.MONTHLY && feedbackFormId ? {
            connect: {
              id: feedbackFormId
            }
          } : undefined
        },
      });
    }

    const updatedMeeting = await prisma.meeting.update({
      where: { id },
      data: {
        status: MeetingStatus.COMPLETED,
      },
      include: {
        candidate: true,
        mentor: true,
        monthlyReview: {
          include: {
            feedbackForm: true,
          },
        },
      },
    });

    return NextResponse.json(updatedMeeting);
  } catch (error) {
    console.error('Error completing meeting:', error);
    return NextResponse.json(
      { error: 'Failed to complete meeting' },
      { status: 500 }
    );
  }
}
