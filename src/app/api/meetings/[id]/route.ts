import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";
import { Meeting, MeetingStatus } from "@/types";
import { sendMeetingAcceptedEmailsWithNotifications, sendMeetingRejectedEmailWithNotification } from "@/lib/email";
import { generateICSContent } from "@/lib/ics";

// Get single meeting
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    const { id } = await params;

    const meeting = await prisma.meeting.findUnique({
      where: { id },
      include: {
        availability: true,
        mentor: {
          select: {
            name: true,
            email: true,
          },
        },
        candidate: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!meeting) {
      return NextResponse.json({ error: "Meeting not found" }, { status: 404 });
    }

    // Check if user is involved in the meeting
    if (
      meeting.candidateId !== session.user.id &&
      meeting.mentorId !== session.user.id
    ) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    return NextResponse.json(meeting);
  } catch (error) {
    console.error("Error fetching meeting:", error);
    return NextResponse.json(
      { error: "Failed to fetch meeting" },
      { status: 500 }
    );
  }
}

// Update meeting status
export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { status, notes, meetingUrl, meetingLocation } = await request.json();

    const meeting = await prisma.meeting.findUnique({
      where: { id },
      include: { 
        availability: true,
        candidate: {
          select: {
            name: true,
            email: true,
          },
        },
        mentor: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!meeting) {
      return NextResponse.json({ error: "Meeting not found" }, { status: 404 });
    }

    // Only mentor can update status
    if (meeting.mentorId !== session.user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Update meeting
    const updatedMeeting = await prisma.meeting.update({
      where: { id },
      data: {
        status,
        notes: notes || meeting.notes,
        meetingUrl: meetingUrl || meeting.meetingUrl,
        meetingLocation: meetingLocation || meeting.meetingLocation,
      },
      include: {
        candidate: {
          select: {
            name: true,
            email: true,
          },
        },
        mentor: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    // If meeting is declined and has availability, free up the slot
    if (
      status === MeetingStatus.DECLINED &&
      meeting.availabilityId &&
      meeting.availability
    ) {
      await prisma.mentorAvailability.update({
        where: { id: meeting.availabilityId },
        data: { isBooked: false },
      });
    }

    // Send email notifications based on status change
    const baseUrl = process.env.NEXTAUTH_URL;
    if (status === MeetingStatus.ACCEPTED) {
      const icsContent = generateICSContent(meeting as Meeting);
      await sendMeetingAcceptedEmailsWithNotifications(
        updatedMeeting.mentor.email || '',
        updatedMeeting.mentor.name || '',
        updatedMeeting.candidate.email || '',
        updatedMeeting.candidate.name || '',
        new Date(meeting.proposedTime),
        `${baseUrl}`,
        icsContent
      );
    } else if (status === MeetingStatus.DECLINED) {
      await sendMeetingRejectedEmailWithNotification(
        updatedMeeting.candidate.email || '',
        updatedMeeting.candidate.name || '',
        updatedMeeting.mentor.name || '',
        new Date(meeting.proposedTime),
        `${baseUrl}`
      );
    }

    return NextResponse.json(updatedMeeting);
  } catch (error) {
    console.error("Error updating meeting:", error);
    return NextResponse.json(
      { error: "Failed to update meeting" },
      { status: 500 }
    );
  }
}

// Delete meeting
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const meeting = await prisma.meeting.findUnique({
      where: { id },
      include: { availability: true },
    });

    if (!meeting) {
      return NextResponse.json({ error: "Meeting not found" }, { status: 404 });
    }

    // Only allow deletion if user is the candidate and meeting is pending
    if (
      meeting.candidateId !== session.user.id ||
      meeting.status !== MeetingStatus.PENDING
    ) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // If meeting has availability, free up the slot
    if (meeting.availabilityId && meeting.availability) {
      await prisma.mentorAvailability.update({
        where: { id: meeting.availabilityId },
        data: { isBooked: false },
      });
    }

    await prisma.meeting.delete({
      where: { id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting meeting:", error);
    return NextResponse.json(
      { error: "Failed to delete meeting" },
      { status: 500 }
    );
  }
}

// Update meeting details
export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { candidateId, proposedTime, type, availabilityId } = body;

    // Validate required fields
    if (!candidateId || !proposedTime || !type) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    const meeting = await prisma.meeting.findUnique({
      where: { id },
      include: { 
        availability: true,
        candidate: true,
        mentor: true,
      },
    });

    if (!meeting) {
      return NextResponse.json({ error: "Meeting not found" }, { status: 404 });
    }

    // Only mentor can update meeting details
    if (meeting.mentorId !== session.user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Verify the candidate is actually a mentee of this mentor
    const mentee = await prisma.user.findFirst({
      where: { 
        id: candidateId,
        mentorId: session.user.id 
      }
    });
    
    if (!mentee) {
      return NextResponse.json(
        { error: "Selected user is not your mentee" },
        { status: 400 }
      );
    }

    // Check for existing meetings within 2 hours (excluding current meeting)
    const existingMeeting = await prisma.meeting.findFirst({
      where: {
        id: { not: id }, // Exclude current meeting
        type,
        mentorId: session.user.id,
        candidateId: candidateId,
        proposedTime: {
          gte: new Date(new Date(proposedTime).getTime() - 2 * 60 * 60 * 1000),
          lte: new Date(new Date(proposedTime).getTime() + 2 * 60 * 60 * 1000),
        },
      },
    });
    
    if (existingMeeting) {
      return NextResponse.json(
        { error: `Another ${type} already exists within 2 hours of the proposed time` },
        { status: 400 }
      );
    }

    // Update the meeting
    const updatedMeeting = await prisma.meeting.update({
      where: { id },
      data: {
        candidateId,
        proposedTime: new Date(proposedTime),
        type,
        availabilityId: availabilityId || null,
      },
      include: {
        candidate: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        mentor: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            isTrainingPrincipal: true
          },
        },
        availability: true,
        monthlyReview: {
          include: {
            feedbackForm: {
              select: {
                id: true,
                status: true,
                menteeName: true
              }
            }
          }
        }
      },
    });

    return NextResponse.json(updatedMeeting);
  } catch (error) {
    console.error("Error updating meeting:", error);
    return NextResponse.json(
      { error: "Failed to update meeting" },
      { status: 500 }
    );
  }
} 