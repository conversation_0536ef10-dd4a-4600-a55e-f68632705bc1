import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";
import { MeetingStatus } from "@/types";

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const startTime = searchParams.get("startTime");
    const endTime = searchParams.get("endTime");
    const mentorId = searchParams.get("mentorId");

    if (!startTime) {
      return NextResponse.json(
        { error: "startTime is required" },
        { status: 400 }
      );
    }

    // Base query to find conflicts
    const baseQuery = {
      OR: [
        { mentorId: session.user.id },
        { candidateId: session.user.id }
      ],
      NOT: {
        status: MeetingStatus.DECLINED
      },
      proposedTime: {
        gte: new Date() // Only return future meetings
      }
    };

    // If checking for a specific mentor
    if (mentorId) {
      baseQuery.OR = [
        { mentorId },
        { candidateId: session.user.id }
      ];
    }

    // If checking a time range
    if (startTime && endTime) {
      const conflicts = await prisma.meeting.findMany({
        where: {
          ...baseQuery,
          proposedTime: {
            ...baseQuery.proposedTime,
            gte: new Date(startTime),
            lte: new Date(endTime)
          }
        },
        include: {
          candidate: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          mentor: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
              isTrainingPrincipal: true
            },
          },
        }
      });

      return NextResponse.json({ conflicts });
    }

    // If checking a specific time (±2 hours)
    const startDateTime = new Date(startTime);
    const conflicts = await prisma.meeting.findMany({
      where: {
        ...baseQuery,
        proposedTime: {
          ...baseQuery.proposedTime,
          gte: new Date(startDateTime.getTime() - 2 * 60 * 60 * 1000),
          lte: new Date(startDateTime.getTime() + 2 * 60 * 60 * 1000),
        }
      },
      include: {
        candidate: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        mentor: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            isTrainingPrincipal: true
          },
        },
      }
    });

    return NextResponse.json({ conflicts });
  } catch (error) {
    console.error("Error checking meeting conflicts:", error);
    return NextResponse.json(
      { error: "Failed to check meeting conflicts" },
      { status: 500 }
    );
  }
} 