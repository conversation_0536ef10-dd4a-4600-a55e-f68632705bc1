import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";
import { MeetingStatus, MeetingType, UserRole } from "@/types";
import { sendMeetingRequestEmailsWithNotifications } from "@/lib/email";

// Get meetings
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get("type") as MeetingType | null;
    const excludeType = searchParams.get("excludeType") as MeetingType | null;
    const trainingPrincipal = searchParams.get("trainingPrincipal");
    const status = searchParams.get("status");
    const userId = searchParams.get("userId");

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const where: any = {};

    if (session.user.role === UserRole.ADMIN && userId) {
      where.OR = [
        { mentorId: userId },
        { candidateId: userId }
      ];
    } else if (session.user.role === UserRole.MENTOR || session.user.role === UserRole.SUPERVISOR) {
      where.mentorId = session.user.id;
    } else {
      where.candidateId = session.user.id;
    }

    if (type) {
      where.type = type;
    } else if (excludeType) {
      where.type = {
        not: excludeType
      };
    }

    if (status) {
      where.status = status;
    }

    // Add mentor filter if needed
    if (trainingPrincipal === "true") {
      where.mentor = {
        isTrainingPrincipal: true
      };
    } else if (trainingPrincipal === "false") {
      where.mentor = {
        isTrainingPrincipal: false
      };
    }

    const meetings = await prisma.meeting.findMany({
      where,
      include: {
        candidate: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        mentor: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            isTrainingPrincipal: true
          },
        },
        availability: true,
        monthlyReview: {
          include: {
            feedbackForm: {
              select: {
                id: true,
                status: true,
                menteeName: true
              }
            }
          }
        }
      },
      orderBy: { proposedTime: "desc" },
    });

    return NextResponse.json(meetings);
  } catch (error) {
    console.error("Error fetching meetings:", error);
    return NextResponse.json(
      { error: "Failed to fetch meetings" },
      { status: 500 }
    );
  }
}

// Create new meeting
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { mentorId, candidateId, proposedTime, type, reviewerRole, status, availabilityId, notes } = body;

    // Validate required fields
    if (!proposedTime || !type) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    let finalMentorId = mentorId;
    let finalCandidateId = candidateId;
    const finalStatus = status || MeetingStatus.PENDING;

    // Handle different user roles
    if (session.user.role === UserRole.MENTOR) {
      // Mentor creating a meeting
      if (!candidateId) {
        return NextResponse.json(
          { error: "candidateId is required when mentor creates meeting" },
          { status: 400 }
        );
      }
      finalMentorId = session.user.id;
      finalCandidateId = candidateId;
      
      // Verify the candidate is actually a mentee of this mentor
      const mentee = await prisma.user.findFirst({
        where: { 
          id: candidateId,
          mentorId: session.user.id 
        }
      });
      
      if (!mentee) {
        return NextResponse.json(
          { error: "Selected user is not your mentee" },
          { status: 400 }
        );
      }
    } else {
      // Trainee creating a meeting request
      if (!mentorId || !reviewerRole) {
        return NextResponse.json(
          { error: "mentorId and reviewerRole are required when trainee creates meeting" },
          { status: 400 }
        );
      }
      
      finalMentorId = mentorId;
      finalCandidateId = session.user.id;
      
      // For supervisor appraisals, verify the supervisor is currently supervising the trainee
      if (reviewerRole === UserRole.SUPERVISOR) {
        const currentPlacement = await prisma.placement.findFirst({
          where: {
            userId: session.user.id,
            supervisorId: mentorId
          }
        });

        if (!currentPlacement) {
          return NextResponse.json(
            { error: "Selected supervisor is not currently supervising you" },
            { status: 400 }
          );
        }
      }

      // For training principal appraisals, verify the mentor is a training principal
      if (reviewerRole === UserRole.MENTOR && type === MeetingType.APPRAISAL) {
        const mentor = await prisma.user.findUnique({
          where: { id: mentorId }
        });

        if (!mentor?.isTrainingPrincipal) {
          return NextResponse.json(
            { error: "Selected mentor is not a training principal" },
            { status: 400 }
          );
        }
      }
    }

    // Check for existing meetings within 2 hours (only for non-completed meetings)
    if (finalStatus !== MeetingStatus.COMPLETED) {
      const existingMeeting = await prisma.meeting.findFirst({
        where: {
          type,
          mentorId: finalMentorId,
          candidateId: finalCandidateId,
          proposedTime: {
            gte: new Date(new Date(proposedTime).getTime() - 2 * 60 * 60 * 1000),
            lte: new Date(new Date(proposedTime).getTime() + 2 * 60 * 60 * 1000),
          },
        },
      });
      
      if (existingMeeting) {
        return NextResponse.json(
          { error: `Another ${type} already exists within 2 hours of the proposed time` },
          { status: 400 }
        );
      }
    }

    // Create the meeting
    const meeting = await prisma.meeting.create({
      data: {
        mentorId: finalMentorId,
        candidateId: finalCandidateId,
        proposedTime: new Date(proposedTime),
        type,
        status: finalStatus,
        availabilityId,
        notes
      },
      include: {
        candidate: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        mentor: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            isTrainingPrincipal: true
          }
        }
      }
    });

    // Send email notifications only for pending meetings
    if (finalStatus === "PENDING") {
      await sendMeetingRequestEmailsWithNotifications(
        meeting.mentor.email || '',
        meeting.mentor.name || '',
        meeting.candidate.email || '',
        meeting.candidate.name || '',
        new Date(proposedTime),
        `${process.env.NEXTAUTH_URL}/mentor/meetings/requests`
      );
    }

    return NextResponse.json(meeting);
  } catch (error) {
    console.error("Error creating meeting:", error);
    return NextResponse.json(
      { error: "Failed to create meeting" },
      { status: 500 }
    );
  }
} 