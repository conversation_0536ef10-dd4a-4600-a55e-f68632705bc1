import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { generateResetToken } from '@/lib/utils';
import { sendEmail, EMAIL_TEMPLATES } from '@/lib/email';

export async function POST(request: Request) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      // For security reasons, we still return success even if user not found
      return NextResponse.json({
        message: 'If an account exists with this email, you will receive password reset instructions'
      });
    }

    // Generate reset token
    const resetToken = generateResetToken();
    const resetTokenExpiry = new Date(Date.now() + 3600000); // 1 hour from now

    // Save reset token to database
    await prisma.user.update({
      where: { id: user.id },
      data: {
        resetToken,
        resetTokenExpiry,
      },
    });

    // Send reset email
    const resetUrl = `${process.env.NEXTAUTH_URL}/reset-password?token=${resetToken}`;
    
    await EMAIL_TEMPLATES.RESET_PASSWORD(user.name || 'User', resetUrl)
      .then(template => sendEmail(email, template));

    return NextResponse.json({
      message: 'If an account exists with this email, you will receive password reset instructions'
    });
  } catch (error) {
    console.error('Error in forgot password:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
} 