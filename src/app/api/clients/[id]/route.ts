import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { UserRole } from '@/types';

// GET a specific client
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }>; }
) {
  const { id } = await params;
  const session = await getServerSession(authOptions);

  if (!session?.user || session.user.role !== UserRole.ADMIN) {
    return new NextResponse('Unauthorized', { status: 401 });
  }

  try {
    const client = await prisma.client.findUnique({
      where: { id: id },
    });

    if (!client) {
      return new NextResponse('Client not found', { status: 404 });
    }

    return NextResponse.json(client);
  } catch (error) {
    console.error('Error fetching client:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}

// PUT update a client
export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }>; }
) {
  const { id } = await params;
  const session = await getServerSession(authOptions);

  if (!session?.user || session.user.role !== UserRole.ADMIN) {
    return new NextResponse('Unauthorized', { status: 401 });
  }

  try {
    const body = await request.json();
    const { name, email, phone, address } = body;

    // Validate required fields
    if (!name || !email) {
      return NextResponse.json(
        { error: 'Name and email are required' },
        { status: 400 }
      );
    }

    // Check if client exists
    const existingClient = await prisma.client.findUnique({
      where: { id: id },
    });

    if (!existingClient) {
      return new NextResponse('Client not found', { status: 404 });
    }

    // Check if email is already taken by another client
    if (email !== existingClient.email) {
      const emailExists = await prisma.client.findFirst({
        where: { 
          email,
          NOT: {
            id: id
          }
        },
      });

      if (emailExists) {
        return NextResponse.json(
          { error: 'Email is already taken by another client' },
          { status: 400 }
        );
      }
    }

    // Update client
    const updatedClient = await prisma.client.update({
      where: { id: id },
      data: {
        name,
        email,
        phone,
        address,
      },
    });

    return NextResponse.json(updatedClient);
  } catch (error) {
    console.error('Error updating client:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE a client
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }>; }
) {
  const session = await getServerSession(authOptions);
  const { id } = await params;

  if (!session?.user || session.user.role !== UserRole.ADMIN) {
    return new NextResponse('Unauthorized', { status: 401 });
  }

  try {
    // Check if client exists with its placements
    const existingClient = await prisma.client.findUnique({
      where: { id },
      include: {
        placements: true,
        supervisors: true
      }
    });

    if (!existingClient) {
      return new NextResponse('Client not found', { status: 404 });
    }

    // Check if client has any placements
    if (existingClient.placements.length > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot delete client with existing placements. Please move or delete all placements first.',
          code: 'CLIENT_HAS_PLACEMENTS',
          placements: existingClient.placements 
        },
        { status: 400 }
      );
    }

    // Delete client supervisors first
    await prisma.clientSupervisor.deleteMany({
      where: { clientId: id }
    });

    // Delete client
    await prisma.client.delete({
      where: { id }
    });

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error('Error deleting client:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 