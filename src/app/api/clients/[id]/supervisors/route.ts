import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/prisma';
import { UserRole } from '@/types';
import { authOptions } from '@/lib/auth';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const clientWithSupervisors = await prisma.clientSupervisor.findMany({
      where: {
        clientId: id,
        supervisor: {
          role: UserRole.SUPERVISOR
        }
      },
      include: {
        supervisor: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    });

    if (!clientWithSupervisors.length) {
      const clientExists = await prisma.client.findUnique({
        where: { id }
      });

      if (!clientExists) {
        return NextResponse.json(
          { error: 'Client not found' },
          { status: 404 }
        );
      }
    }

    const supervisors = clientWithSupervisors.map((cs: any) => cs.supervisor);
    return NextResponse.json(supervisors);
  } catch (error) {
    console.error('Error fetching client supervisors:', error);
    return NextResponse.json(
      { error: 'Failed to fetch client supervisors' },
      { status: 500 }
    );
  }
}

// POST to assign supervisors to a client
export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const { supervisorIds } = await request.json();

    if (!Array.isArray(supervisorIds)) {
      return NextResponse.json(
        { error: 'supervisorIds must be an array' },
        { status: 400 }
      );
    }

    // Verify client exists
    const client = await prisma.client.findUnique({
      where: { id }
    });

    if (!client) {
      return NextResponse.json(
        { error: 'Client not found' },
        { status: 404 }
      );
    }

    // Verify all supervisors exist and have SUPERVISOR role
    const supervisors = await prisma.user.findMany({
      where: {
        id: { in: supervisorIds },
        role: UserRole.SUPERVISOR
      }
    });

    if (supervisors.length !== supervisorIds.length) {
      return NextResponse.json(
        { error: 'One or more invalid supervisor IDs' },
        { status: 400 }
      );
    }

    // Create client-supervisor relationships
    await prisma.clientSupervisor.createMany({
      data: supervisorIds.map(supervisorId => ({
        clientId: id,
        supervisorId,
      })),
      skipDuplicates: true,
    });

    // Return updated list of supervisors
    const updatedClient = await prisma.client.findUnique({
      where: { id },
      include: {
        supervisors: {
          include: {
            supervisor: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true
              }
            }
          }
        }
      }
    });

    const updatedSupervisors = updatedClient?.supervisors
      .map(cs => cs.supervisor)
      .filter(supervisor => supervisor.role === UserRole.SUPERVISOR) || [];

    return NextResponse.json(updatedSupervisors);
  } catch (error) {
    console.error('Error assigning supervisors to client:', error);
    return NextResponse.json(
      { error: 'Failed to assign supervisors' },
      { status: 500 }
    );
  }
}

// DELETE to remove supervisors from a client
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const { supervisorIds } = await request.json();

    if (!Array.isArray(supervisorIds)) {
      return NextResponse.json(
        { error: 'supervisorIds must be an array' },
        { status: 400 }
      );
    }

    // Verify client exists
    const client = await prisma.client.findUnique({
      where: { id }
    });

    if (!client) {
      return NextResponse.json(
        { error: 'Client not found' },
        { status: 404 }
      );
    }

    // Delete client-supervisor relationships
    await prisma.clientSupervisor.deleteMany({
      where: {
        clientId: id,
        supervisorId: { in: supervisorIds }
      }
    });

    // Return remaining supervisors
    const updatedClient = await prisma.client.findUnique({
      where: { id },
      include: {
        supervisors: {
          include: {
            supervisor: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true
              }
            }
          }
        }
      }
    });

    const remainingSupervisors = updatedClient?.supervisors
      .map(cs => cs.supervisor)
      .filter(supervisor => supervisor.role === UserRole.SUPERVISOR) || [];

    return NextResponse.json(remainingSupervisors);
  } catch (error) {
    console.error('Error removing supervisors from client:', error);
    return NextResponse.json(
      { error: 'Failed to remove supervisors' },
      { status: 500 }
    );
  }
} 