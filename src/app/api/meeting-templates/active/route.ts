import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { UserRole } from '@/types';
import { getSignedDownloadUrl } from '@/lib/s3';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only mentors and admins can access templates
    if (![UserRole.MENTOR, UserRole.ADMIN].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const activeTemplate = await prisma.meetingTemplate.findFirst({
      where: { isActive: true },
      include: {
        uploader: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    if (!activeTemplate) {
      return NextResponse.json({ error: 'No active template found' }, { status: 404 });
    }

    // Generate signed download URL
    const downloadUrl = await getSignedDownloadUrl(activeTemplate.s3Key, 3600); // 1 hour expiry

    return NextResponse.json({
      ...activeTemplate,
      downloadUrl
    });
  } catch (error) {
    console.error('Error fetching active meeting template:', error);
    return NextResponse.json(
      { error: 'Failed to fetch active meeting template' },
      { status: 500 }
    );
  }
} 