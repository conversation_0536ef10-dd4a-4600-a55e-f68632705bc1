import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getSignedDownloadUrl } from '@/lib/s3';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ key: string }>; }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { key } = await params;
    if (!key) {
      return NextResponse.json({ error: 'Missing file key' }, { status: 400 });
    }

    const { searchParams } = new URL(request.url);
    const expiresIn = parseInt(searchParams.get('expiresIn') || '3600', 10);

    const url = await getSignedDownloadUrl(key, expiresIn);

    return NextResponse.json({ 
      url,
      expires: new Date(Date.now() + expiresIn * 1000).toISOString()
    });
  } catch (error) {
    console.error('Error generating signed URL:', error);
    return NextResponse.json(
      { error: 'Failed to generate file URL' },
      { status: 500 }
    );
  }
} 