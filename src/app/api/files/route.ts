import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getSignedDownloadUrl } from '@/lib/s3';

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get document key from request body
    const body = await request.json();
    const { documentKey } = body;
    
    if (!documentKey) {
      return NextResponse.json({ error: 'Missing document key' }, { status: 400 });
    }

    const expiresIn = 3600; // 1 hour

    // The documentKey might be a full path like /entries/filename.pdf
    // We need to remove the leading / if it exists
    const key = documentKey.startsWith('/') ? documentKey.substring(1) : documentKey;

    const url = await getSignedDownloadUrl(key, expiresIn);

    return NextResponse.json({ 
      url,
      expires: new Date(Date.now() + expiresIn * 1000).toISOString()
    });
  } catch (error) {
    console.error('Error generating signed URL:', error);
    return NextResponse.json(
      { error: 'Failed to generate file URL' },
      { status: 500 }
    );
  }
} 