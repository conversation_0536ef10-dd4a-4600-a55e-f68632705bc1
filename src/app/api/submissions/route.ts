import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { UserRole } from '@/types';
import { sendSubmissionNotificationsWithNotifications } from '@/lib/email';
import dayjs from 'dayjs';

// GET submissions (filtered by role)
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const offset = parseInt(url.searchParams.get('offset') || '0');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const placementId = url.searchParams.get('placementId');
    const queryUserId = url.searchParams.get('userId');

    let whereClause: Record<string, any> = {};
    const { id: userId, role } = session.user;

    // Handle specific user ID query (only for admins)
    if (queryUserId) {
      whereClause = { traineeId: queryUserId, reviewerId: userId };
    } else {
      // Regular role-based filtering
      switch (role) {
        case UserRole.ADMIN:
          // Admin sees all
          break;
        case UserRole.TRAINEE:
          whereClause = { traineeId: userId };
          break;
        case UserRole.MENTOR:
          whereClause = { reviewerId: userId };
          break;
        case UserRole.SUPERVISOR:
          // Supervisor only sees submissions sent to them as reviewer
          whereClause = { reviewerId: userId };
          break;
        default:
          // Unknown role or role that shouldn't fetch submissions
          return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }
    }

    // Add placement filter if provided
    if (placementId) {
      whereClause = {
        ...whereClause,
        placementId: placementId
      };
    }

    // Count total submissions for pagination
    const total = await prisma.submission.count({
      where: whereClause,
    });

    // Fetch submissions with pagination
    const submissions = await prisma.submission.findMany({
      where: whereClause,
      include: {
        entries: {
          include: {
            placement: {
              include: {
                client: true,
              },
            },
            entrySubSkills: {
              include: {
                subSkill: true,
              },
            },
          },
        },
        entrySubmissions: {
          include: {
            entry: {
              include: {
                placement: {
                  include: {
                    client: true,
                  },
                },
                entrySubSkills: {
                  include: {
                    subSkill: true,
                  },
                },
              },
            },
          },
        },
        trainee: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        reviewer: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        ...(role === UserRole.SUPERVISOR || role === UserRole.ADMIN ? {
          supervisor: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        } : {}),
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip: offset,
      take: limit,
    });

    return NextResponse.json({
      submissions: {
        edges: submissions,
        pageInfo: {
          total,
          offset,
          limit,
        },
      },
    });
  } catch (error) {
    console.error('Error fetching submissions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch submissions' },
      { status: 500 }
    );
  }
}

// POST create a new submission
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || session.user.role !== UserRole.TRAINEE) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { entryIds, placementId, startDate, endDate, reviewerId, title, reviewerRole } = body;

    // Validate required fields
    if (!entryIds || !entryIds.length || !placementId || !startDate || !endDate || !reviewerId || !title) {
      return NextResponse.json(
        { error: 'Missing required fields: entryIds, placementId, startDate, endDate, reviewerId, and title are required' },
        { status: 400 }
      );
    }

    // Verify that the placement exists and belongs to the trainee
    const placement = await prisma.placement.findFirst({
      where: {
        id: placementId,
        userId: session.user.id,
      },
      include: {
        supervisor: true,
        mentor: true,
      },
    });

    if (!placement) {
      return NextResponse.json(
        { error: 'Placement not found or does not belong to you' },
        { status: 400 }
      );
    }

    const trainee = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: { mentor: true }
    });

    const isValidReviewer =
      (placement.supervisorId === reviewerId) ||
      (trainee?.mentor?.id === reviewerId);

    if (!isValidReviewer) {
      return NextResponse.json(
        { error: 'Reviewer must be either the supervisor of the placement or your mentor' },
        { status: 400 }
      );
    }

    // Verify that all entries belong to the trainee and to the specified placement
    const entries = await prisma.entry.findMany({
      where: {
        id: { in: entryIds },
        placement: {
          id: placementId,
          userId: session.user.id,
        },
      },
    });

    if (entries.length !== entryIds.length) {
      return NextResponse.json(
        { error: 'One or more entries do not belong to you, do not exist, or are not part of the specified placement' },
        { status: 400 }
      );
    }

    // Verify that all entries are in draft status
    const nonDraftEntries = entries.filter(entry => entry.status !== 'draft');
    if (nonDraftEntries.length > 0) {
      return NextResponse.json(
        { error: 'One or more entries are not in draft status and cannot be submitted' },
        { status: 400 }
      );
    }

    // Create submission
    const submission = await prisma.submission.create({
      data: {
        title,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        status: 'pending',
        reviewerRole: reviewerRole || null,
        placementId: placementId,
        trainee: {
          connect: { id: session.user.id }
        },
        reviewer: {
          connect: { id: reviewerId }
        },
        ...(placement.supervisorId ? {
          supervisor: {
            connect: { id: placement.supervisorId }
          }
        } : {})
      },
    });

    // Create EntrySubmission records for many-to-many relationship
    await Promise.all(entryIds.map(async (entryId: string) => {
      await prisma.entrySubmission.create({
        data: {
          entryId: entryId,
          submissionId: submission.id,
          status: 'submitted',
          submittedAt: new Date(),
        },
      });
    }));

    // Keep backward compatibility: Update entries with submission ID for existing logic
    await Promise.all(entryIds.map(async (entryId: string) => {
      await prisma.entry.update({
        where: { id: entryId },
        data: {
          submissionId: submission.id,
          submittedAt: new Date(),
        },
      });
    }));

    // Send notification to reviewer if specified
    if (placement.supervisorId || trainee?.mentor) {
      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
      const viewLink = `${baseUrl}/signoff-generator/${submission.id}`;
      const approveLink = `${baseUrl}/supervisor/submissions/${submission.id}`;

      try {
        await sendSubmissionNotificationsWithNotifications(
          session.user.name!,
          session.user.email!,
          placement.supervisor?.email || null,
          placement.supervisor?.name || null,
          trainee?.mentor?.email || null,
          trainee?.mentor?.name || null,
          viewLink,
          approveLink,
          dayjs(startDate).format('DD/MM/YYYY'),
          dayjs(endDate).format('DD/MM/YYYY'),
          reviewerRole
        );
      } catch (emailError) {
        console.error('Error sending notification emails:', emailError);
        // Don't fail the request if email sending fails
      }
    }

    return NextResponse.json({
      success: true,
      submission: {
        id: submission.id,
        title: submission.title,
        startDate: submission.startDate,
        endDate: submission.endDate,
        status: submission.status,
        entryIds,
      },
    });
  } catch (error) {
    console.error('Error creating submission:', error);
    return NextResponse.json(
      { error: 'Failed to create submission' },
      { status: 500 }
    );
  }
}
