import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { UserRole } from '@/types';

// POST withdraw a submission
export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }>; }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== UserRole.TRAINEE) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Check if submission exists and belongs to the trainee
    const submission = await prisma.submission.findFirst({
      where: {
        id: id,
        traineeId: session.user.id,
        status: 'pending', // Can only withdraw pending submissions
      },
      include: {
        entries: true,
      },
    });
    
    if (!submission) {
      return NextResponse.json(
        { error: 'Submission not found, does not belong to you, or cannot be withdrawn' },
        { status: 404 }
      );
    }
    
    // Delete the submission
    await prisma.submission.delete({
      where: { id: id },
    });
    
    // Update all entries in the submission back to draft status
    await Promise.all(submission.entries.map(async (entry) => {
      await prisma.entry.update({
        where: { id: entry.id },
        data: {
          status: 'draft',
          submissionId: null,
          submittedAt: null,
        },
      });
    }));
    
    // Send notification to reviewer if specified
    if (submission.reviewerId) {
      // In a real implementation, we would send an email notification here
      console.log(`Notification: Submission withdrawn by ${session.user.name}`);
    }
    
    return NextResponse.json({
      success: true,
      message: 'Submission withdrawn successfully',
    });
  } catch (error) {
    console.error('Error withdrawing submission:', error);
    return NextResponse.json(
      { error: 'Failed to withdraw submission' },
      { status: 500 }
    );
  }
}
