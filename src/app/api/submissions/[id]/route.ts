import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { UserRole } from '@/types';

// GET a specific submission
export async function GET(
  request: Request,
  context: { params: Promise<{ id: string }>; }
) {
  try {
    const session = await getServerSession(authOptions);
    const { id } = await context.params;

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check access permission for the submission
    const whereClause: Record<string, any> = {
      id,
    };

    // If not ADMIN, check role-based permissions
    if (session.user.role !== UserRole.ADMIN) {
      switch (session.user.role) {
        case UserRole.TRAINEE:
          whereClause.traineeId = session.user.id;
          break;
        case UserRole.MENTOR:
          whereClause.reviewerId = session.user.id;
          break;
        case UserRole.SUPERVISOR:
          whereClause.OR = [
            { reviewerId: session.user.id },
            { supervisorId: session.user.id },
            {
              entries: {
                some: {
                  placement: {
                    supervisorId: session.user.id
                  }
                }
              }
            }
          ];
          break;
      }
    }

    const submission = await prisma.submission.findFirst({
      where: whereClause,
      include: {
        entries: {
          include: {
            placement: {
              include: {
                client: true,
                user: true,
              },
            },
            entrySubSkills: {
              include: {
                subSkill: {
                  include: {
                    practiceSkillGroup: {
                      include: {
                        practiceSkill: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
        trainee: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        reviewer: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        supervisor: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!submission) {
      return NextResponse.json(
        { error: 'Submission not found or you do not have permission to access it' },
        { status: 404 }
      );
    }

    return NextResponse.json(submission);
  } catch (error) {
    console.error('Error fetching submission:', error);
    return NextResponse.json(
      { error: 'Failed to fetch submission' },
      { status: 500 }
    );
  }
}

// PUT update a submission (approve/reject)
export async function PUT(
  request: Request,
  context: { params: Promise<{ id: string }>; }
) {
  try {
    const session = await getServerSession(authOptions);
    const { id } = await context.params;
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only allow supervisor and admin to approve/reject submissions
    if (session.user.role !== UserRole.SUPERVISOR && session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Only supervisors can approve/reject submissions' }, { status: 403 });
    }

    const body = await request.json();
    const { status } = body;

    // Validate status
    if (!status || !['approved', 'rejected', 'pending'].includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status. Status must be "approved", "rejected", or "pending"' },
        { status: 400 }
      );
    }

    // Check if user is the reviewer or admin
    const submission = await prisma.submission.findUnique({
      where: { id },
      include: {
        entries: {
          include: {
            placement: {
              include: {
                client: true,
                user: true,
              },
            },
            entrySubSkills: {
              include: {
                subSkill: {
                  include: {
                    practiceSkillGroup: {
                      include: {
                        practiceSkill: true,
                      },
                    },
                  },
                },
              },
            },
          }
        },
        trainee: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!submission) {
      return NextResponse.json(
        { error: 'Submission not found' },
        { status: 404 }
      );
    }
    // Check if user has permission to approve/reject
    const hasPermission = 
      session.user.role === UserRole.ADMIN || 
      (session.user.role === UserRole.SUPERVISOR && (
        submission.reviewerId === session.user.id ||
        submission.supervisorId === session.user.id ||
        submission.entries.some(entry => entry.placement.supervisorId === session.user.id)
      ));

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'You are not authorized to review this submission' },
        { status: 403 }
      );
    }

    // Validate submission can be updated
    if (status === submission.status) {
      return NextResponse.json(
        { error: `Submission is already ${status}` },
        { status: 400 }
      );
    }

    // Use a transaction to ensure all updates are atomic
    const result = await prisma.$transaction(async (tx) => {
      // Update submission status
      const updatedSubmission = await tx.submission.update({
        where: { id },
        data: {
          status,
        },
      });

      // Update all entries in the submission
      let entryStatus;
      const entryData: {
        reviewedAt: Date;
        reviewerId: string;
        status?: string;
        feedback?: string;
      } = {
        reviewedAt: new Date(),
        reviewerId: session.user.id,
      };

      switch (status) {
        case 'approved':
          entryStatus = 'signedoff';
          break;
        case 'rejected':
          entryStatus = 'rejected';
          break;
        case 'pending':
          entryStatus = 'submitted';
          break;
      }

      entryData.status = entryStatus;

      await tx.entry.updateMany({
        where: { submissionId: id },
        data: entryData,
      });

      // If entries are approved, update trainee skills
      if (status === 'approved') {
        const traineeId = submission.trainee.id;

        // For each entry, update the trainee skills for associated skills
        const skillUpdates = submission.entries.flatMap(entry => 
          entry.entrySubSkills.map(skill => ({
            traineeId,
            subSkillId: skill.subSkill.id
          }))
        );

        // Batch update trainee skills
        if (skillUpdates.length > 0) {
          await Promise.all(skillUpdates.map(update =>
            tx.traineeSkill.upsert({
              where: {
                traineeId_subSkillId: {
                  traineeId: update.traineeId,
                  subSkillId: update.subSkillId
                }
              },
              update: {
                doneEntryCount: {
                  increment: 1
                },
                updatedAt: new Date()
              },
              create: {
                traineeId: update.traineeId,
                subSkillId: update.subSkillId,
                doneEntryCount: 1,
                minSuggestedEntryCount: 1
              }
            })
          ));
        }
      }

      return updatedSubmission;
    }, {
      timeout: 60000 // Set timeout to 60 seconds
    });

    // Send notification to trainee
    // In a real implementation, we would send an email notification here
    console.log(`Notification: Your submission has been ${status} by ${session.user.name}`);

    return NextResponse.json({
      success: true,
      submission: result,
    });
  } catch (error) {
    console.error('Error updating submission:', error);
    return NextResponse.json(
      { error: 'Failed to update submission' },
      { status: 500 }
    );
  }
}
