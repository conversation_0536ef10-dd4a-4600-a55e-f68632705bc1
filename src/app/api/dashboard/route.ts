import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.id;

    // Get trainee skills
    const traineeSkills = await prisma.traineeSkill.findMany({
      where: { traineeId: userId }
    });

    // Fetch all required data in parallel
    const [qualifications, characterSuitability, practiceAreas] = await Promise.all([
      // Get qualifications
      prisma.qualification.findMany({
        where: {
          userId: userId
        }
      }),

      // Get character suitability with user completion
      prisma.characterSuitability.findFirst({
        include: {
          userCompletions: {
            where: {
              userId: userId
            }
          }
        }
      }),

      // Get skills progress
      prisma.practiceArea.findMany({
        where: {
          name: {
            contains: session.user.qualificationRoute,
            mode: 'insensitive'
          }
        },
        include: {
          practiceSkills: {
            include: {
              practiceSkillGroups: {
                include: {
                  practiceSubSkills: true
                }
              }
            }
          }
        }
      })
    ]);

    // Format practice areas with progress
    const formattedAreas = practiceAreas.map(area => ({
      ...area,
      practiceSkills: area.practiceSkills.map(skill => ({
        ...skill,
        practiceSkillGroups: skill.practiceSkillGroups.map(group => ({
          ...group,
          practiceSubSkills: group.practiceSubSkills.map(subSkill => {
            const traineeSkill = traineeSkills.find(ts => ts.subSkillId === subSkill.id);
            return {
              ...subSkill,
              doneEntryCount: traineeSkill?.doneEntryCount || 0
            };
          })
        }))
      }))
    }));

    // Get all signed off entries for the trainee
    const signedOffEntries = await prisma.entry.findMany({
      where: {
        placement: {
          userId: userId
        },
        status: 'signedoff'
      },
      select: {
        startDate: true,
        endDate: true
      }
    });

    // Calculate total days from signed off entries
    const totalDays = signedOffEntries.reduce((total: number, entry: { startDate: Date; endDate: Date }) => {
      const startDate = new Date(entry.startDate);
      const endDate = new Date(entry.endDate);
      const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return total + diffDays;
    }, 0);

    return NextResponse.json({
      qualifications,
      characterSuitability,
      skillsProgress: {
        practiceAreas: formattedAreas,
        totalDays
      }
    });

  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
} 