import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// Mock practice areas for development
const mockPracticeAreas = [
  { id: 1, name: 'Civil Litigation', description: 'Civil litigation practice area' },
  { id: 2, name: 'Commercial Law', description: 'Commercial law practice area' },
  { id: 3, name: 'Corporate Law', description: 'Corporate law practice area' },
  { id: 4, name: 'Criminal Law', description: 'Criminal law practice area' },
  { id: 5, name: 'Employment Law', description: 'Employment law practice area' },
  { id: 6, name: 'Family Law', description: 'Family law practice area' },
  { id: 7, name: 'Immigration Law', description: 'Immigration law practice area' },
  { id: 8, name: 'Intellectual Property', description: 'Intellectual property practice area' },
  { id: 9, name: 'Property Law', description: 'Property law practice area' },
  { id: 10, name: 'Tax Law', description: 'Tax law practice area' },
];

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Try to get practice areas from database
    const practiceAreas = await prisma.practiceArea.findMany({
      orderBy: {
        name: 'asc',
      },
    });
    
    // If no practice areas in database, return mock data
    if (practiceAreas.length === 0) {
      return NextResponse.json(mockPracticeAreas);
    }
    
    return NextResponse.json(practiceAreas);
  } catch (error) {
    console.error('Error fetching practice areas:', error);
    
    // Return mock data in case of error
    return NextResponse.json(mockPracticeAreas);
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const data = await request.json();
    
    if (!data.name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }
    
    const practiceArea = await prisma.practiceArea.create({
      data: {
        name: data.name,
      },
    });
    
    return NextResponse.json(practiceArea);
  } catch (error) {
    console.error('Error creating practice area:', error);
    return NextResponse.json(
      { error: 'Failed to create practice area' },
      { status: 500 }
    );
  }
}
