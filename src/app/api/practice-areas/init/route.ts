import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function POST() {
  try {
    // Skip authentication in development
    if (process.env.NODE_ENV !== 'development') {
      const session = await getServerSession(authOptions);
      if (!session?.user || session.user.role !== 'ADMIN') {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
    }

    // Create default practice areas if they don't exist
    const practiceAreas = await Promise.all([
      prisma.practiceArea.create({
        data: {
          name: 'Solicitors Qualifying Examination (SQE)'
        }
      }),
      prisma.practiceArea.create({
        data: {
          name: 'Training Contract (TC)'
        }
      })
    ]);

    return NextResponse.json({ 
      success: true,
      practiceAreas
    });

  } catch (error) {
    console.error('Error initializing practice areas:', error);
    return NextResponse.json({ 
      error: 'Failed to initialize practice areas',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 