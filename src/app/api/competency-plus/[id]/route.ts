import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { UserRole } from '@/types';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const competencyPlus = await prisma.competencyPlus.findUnique({
      where: { id }
    });

    if (!competencyPlus) {
      return NextResponse.json({ error: 'Not found' }, { status: 404 });
    }

    // For non-admin users, only return published items
    if (session.user.role !== UserRole.ADMIN && !competencyPlus.published) {
      return NextResponse.json({ error: 'Not found' }, { status: 404 });
    }

    return NextResponse.json(competencyPlus);
  } catch (error) {
    console.error('Error fetching competency plus item:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();
    
    const competencyPlus = await prisma.competencyPlus.update({
      where: { id },
      data: {
        title: data.title,
        shortDescription: data.shortDescription,
        content: data.content,
        externalLinks: data.externalLinks || [],
        audioUrl: data.audioUrl,
        videoUrl: data.videoUrl,
        bannerImageUrl: data.bannerImageUrl,
        published: data.published,
        publishedAt: data.published && !data.wasPublished ? new Date() : data.publishedAt,
      }
    });

    return NextResponse.json(competencyPlus);
  } catch (error) {
    console.error('Error updating competency plus item:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await prisma.competencyPlus.delete({
      where: { id }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting competency plus item:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 