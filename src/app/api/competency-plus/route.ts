import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { UserRole } from '@/types';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // For admin, return all items. For trainee, return only published items
    const competencyPlusItems = await prisma.competencyPlus.findMany({
      where: session.user.role === UserRole.ADMIN ? {} : { published: true },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return NextResponse.json(competencyPlusItems);
  } catch (error) {
    console.error('Error fetching competency plus items:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();
    
    const competencyPlus = await prisma.competencyPlus.create({
      data: {
        title: data.title,
        shortDescription: data.shortDescription,
        content: data.content,
        externalLinks: data.externalLinks || [],
        audioUrl: data.audioUrl,
        videoUrl: data.videoUrl,
        bannerImageUrl: data.bannerImageUrl,
        published: data.published || false,
        publishedAt: data.published ? new Date() : null,
      }
    });

    return NextResponse.json(competencyPlus);
  } catch (error) {
    console.error('Error creating competency plus item:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 