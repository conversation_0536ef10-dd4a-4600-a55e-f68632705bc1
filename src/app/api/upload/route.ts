import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { 
  uploadToS3, 
  generateUniqueFileName, 
  getContentType, 
  S3_FOLDERS,
} from '@/lib/s3';

export async function POST(request: Request) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    // Required parameters
    const entityType = formData.get('entityType') as string;

    if (!file) {
      return NextResponse.json(
        { error: 'Missing required file' },
        { status: 400 }
      );
    }

    // Validate file type
    const contentType = getContentType(file.name);
    if (!contentType) {
      return NextResponse.json(
        { error: 'Invalid file type' },
        { status: 400 }
      );
    }

    // Check file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'File size exceeds 10MB limit' },
        { status: 400 }
      );
    }

    // Get appropriate S3 folder based on entityType
    let folder;
    switch (entityType) {
      case 'entry':
        folder = S3_FOLDERS.ENTRIES;
        break;
      case 'placement':
        folder = S3_FOLDERS.PLACEMENTS;
        break;
      case 'profile':
        folder = S3_FOLDERS.PROFILES;
        break;
      case 'signature':
        folder = S3_FOLDERS.SIGNATURES;
        break;
      case 'competency-plus':
        folder = S3_FOLDERS.COMPETENCY_PLUS;
        break;
      default:
        folder = S3_FOLDERS.GENERAL;
    }

    // Generate unique filename with folder prefix
    const fileName = `${folder}/${generateUniqueFileName(file.name)}`;

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer());

    // Upload to S3
    const s3Key = await uploadToS3(buffer, fileName, contentType);

    // Return the file information
    return NextResponse.json({
      file: {
        s3Key,
        name: file.name,
        contentType,
        size: file.size
      }
    });

  } catch (error) {
    console.error('Error in file upload:', error);
    return NextResponse.json(
      { error: 'Internal server error during file upload' },
      { status: 500 }
    );
  }
} 