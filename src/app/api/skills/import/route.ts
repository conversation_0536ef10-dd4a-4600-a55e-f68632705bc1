import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import sqeSkills from '@/../prisma/skillsJson/SQESkills.json';
import tcSkills from '@/../prisma/skillsJson/TCSkills.json';

export async function POST(request: Request) {
  try {
    // Skip authentication in development
    if (process.env.NODE_ENV !== 'development') {
      const session = await getServerSession(authOptions);
      if (!session?.user || session.user.role !== 'ADMIN') {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
    }

    // Get the type of skills to import from the request
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type')?.toLowerCase() || 'all';

    // Get practice areas first
    const practiceAreas = await prisma.practiceArea.findMany();
    const sqeArea = practiceAreas.find(area => area.name.toLowerCase().includes('sqe'));
    const tcArea = practiceAreas.find(area => area.name.toLowerCase().includes('tc'));

    if (!sqeArea || !tcArea) {
      return NextResponse.json({ 
        error: 'Practice areas not found',
        message: 'Please initialize practice areas first'
      }, { status: 404 });
    }

    const results = {
      sqe: false,
      tc: false
    };

    // Import SQE skills if requested
    if (type === 'sqe' || type === 'all') {
      try {
        for (const skillData of sqeSkills.practiceSkills.edges) {
          await prisma.practiceSkill.create({
            data: {
              name: skillData.name,
              practiceAreaId: sqeArea.id,
              practiceSkillGroups: {
                create: skillData.practiceSkillGroups.edges.map(group => ({
                  name: group.name,
                  practiceSubSkills: {
                    create: group.practiceSubSkills.edges.map((subSkill, index) => ({
                      name: subSkill.name,
                      practiceSubSkillType: 'sqe',
                      minSuggestedEntryCount: subSkill.minSuggestedEntryCount,
                      order: index + 1
                    }))
                  }
                }))
              }
            }
          });
        }
        results.sqe = true;
      } catch (error) {
        console.error('Error importing SQE skills:', error);
      }
    }

    // Import TC skills if requested
    if (type === 'tc' || type === 'all') {
      try {
        for (const skillData of tcSkills.practiceSkills.edges) {
          await prisma.practiceSkill.create({
            data: {
              name: skillData.name,
              practiceAreaId: tcArea.id,
              practiceSkillGroups: {
                create: skillData.practiceSkillGroups.edges.map(group => ({
                  name: group.name,
                  practiceSubSkills: {
                    create: group.practiceSubSkills.edges.map((subSkill, index) => ({
                      name: subSkill.name,
                      practiceSubSkillType: 'tc',
                      minSuggestedEntryCount: subSkill.minSuggestedEntryCount,
                      order: index + 1
                    }))
                  }
                }))
              }
            }
          });
        }
        results.tc = true;
      } catch (error) {
        console.error('Error importing TC skills:', error);
      }
    }

    // Return results
    if (results.sqe || results.tc) {
      return NextResponse.json({ 
        success: true,
        message: `Successfully imported: ${Object.entries(results)
          .filter(([, success]) => success)
          .map(([type]) => type.toUpperCase())
          .join(', ')} skills`
      });
    } else {
      return NextResponse.json({ 
        error: 'Failed to import skills',
        message: 'No skills were imported successfully'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error importing skills:', error);
    return NextResponse.json({ 
      error: 'Failed to import skills',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 