import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the current user to check their qualification route
    const user = await prisma.user.findUnique({
      where: { email: session.user.email || '' },
      select: { qualificationRoute: true }
    });

    // Default to TC if no qualification route is set
    const qualificationRoute = user?.qualificationRoute || 'TC';

    try {
      // Fetch practice skills with their hierarchy
      const practiceSkills = await prisma.practiceSkill.findMany({
        include: {
          practiceSkillGroups: {
            include: {
              practiceSubSkills: {
                orderBy: { order: 'asc' }
              }
            }
          }
        }
      });


      // Transform the data to match the expected format
      const transformedData = practiceSkills.map(skill => {
        
        const transformedSkill = {
          id: skill.id,
          name: skill.name,
          practiceSkillGroups: skill.practiceSkillGroups.map(group => {
            const filteredSubSkills = group.practiceSubSkills
              .filter(subSkill => {
                const shouldInclude = subSkill.practiceSubSkillType === 'both' || 
                  subSkill.practiceSubSkillType.toLowerCase() === qualificationRoute.toLowerCase() ||
                  (qualificationRoute.toLowerCase() === 'tc' && subSkill.practiceSubSkillType.toLowerCase() === 'sqe');
                return shouldInclude;
              })
              .map(subSkill => ({
                id: subSkill.id,
                name: subSkill.name,
                practiceSubSkillType: subSkill.practiceSubSkillType,
                minSuggestedEntryCount: subSkill.minSuggestedEntryCount,
                order: subSkill.order
              }));

            
            return {
              id: group.id,
              name: group.name,
              practiceSubSkills: filteredSubSkills
            };
          })
        };

        return transformedSkill;
      });

      return NextResponse.json(transformedData);
    } catch (dbError) {
      console.error('Database error fetching skills:', dbError);
      throw dbError;
    }
  } catch (error) {
    console.error('Error fetching skills:', error);
    return NextResponse.json({ error: 'Failed to fetch skills' }, { status: 500 });
  }
}
