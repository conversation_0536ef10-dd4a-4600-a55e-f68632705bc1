import { prisma } from '@/lib/prisma';

import sqeSkills from '@/../prisma/skillsJson/SQESkills.json';
import tcSkills from '@/../prisma/skillsJson/TCSkills.json';
import { PracticeSubSkillType } from '@/generated/prisma';

export async function POST(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const type = searchParams.get('type');
    
    if (!type) {
      return new Response(JSON.stringify({ message: 'Type parameter is required' }), {
        status: 400,
      });
    }

    const jsonData = type === 'sqe' ? sqeSkills : tcSkills;
    const updates = [];

    // Process each practice skill
    for (const practiceSkill of jsonData.practiceSkills.edges) {
      // Process each skill group
      for (const skillGroup of practiceSkill.practiceSkillGroups.edges) {
        // Process each subskill
        for (const subSkill of skillGroup.practiceSubSkills.edges) {
          // Update minSuggestedEntryCount for the subskill by matching name
          updates.push(
            prisma.practiceSubSkill.updateMany({
              where: {
                name: subSkill.name,
                practiceSubSkillType: type.toLowerCase() as PracticeSubSkillType
              },
              data: {
                minSuggestedEntryCount: subSkill.minSuggestedEntryCount
              }
            })
          );
        }
      }
    }

    // Execute all updates in parallel
    const results = await Promise.all(updates);
    const totalUpdated = results.reduce((acc, result) => acc + result.count, 0);

    return new Response(JSON.stringify({ 
      message: 'Successfully updated minSuggestedEntryCount values',
      updatedCount: totalUpdated
    }), {
      status: 200,
    });

  } catch (error) {
    console.error('Error updating minSuggestedEntryCount:', error);
    return new Response(JSON.stringify({ 
      message: 'Error updating minSuggestedEntryCount values',
      error: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
    });
  }
} 