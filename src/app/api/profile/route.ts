import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';

export async function PATCH(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session?.user) {
    return new NextResponse('Unauthorized', { status: 401 });
  }

  try {
    const userId = session.user.id;
    const body = await request.json();
    const { name, email, phoneNumber, address, qualificationRoute, image } = body;

    // Get current user data to check if qualification route is being changed
    const currentUser = await prisma.user.findUnique({
      where: { id: userId },
      select: { 
        role: true,
        qualificationRoute: true,
        email: true
      }
    });

    if (!currentUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if email is being changed and if it's already in use
    if (email && email !== currentUser.email) {
      const existingUser = await prisma.user.findFirst({
        where: {
          email,
          NOT: {
            id: userId,
          },
        },
      });

      if (existingUser) {
        return NextResponse.json(
          { error: 'Email already in use' },
          { status: 400 }
        );
      }
    }

    // If the user is a trainee and qualification route is being changed
    if (
      currentUser.role === 'TRAINEE' && 
      qualificationRoute && 
      (!currentUser.qualificationRoute || currentUser.qualificationRoute !== qualificationRoute)
    ) {
      // Check if trainee has any skills with progress
      const traineeSkills = await prisma.traineeSkill.findMany({
        where: { 
          traineeId: userId,
          doneEntryCount: { gt: 0 } // Only find skills with progress
        }
      });

      // If trainee has progress, don't allow changing qualification route
      if (traineeSkills.length > 0) {
        return NextResponse.json(
          { error: 'Cannot change qualification route once you have progress on skills' },
          { status: 400 }
        );
      }

      // If trainee has initialized skills but no progress, delete them to allow reinitializing
      const allTraineeSkills = await prisma.traineeSkill.findMany({
        where: { traineeId: userId }
      });

      if (allTraineeSkills.length > 0) {
        await prisma.traineeSkill.deleteMany({
          where: { traineeId: userId }
        });
      }

      // Get all skills based on the new qualification route
      const subSkills = await prisma.practiceSubSkill.findMany({
        where: {
          practiceSubSkillType: qualificationRoute.toLowerCase() as 'tc' | 'sqe'
        }
      });

      // Create TraineeSkill records for each skill of the new qualification route
      if (subSkills.length > 0) {
        await Promise.all(
          subSkills.map(subSkill => 
            prisma.traineeSkill.create({
              data: {
                traineeId: userId,
                subSkillId: subSkill.id,
                doneEntryCount: 0,
                minSuggestedEntryCount: subSkill.minSuggestedEntryCount
              }
            })
          )
        );
      }
    }

    // Update user data
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        name,
        email,
        phoneNumber,
        address,
        qualificationRoute,
        image,
      },
    });

    return NextResponse.json(updatedUser);
  } catch (error) {
    console.error('Error updating profile:', error);
    return NextResponse.json(
      { error: 'Failed to update profile' },
      { status: 500 }
    );
  }
} 