import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth';
import { EntryFormData, UserRole } from '@/types';

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: userId, role } = session.user;

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const placementId = searchParams.get('placementId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    let whereClause: Record<string, unknown> = {};

    switch (role) {
      case UserRole.ADMIN:
        break;
      case UserRole.TRAINEE:
        whereClause = {
          placement: {
            userId: userId,
          },
        };
        break;
      case UserRole.SUPERVISOR:
        return NextResponse.json([], { status: 200 });
      case UserRole.MENTOR:
        // Get user details to check if they are a Training Principal
        const mentorUser = await prisma.user.findUnique({
          where: { id: userId },
          select: { isTrainingPrincipal: true }
        });

        if (mentorUser?.isTrainingPrincipal) {
          // Training Principals can see ALL entries except drafts
          whereClause = {
            NOT: {
              status: 'draft'
            }
          };
        } else {
          // Regular mentors only see their assigned entries except drafts
          whereClause = {
            AND: [
              {
                OR: [
                  {
                    placement: {
                      mentorId: userId,
                    },
                  },
                  {
                    placement: {
                      user: {
                        mentorId: userId
                      }
                    }
                  },
                  {
                    reviewerId: userId
                  }
                ]
              },
              {
                NOT: {
                  status: 'draft'
                }
              }
            ]
          };
        }
        break;
      default:
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    if (status) {
      if (role === UserRole.MENTOR) {
        whereClause = {
          AND: [
            whereClause,
            { status: status.toLowerCase() }
          ]
        };
      } else {
        whereClause.status = status.toLowerCase();
      }
    }

    if (placementId) {
      whereClause.placementId = placementId;
    }

    if (startDate && endDate) {
      whereClause.OR = [
        {
          AND: [
            { startDate: { gte: new Date(startDate) } },
            { startDate: { lte: new Date(endDate) } }
          ]
        },
        {
          AND: [
            { endDate: { gte: new Date(startDate) } },
            { endDate: { lte: new Date(endDate) } }
          ]
        }
      ];
    }

    const entries = await prisma.entry.findMany({
      where: whereClause,
      include: {
        placement: {
          include: {
            client: true,
            user: true,
            supervisor: true,
            mentor: true,
          },
        },
        reviewer: true,
        submission: {
          select: {
            id: true,
            status: true,
            submittedAt: true,
            reviewerId: true,
            supervisorId: true
          }
        },
        entrySubSkills: {
          include: {
            subSkill: {
              include: {
                practiceSkillGroup: {
                  include: {
                    practiceSkill: {
                      include: {
                        practiceArea: true
                      }
                    }
                  }
                }
              }
            }
          }
        },
        entrySubmissions: {
          include: {
            submission: {
              select: {
                id: true,
                status: true,
                submittedAt: true,
                reviewerId: true,
                reviewerRole: true,
                supervisorId: true
              }
            }
          }
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return NextResponse.json(entries);
  } catch (error) {
    console.error('Error fetching entries:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== UserRole.TRAINEE) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      placementId,
      title,
      startDate,
      endDate,
      experience,
      evidence,
      learnt,
      moreExperience,
      needMoreExperience,
      contentiousType,
      taskedBy,
      documentKey,
      documentName,
      practiceAreas,
      practiceSubSkillIds
    } = body;

    // Validate required fields
    if (!placementId || !title || !startDate || !endDate || !experience || !learnt || !contentiousType) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Verify that the placement belongs to the trainee and get placement details
    const placement = await prisma.placement.findUnique({
      where: { id: placementId },
      include: {
        user: true,
        supervisor: true,
        mentor: true,
      }
    });

    if (!placement || placement.userId !== session.user.id) {
      return NextResponse.json(
        { error: 'Invalid placement or placement does not belong to you' },
        { status: 400 }
      );
    }

    // Create entry data
    const entryData: EntryFormData = {
      title: title || '',
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      experience: experience || '',
      evidence: evidence || '',
      learnt: learnt || '',
      contentiousType: contentiousType || 'both',
      placementId,
      practiceAreas: [],
      practiceSubSkillIds: practiceSubSkillIds || []
    };

    // Add optional fields if provided
    if (moreExperience) entryData.moreExperience = moreExperience;
    if (needMoreExperience) entryData.needMoreExperience = needMoreExperience;
    if (taskedBy) entryData.taskedBy = taskedBy;
    if (documentKey) entryData.documentKey = documentKey;
    if (documentName) entryData.documentName = documentName;
    if (practiceAreas && practiceAreas.length > 0) entryData.practiceAreas = practiceAreas;

    // Create the entry using transaction for better performance
    const { practiceSubSkillIds: subSkillIds, ...entryDataWithoutSkills } = entryData;
    
    const entry = await prisma.$transaction(async (tx) => {
      // First create the entry
      const createdEntry = await tx.entry.create({
        data: {
          ...entryDataWithoutSkills,
          practiceAreas: practiceAreas.map(String),
        },
        include: {
          placement: {
            include: {
              client: true,
            },
          },
        },
      });

      // Then create all entrySubSkills at once if there are any
      if (subSkillIds.length > 0) {
        await tx.entrySubSkill.createMany({
          data: subSkillIds.map((subSkillId: string) => ({
            entryId: createdEntry.id,
            subSkillId: subSkillId,
          })),
        });
      }

      // Return the entry with entrySubSkills included
      return await tx.entry.findUnique({
        where: { id: createdEntry.id },
        include: {
          placement: {
            include: {
              client: true,
            },
          },
          entrySubSkills: true,
        },
      });
    });

    // Generate view link
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const viewLink = `${baseUrl}/entries/${entry?.id}`;

    // Format dates for email
    const formattedStartDate = new Date(startDate).toLocaleDateString('en-GB');
    const formattedEndDate = new Date(endDate).toLocaleDateString('en-GB');

    // Send emails and notifications
    // try {
    //   await sendEntryCreationEmailsWithNotifications(
    //     placement.user.email!,
    //     placement.user.name!,
    //     placement.mentor?.email || null,
    //     placement.mentor?.name || null,
    //     formattedStartDate,
    //     formattedEndDate,
    //     viewLink
    //   );
    // } catch (emailError) {
    //   console.error('Error sending notification emails:', emailError);
    // }

    return NextResponse.json(entry);
  } catch (error) {
    console.error('Error creating entry:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}