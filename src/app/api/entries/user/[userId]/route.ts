// src/app/api/entries/user/[userId]/route.ts
import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET(
  req: Request,
  { params }: { params: Promise<{ userId: string }>; }
) {
  const { userId } = await params;

  try {
    const entries = await prisma.entry.findMany({
      where: {
        placement: {
          userId: userId,
        },
      },
      include: {
        placement: {
          include: {
            client: true,
            user: true,
            supervisor: true,
            mentor: true,
          },
        },
        reviewer: true,
        entrySubSkills: {
          include: {
            subSkill: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    return NextResponse.json(entries);
  } catch (error) {
    console.error('Error fetching entries:', error);
    return NextResponse.json({ error: 'Failed to fetch entries' }, { status: 500 });
  }
}
