import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth';
import { UserRole } from '@/types';

// GET entry by ID
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);

    // 1. Check authentication
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 2. Find the entry with all relations
    const entry = await prisma.entry.findUnique({
      where: { id },
      include: {
        placement: {
          include: {
            client: true,
            user: {
              include: {
                mentor: true
              }
            },
            supervisor: true,
            mentor: true,
          },
        },
        entrySubSkills: {
          include: {
            subSkill: {
              include: {
                practiceSkillGroup: {
                  include: {
                    practiceSkill: true
                  }
                }
              }
            }
          }
        },
        reviewer: true,
      },
    });

    if (!entry) {
      return NextResponse.json({ error: 'Entry not found' }, { status: 404 });
    }

    // 3. Authorization: Check if user is allowed to view this entry
    const { id: userId, role } = session.user;
    
    let isAllowed = false;
    
    if (role === UserRole.ADMIN) {
      isAllowed = true;
    } else if (role === UserRole.TRAINEE && entry.placement.userId === userId) {
      isAllowed = true;
    } else if (role === UserRole.SUPERVISOR && entry.placement.supervisorId === userId) {
      isAllowed = true;
    } else if (role === UserRole.MENTOR) {
      // Check if the mentor is a Training Principal
      const mentorUser = await prisma.user.findUnique({
        where: { id: userId },
        select: { isTrainingPrincipal: true }
      });
      
      if (mentorUser?.isTrainingPrincipal) {
        // Training Principals can access any entry
        isAllowed = true;
      } else {
        // Regular mentors can only access entries from their assigned trainees
        isAllowed = entry.placement.user.mentorId === userId;
      }
    }

    if (!isAllowed) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    } 

    return NextResponse.json(entry);
  } catch (error) {
    console.error('Error fetching entry:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PATCH update entry status and feedback (for Supervisor/Mentor review)
export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    const body = await request.json();
    const { status, feedback } = body;

    // 1. Check authentication
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 2. Validate input - status is only required for status changes
    if (status && typeof status !== 'string') {
      return NextResponse.json({ error: 'Invalid status provided' }, { status: 400 });
    }

    // 3. Find the entry and its placement to authorize
    const entry = await prisma.entry.findUnique({
      where: { id },
      include: {
        placement: {
          include: {
            user: {
              include: {
                mentor: true
              }
            }
          }
        },
        entrySubSkills: {
          include: {
            subSkill: true
          }
        },
      },
    });

    if (!entry) {
      return NextResponse.json({ error: 'Entry not found' }, { status: 404 });
    }

    // 4. Authorization: Check permissions based on action/status
    const currentRole = session.user.role;
    const currentUserId = session.user.id;
    const updateData: Record<string, unknown> = {};
    let isAllowed = false;

    if (currentRole === UserRole.ADMIN) {
      isAllowed = true;
    } else if (status === 'signedoff' || status === 'rejected') {
      isAllowed =
        currentRole === UserRole.SUPERVISOR &&
        entry.placement.supervisorId === currentUserId &&
        entry.status === 'submitted';
    } else if (status === 'submitted') {
      isAllowed =
        currentRole === UserRole.TRAINEE &&
        entry.placement.userId === currentUserId &&
        (entry.status === 'draft' || entry.status === 'rejected');
    } else if (status === 'draft') {
      isAllowed =
        currentRole === UserRole.TRAINEE &&
        entry.placement.userId === currentUserId &&
        entry.status !== 'signedoff';
    } else if (!status && currentRole === UserRole.MENTOR) {
      // Check if the mentor is a Training Principal
      const mentorUser = await prisma.user.findUnique({
        where: { id: currentUserId },
        select: { isTrainingPrincipal: true }
      });

      if (mentorUser?.isTrainingPrincipal) {
        // Training Principals can provide feedback on any entry
        isAllowed = true;
      } else {
        // Regular mentors can only provide feedback on entries from their assigned trainees
        isAllowed = entry.placement.user.mentorId === currentUserId;
      }
      
      updateData.reviewerId = currentUserId;
      updateData.reviewedAt = new Date();
    }

    if (!isAllowed) {
      return NextResponse.json({ error: 'Forbidden or invalid status transition' }, { status: 403 });
    }

    // 5. Update the entry
    if (status) {
      updateData.status = status;
      updateData.reviewedAt = status === 'signedoff' || status === 'rejected' ? new Date() : entry.reviewedAt;
      updateData.reviewerId = status === 'signedoff' || status === 'rejected' ? session.user.id : entry.reviewerId;
      updateData.submittedAt = status === 'submitted' ? new Date() : entry.submittedAt;
    }
    
    if (feedback !== undefined) {
      updateData.feedback = feedback;
    }

    const updatedEntry = await prisma.entry.update({
      where: { id },
      data: updateData,
      include: {
        placement: {
          include: {
            client: true,
            user: true,
          },
        },
        reviewer: true,
        entrySubSkills: {
          include: {
            subSkill: true
          }
        },
      },
    });

    // 6. If entry is signed off, update trainee skills
    if (status === 'signedoff') {
      try {
        const traineeId = entry.placement.userId;

        // Update TraineeSkill for each subskill in the entry
        const updatePromises = entry.entrySubSkills.map(({ subSkill }) =>
          prisma.traineeSkill.upsert({
            where: {
              traineeId_subSkillId: {
                traineeId,
                subSkillId: subSkill.id
              }
            },
            update: {
              doneEntryCount: {
                increment: 1
              },
              updatedAt: new Date()
            },
            create: {
              traineeId,
              subSkillId: subSkill.id,
              doneEntryCount: 1,
              minSuggestedEntryCount: subSkill.minSuggestedEntryCount
            }
          })
        );

        await Promise.all(updatePromises);
        console.log(`Trainee skills updated for trainee ${traineeId}`);
      } catch (progressError) {
        console.error('Error updating trainee skills:', progressError);
        // Continue with the entry update even if skill tracking fails
      }
    }

    return NextResponse.json(updatedEntry);

  } catch (error) {
    console.error('Error updating entry:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE an entry by ID
export async function DELETE(
  _request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);

    // 1. Check authentication
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 2. Check if entry exists with its placement details
    const entry = await prisma.entry.findUnique({
      where: { id },
      include: {
        placement: true,
        entrySubSkills: true
      }
    });

    if (!entry) {
      return NextResponse.json({ error: 'Entry not found' }, { status: 404 });
    }

    // 3. Authorization check:
    // - Admin can delete any entry
    // - Trainee can only delete their own entries in draft state
    const isAdmin = session.user.role === UserRole.ADMIN;
    const isOwnEntry = session.user.id === entry.placement.userId;
    const isDraft = entry.status === 'draft';

    if (!(isAdmin || (isOwnEntry && isDraft))) {
      return NextResponse.json({ 
        error: 'You can only delete your own entries in draft state' 
      }, { status: 403 });
    }

    // 4. First delete related entrySubSkills (if not using cascade), then the entry
    await prisma.$transaction(async (tx) => {
      // Delete related EntrySubSkill records
      prisma.entrySubSkill.deleteMany({
        where: { entryId: id }
      }),
      
      // Delete the entry
      prisma.entry.delete({
        where: { id }
      })
    }, {
      timeout: 30000, // 30 seconds timeout
      maxWait: 10000, // 10 seconds max wait
    });

    return NextResponse.json({ 
      success: true, 
      message: 'Entry deleted successfully' 
    });
  } catch (error) {
    console.error('Error deleting entry:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    const body = await request.json();
    const {
      title,
      startDate,
      endDate,
      experience,
      evidence,
      learnt,
      moreExperience,
      needMoreExperience,
      contentiousType,
      taskedBy,
      documentKey,
      documentName,
      practiceAreas,
      practiceSubSkillIds
    } = body;

    // Check authentication
    if (!session?.user || session.user.role !== UserRole.TRAINEE) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Find the entry
    const existingEntry = await prisma.entry.findUnique({
      where: { id },
      include: {
        placement: true,
        entrySubSkills: true,
      },
    });

    if (!existingEntry) {
      return NextResponse.json({ error: 'Entry not found' }, { status: 404 });
    }

    // Verify ownership
    if (existingEntry.placement.userId !== session.user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Update entry
    const updatedEntry = await prisma.entry.update({
      where: { id },
      data: {
        title,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        experience,
        evidence,
        learnt,
        moreExperience,
        needMoreExperience,
        contentiousType,
        taskedBy,
        documentKey,
        documentName,
        practiceAreas,
        placementId: body.placementId, // Add placementId to update the placement
        entrySubSkills: {
          deleteMany: {}, // First delete all existing relationships
          create: practiceSubSkillIds.map((subSkillId: string) => ({
            subSkillId
          }))
        }
      },
      include: {
        placement: {
          include: {
            client: true,
          },
        },
        entrySubSkills: {
          include: {
            subSkill: true
          }
        },
      },
    });

    return NextResponse.json(updatedEntry);
  } catch (error) {
    console.error('Error updating entry:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
