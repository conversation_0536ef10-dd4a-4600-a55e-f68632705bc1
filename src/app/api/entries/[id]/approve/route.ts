import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { UserRole } from '@/types';

export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }>; }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only supervisors can approve entries
    if (session.user.role !== UserRole.SUPERVISOR) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { status } = body;

    // Validate status
    if (!['APPROVED', 'REJECTED'].includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status' },
        { status: 400 }
      );
    }

    // Check if the entry belongs to a placement supervised by this supervisor
    const entry = await prisma.entry.findFirst({
      where: {
        id: id,
        placement: {
          supervisorId: session.user.id,
        },
      },
    });

    if (!entry) {
      return NextResponse.json(
        { error: 'Entry not found or not authorized' },
        { status: 404 }
      );
    }

    // Update the entry
    const updatedEntry = await prisma.entry.update({
      where: {
        id: id,
      },
      data: {
        status,
        reviewedAt: new Date(),
        reviewerId: session.user.id,
      },
    });

    return NextResponse.json(updatedEntry);
  } catch (error) {
    console.error('Error approving entry:', error);
    return NextResponse.json(
      { error: 'Failed to approve entry' },
      { status: 500 }
    );
  }
} 