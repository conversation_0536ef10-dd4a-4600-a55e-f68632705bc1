import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { UserRole } from '@/types';

export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }>; }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only mentors can review entries
    if (session.user.role !== UserRole.MENTOR) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { status, feedback } = body;

    // Validate status
    if (!['REVIEWED_WITH_FEEDBACK', 'APPROVED'].includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status' },
        { status: 400 }
      );
    }

    // If there's feedback, append it to the status
    const finalStatus = feedback ? `${status}: ${feedback}` : status;

    // Check if the mentor is a Training Principal
    const mentorUser = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { isTrainingPrincipal: true }
    });

    // Find the entry
    const entry = await prisma.entry.findUnique({
      where: { id },
      include: {
        placement: {
          include: {
            user: true
          }
        }
      }
    });

    if (!entry) {
      return NextResponse.json(
        { error: 'Entry not found' },
        { status: 404 }
      );
    }

    // Authorization check
    let isAllowed = false;
    if (mentorUser?.isTrainingPrincipal) {
      // Training Principals can review any entry
      isAllowed = true;
    } else {
      // Regular mentors can only review entries from their assigned trainees
      isAllowed = entry.placement.user.mentorId === session.user.id;
    }

    if (!isAllowed) {
      return NextResponse.json(
        { error: 'Entry not found or not authorized' },
        { status: 404 }
      );
    }

    // Update the entry
    const updatedEntry = await prisma.entry.update({
      where: {
        id: id,
      },
      data: {
        status: finalStatus,
        reviewedAt: new Date(),
        reviewerId: session.user.id,
      },
    });

    return NextResponse.json(updatedEntry);
  } catch (error) {
    console.error('Error reviewing entry:', error);
    return NextResponse.json(
      { error: 'Failed to review entry' },
      { status: 500 }
    );
  }
} 