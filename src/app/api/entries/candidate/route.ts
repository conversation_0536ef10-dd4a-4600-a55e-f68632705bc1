import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { UserRole } from '@/types';

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user?.email },
    });

    if (!user || user.role !== UserRole.TRAINEE) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get URL search params
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');

    // Get entries for the candidate's placements
    const entries = await prisma.entry.findMany({
      where: {
        placement: {
          userId: user.id,
        },
        ...(status && { status: status.toUpperCase() }),
      },
      include: {
        entrySubSkills: {
          include: {
            subSkill: true,
          },
        },
        placement: {
          include: {
            client: true,
            supervisor: true,
            mentor: true,
          },
        },
      },
      orderBy: {
        submittedAt: 'desc',
      },
    });

    return NextResponse.json(entries);
  } catch (error) {
    console.error('Error fetching entries:', error);
    return NextResponse.json(
      { error: 'Failed to fetch entries' },
      { status: 500 }
    );
  }
}