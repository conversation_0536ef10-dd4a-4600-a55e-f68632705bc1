import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { UserRole } from '@/types';

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (session.user.role !== UserRole.SUPERVISOR) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'all'; // 'current', 'past', or 'all'

    // Get all trainees from history table with minimal competency data
    const allTraineesHistory = await prisma.supervisorTraineeHistory.findMany({
      where: {
        supervisorId: session.user.id,
      },
      include: {
        trainee: {
          select: {
            id: true,
            name: true,
            email: true,
            updatedAt: true,
            qualificationRoute: true,
            traineeLevel: true,
            traineeSkills: {
              select: {
                doneEntryCount: true,
                minSuggestedEntryCount: true,
              }
            }
          }
        }
      },
      orderBy: [
        { isActive: 'desc' }, // Current first
        { endDate: 'desc' }    // Then by end date
      ]
    });

    // Group by traineeId to avoid duplicates (same trainee with multiple placements)
    const traineeGroups = new Map<string, any[]>();
    
    allTraineesHistory.forEach(record => {
      const traineeId = record.trainee.id;
      if (!traineeGroups.has(traineeId)) {
        traineeGroups.set(traineeId, []);
      }
      traineeGroups.get(traineeId)!.push(record);
    });

    // Helper function to transform grouped trainee data
    const transformGroupedTraineeData = (traineeId: string, records: any[]) => {
      const firstRecord = records[0]; // All records have same trainee data
      const trainee = firstRecord.trainee;
      
      // Determine if trainee is currently active (has at least one active relationship)
      const isCurrentlyActive = records.some(r => r.isActive);
      
      // Get earliest start date and latest end date
      const startDates = records.map(r => r.startDate);
      const endDates = records.map(r => r.endDate).filter(date => date !== null);
      
      const earliestStart = new Date(Math.min(...startDates.map(d => d.getTime())));
      const latestEnd = endDates.length > 0 
        ? new Date(Math.max(...endDates.map(d => d.getTime())))
        : null;

      // Calculate competency progress from traineeSkills
      const traineeSkills = trainee.traineeSkills || [];
      const totalDone = traineeSkills.reduce((sum: number, skill: any) => sum + skill.doneEntryCount, 0);
      const totalRequired = traineeSkills.reduce((sum: number, skill: any) => sum + skill.minSuggestedEntryCount, 0);
      
      // Debug: Uncomment for debugging competency calculations
      // console.log(`Trainee ${trainee.name}: ${totalDone}/${totalRequired} = ${competencyProgress}%`);
      
      const competencyProgress = totalRequired > 0 
        ? parseFloat(((totalDone / totalRequired) * 100).toFixed(2))
        : 0;

      return {
        id: trainee.id,
        name: trainee.name || 'Unknown',
        email: trainee.email || '',
        qualificationRoute: trainee.qualificationRoute,
        traineeLevel: trainee.traineeLevel,
        lastActive: trainee.updatedAt.toISOString(),
        competencyProgress,
        relationshipStartDate: earliestStart.toISOString(),
        relationshipEndDate: latestEnd?.toISOString(),
        isActive: isCurrentlyActive,
        placementCount: records.length, // Number of placements with this supervisor
      };
    };

    // Transform grouped trainees
    const allFormattedTrainees = Array.from(traineeGroups.entries()).map(
      ([traineeId, records]) => transformGroupedTraineeData(traineeId, records)
    );

    // Separate current and past trainees based on isActive status
    const formattedCurrentTrainees = allFormattedTrainees.filter(t => t.isActive);
    const formattedPastTrainees = allFormattedTrainees.filter(t => !t.isActive);

    if (type === 'all') {
      return NextResponse.json({
        current: formattedCurrentTrainees,
        past: formattedPastTrainees,
      });
    }

    return NextResponse.json(type === 'current' ? formattedCurrentTrainees : formattedPastTrainees);
  } catch (error) {
    console.error('Error fetching trainees:', error);
    return NextResponse.json(
      { error: 'Failed to fetch trainees' },
      { status: 500 }
    );
  }
} 