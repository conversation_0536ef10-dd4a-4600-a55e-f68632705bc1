import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get submissions from current trainees that supervisor can review
    const submissions = await prisma.submission.findMany({
      where: {
        AND: [
          // Only from current trainees
          {
            trainee: {
              traineeHistory: {
                some: {
                  supervisorId: session.user.id,
                  isActive: true
                }
              }
            }
          },
          // That supervisor can review
          {
            OR: [
              { reviewerId: session.user.id },
              { supervisorId: session.user.id },
              {
                entries: {
                  some: {
                    placement: {
                      supervisorId: session.user.id
                    }
                  }
                }
              }
            ]
          }
        ]
      },
      include: {
        trainee: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        entries: {
          select: {
            id: true,
            title: true,
            startDate: true,
            endDate: true,
            status: true,
            documentKey: true,
            documentName: true,
            feedback: true,
            entrySubSkills: {
              include: {
                subSkill: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return NextResponse.json(submissions);
  } catch (error) {
    console.error('Error fetching supervisor submissions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch submissions' },
      { status: 500 }
    );
  }
} 