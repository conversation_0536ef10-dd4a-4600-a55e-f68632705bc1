'use client';

import { useState } from 'react';
import { Form, Input, Button, App, Card } from 'antd';
import { MailOutlined } from '@ant-design/icons';
import Link from 'next/link';

export default function ForgotPasswordPage() {
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const { message } = App.useApp();

  const onFinish = async (values: { email: string }) => {
    setLoading(true);
    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (response.ok) {
        setEmailSent(true);
        message.success('Password reset instructions have been sent to your email');
      } else {
        const data = await response.json();
        message.error(data.error || 'Failed to process request');
      }
    } catch (error) {
      console.error('Error:', error);
      message.error('An error occurred while processing your request');
    } finally {
      setLoading(false);
    }
  };

  if (emailSent) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <Card className="max-w-md w-full">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">Check your email</h2>
            <p className="mt-2 text-sm text-gray-600">
              We have sent password reset instructions to your email address.
              Please check your inbox and follow the instructions to reset your password.
            </p>
            <div className="mt-4">
              <Link href="/auth/signin" className="text-blue-600 hover:text-blue-500">
                Return to login
              </Link>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="max-w-md w-full">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">Forgot Password</h2>
          <p className="mt-2 text-sm text-gray-600">
            Enter your email address and we will send you instructions to reset your password.
          </p>
        </div>

        <Form
          name="forgot-password"
          className="mt-8!"
          onFinish={onFinish}
          layout="vertical"
        >
          <Form.Item
            name="email"
            rules={[
              { required: true, message: 'Please input your email!' },
              { type: 'email', message: 'Please enter a valid email!' }
            ]}
          >
            <Input
              prefix={<MailOutlined />}
              placeholder="Email address"
              size="large"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              className="w-full"
              size="large"
              loading={loading}
            >
              Send Reset Instructions
            </Button>
          </Form.Item>

          <div className="text-center">
            <Link href="/auth/signin" className="text-blue-600 hover:text-blue-500">
              Return to login
            </Link>
          </div>
        </Form>
      </Card>
    </div>
  );
} 