'use client';
import { useParams } from 'next/navigation';
import FeedbackFormEdit from '@/components/mentoring/FeedbackFormEdit';
import { useState } from 'react';
import { useEffect } from 'react';
import type { FeedbackForm } from '@/types/feedback';

export default function EditFeedbackFormPage() {
  const params = useParams();
  const [isLoading, setIsLoading] = useState(true);
  const [feedbackForm, setFeedbackForm] = useState<FeedbackForm | null>(null);

  useEffect(() => {
    const fetchFeedbackForm = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/feedback-forms/${params.id}`);
        if (!response.ok) throw new Error('Failed to fetch feedback form');
        setFeedbackForm(await response.json());
      } catch (err) {
        console.error('Error fetching feedback form:', err);
      } finally {
        setIsLoading(false);
      }
    };
    fetchFeedbackForm();
  }, [params.id]);
  
  return (
    <div className="w-full bg-white">
      <div className="max-w-4xl mx-auto p-6">
        <h1 className="text-2xl font-bold mb-6">Edit Feedback Form</h1>
        {isLoading ? (
          <p>Loading...</p>
        ) : (
          <FeedbackFormEdit feedbackForm={feedbackForm as FeedbackForm & { monthlyReview: { candidate: { name: string; }; mentor: { name: string; } | null; }; }} onSuccess={() => {}} />
        )}
      </div>
    </div>
  );
} 