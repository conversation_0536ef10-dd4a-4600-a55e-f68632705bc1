'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { Meeting, UserRole } from '@/types';
import { Spin, Alert } from 'antd';
import { useSession } from 'next-auth/react';

export default function MeetingPage() {
  const { data: session } = useSession();
  const params = useParams();
  const isMentor = session?.user?.role === UserRole.MENTOR;
  const [meeting, setMeeting] = useState<Meeting | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchMeeting = async () => {
      try {
        const response = await fetch(`/api/meetings/${params.id}`);
        if (!response.ok) {
          throw new Error('Failed to fetch meeting');
        }
        const data = await response.json();
        setMeeting(data);
      } catch (err) {
        setError('Failed to load meeting. Please try again later.');
        console.error('Error fetching meeting:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchMeeting();
  }, [params.id]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Spin size="large" tip="Loading meeting..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Alert
          message="Error"
          description={error}
          type="error"
          showIcon
        />
      </div>
    );
  }

  if (!meeting) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Alert
          message="Not Found"
          description="Meeting not found"
          type="error"
          showIcon
        />
      </div>
    );
  }

  const myName = session?.user?.name || (isMentor ? meeting.mentor.name : meeting.candidate.name);
  const meetingUrl = `https://www.accuconnect.tech/video-call.html?room=${meeting.id}&name=${myName}`;

  return (
    <div className="w-full h-screen">
      <iframe
        src={meetingUrl}
        className="w-full h-full border-0"
        allow="camera; microphone; fullscreen; display-capture"
      />
    </div>
  );
} 
