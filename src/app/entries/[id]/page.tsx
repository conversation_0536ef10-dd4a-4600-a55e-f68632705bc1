'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Spin, Tag, Divider, Typography, Result, Button } from 'antd';
import { Entry } from '@/types';
import dayjs from 'dayjs';
import { viewDocument } from '@/lib/utils';
import { practiceAreaOptions } from '@/helpers/practiceAreas';

export default function EntryPrintPreview() {
  const params = useParams();
  const [entry, setEntry] = useState<Entry | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchEntry = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/entries/${params.id}`);
        const data = await response.json();
        console.log('data', response);
        if (!response.ok) {
          console.log('Error response:', data, 'Status:', response.status);
          
          if (response.status === 403) {
            setError('Access Denied: You do not have permission to view this entry. Please contact your supervisor or mentor if you believe this is a mistake.');
            throw new Error('Access Denied: You do not have permission to view this entry. Please contact your supervisor or mentor if you believe this is a mistake.');
          } else if (response.status === 404) {
            setError('Not Found: The entry you are looking for does not exist or has been removed.');
            throw new Error('Not Found: The entry you are looking for does not exist or has been removed.');
          } else {
            throw new Error(data.error || 'An unexpected error occurred. Please try again later.');
          }
        }
        
        console.log('Entry data:', data);
        setEntry(data);
      } catch (err) {
        console.log('err', err);
      } finally {
        setLoading(false);
      }
    };
    fetchEntry();
  }, [params.id]);

  const router = useRouter();

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-white dark:bg-gray-800">
        <Spin size="large" />
      </div>
    );
  }

  if (error || !entry) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-800 py-8">
        <Result
          status="error"
          title={error || "Entry not found"}
          extra={[
            <Button type="primary" key="back" onClick={() => router.back()}>
              Go Back
            </Button>,
            <Button key="home" onClick={() => router.push('/')}>
              Go Home
            </Button>,
          ]}
        />
      </div>
    );
  }

  // Map practice area IDs to names
  const practiceAreaNames = Array.isArray(entry.practiceAreas)
    ? entry.practiceAreas.map(id => {
      const found = practiceAreaOptions.find(area => String(area.id) === String(id));
      return found ? found.title : String(id);
    })
    : [];

  const getContentiousTypeDisplay = (type: string) => {
    switch(type) {
      case 'contentious':
        return 'Contentious';
      case 'non_contentious':
        return 'Non-Contentious';
      case 'both':
        return 'Both';
      default:
        return type;
    }
  };

  const groupedSkills: {
    [skillName: string]: {
      [groupName: string]: string[]
    }
  } = {};

  if (Array.isArray(entry.entrySubSkills)) {
    entry.entrySubSkills.forEach(skill => {
      const skillName = skill.subSkill.practiceSkillGroup?.practiceSkill?.name || 'Other Skills';
      const groupName = skill.subSkill.practiceSkillGroup?.name || 'Other Group';
      const subSkillName = skill.subSkill.name;

      if (!groupedSkills[skillName]) {
        groupedSkills[skillName] = {};
      }
      if (!groupedSkills[skillName][groupName]) {
        groupedSkills[skillName][groupName] = [];
      }
      if (!groupedSkills[skillName][groupName].includes(subSkillName)) {
        groupedSkills[skillName][groupName].push(subSkillName);
      }
    });
  }

  // Sort skills alphabetically
  const sortedSkills = Object.entries(groupedSkills).sort(([a], [b]) => {
    // Extract the letter prefix (A, B, C, etc.)
    const aPrefix = a.match(/^[A-Z]/)?.[0] || '';
    const bPrefix = b.match(/^[A-Z]/)?.[0] || '';
    return aPrefix.localeCompare(bPrefix);
  });

  // Helper function to sort groups by their prefix number
  const getGroupPrefix = (groupName: string) => {
    const match = groupName.match(/^[A-Z](\d+)/);
    return match ? parseInt(match[1]) : 999; // If no number prefix, put it at the end
  };

  return (
    <div className="min-h-screen bg-white dark:bg-gray-800 py-8">
      <div className="print-preview bg-white rounded-lg shadow" style={{ padding: 32, maxWidth: 900, margin: '0 auto' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <img src="https://admin.accutraineepw.com/images/logos/logo--dark.svg" alt="Accutrainee Logo" style={{ width: 180 }} />
        <div>
          <div style={{ fontWeight: 700, fontSize: 14 }}>Candidate:</div>
          <div style={{ color: '#f27b21', fontWeight: 700, fontSize: 22 }}>
            {entry.placement?.user?.name}
          </div>
        </div>
      </div>
      <Divider />
      {/* Entry Title and Meta */}
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
        <div>
          <Typography.Title level={4} style={{ margin: 0 }}>{entry.title}</Typography.Title>
          <div style={{ marginTop: 8 }}>
            <strong>Placement:</strong> {entry.placement?.client?.name}
          </div>
          {entry.taskedBy && (
            <div style={{ marginTop: 4 }}>
              <strong>Tasked By:</strong> {entry.taskedBy}
            </div>
          )}
          <div style={{ marginTop: 8 }}>
            <strong>Practice Areas:</strong>
            <div>
              {practiceAreaNames.map(name => (
                <Tag key={name} color="orange">{name}</Tag>
              ))}
            </div>
          </div>
        </div>
        <div style={{ textAlign: 'right' }}>
          <div>
            <strong>Start Date:</strong> {dayjs(entry.startDate).format('DD/MM/YYYY')}
          </div>
          <div>
            <strong>End Date:</strong> {dayjs(entry.endDate).format('DD/MM/YYYY')}
          </div>
          <div style={{ marginTop: 4 }}>
            <strong>Matter Type:</strong> {getContentiousTypeDisplay(entry.contentiousType)}
          </div>
        </div>
      </div>
      <Divider />
      {/* Entry Details */}
      <Typography.Title level={5}>Details of the task/work completed</Typography.Title>
      <Typography.Paragraph style={{ whiteSpace: 'pre-wrap' }}>{entry.experience}</Typography.Paragraph>
      <Typography.Title level={5}>Reflections/What did I learn?</Typography.Title>
      <Typography.Paragraph style={{ whiteSpace: 'pre-wrap' }}>{entry.learnt}</Typography.Paragraph>
      <Typography.Title level={5}>How can I improve?</Typography.Title>
      <Typography.Paragraph style={{ whiteSpace: 'pre-wrap' }}>{entry.needMoreExperience}</Typography.Paragraph>
      {/* Attached Documents */}
      {entry.documentName && entry.documentKey && entry.documentKey !== 'null' && (
        <>
          <Divider />
          <Typography.Title level={5}>Supporting Document</Typography.Title>
          <ul>
            <li key={entry.documentKey}>
              <a onClick={() => entry.documentKey && viewDocument(entry.documentKey)}>{entry.documentName}</a>
            </li>
          </ul>
        </>
      )}
      {/* Skills */}
      <Divider style={{ margin: '16px 0' }} />
      <div style={{ marginBottom: 16 }}>
        <Typography.Title level={5} style={{ marginBottom: 0 }}>Competency/Skills:</Typography.Title>
        <div style={{ marginTop: 4 }}>
          {sortedSkills.map(([skillName, groups]) => (
            <div key={skillName} style={{ marginBottom: 32 }}>
              <Typography.Title level={5} style={{ marginBottom: 8 }}>{skillName}</Typography.Title>
              {Object.entries(groups)
                .sort(([a], [b]) => {
                  const prefixA = getGroupPrefix(a);
                  const prefixB = getGroupPrefix(b);
                  return prefixA - prefixB;
                })
                .map(([groupName, subSkills]) => (
                  <div key={groupName} style={{ marginLeft: 20, marginBottom: 16 }}>
                    <Typography.Text strong>{groupName}</Typography.Text>
                    <ul style={{ marginTop: 4, marginBottom: 8, marginLeft: 20 }}>
                      {subSkills.sort().map((subSkill, idx) => (
                        <li key={idx}>{subSkill}</li>
                      ))}
                    </ul>
                  </div>
                ))}
            </div>
          ))}
        </div>
      </div>
      {/* <div className="print-hide" style={{ marginTop: 32 }}>
        <Button type="primary" onClick={() => window.print()}>Print</Button>
      </div> */}
      <style jsx global>{`
        @media print {
          .print-hide { display: none !important; }
          body { background: #fff !important; }
        }
      `}</style>
      </div>
    </div>
  );
} 