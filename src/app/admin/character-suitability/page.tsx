'use client';
import { Card, Typography, Button, Form, Input, Table, Spin, Alert } from 'antd';
import { PlusOutlined, DeleteOutlined, CheckCircleFilled, MinusCircleOutlined } from '@ant-design/icons';
import useCharacterSuitabilityStore from '@/store/characterSuitabilityStore';
import { useEffect, useState } from 'react';
import { ExternalLink } from '@/types/character-suitability';

const { Title } = Typography;
const { TextArea } = Input;

export default function AdminCharacterSuitabilityPage() {
  const {
    characterSuitability,
    completions,
    loading,
    error,
    fetchCharacterSuitability,
    fetchCompletions,
    updateCharacterSuitability,
  } = useCharacterSuitabilityStore();

  const [form] = Form.useForm();
  const [editingLinks, setEditingLinks] = useState<ExternalLink[]>([]);

  useEffect(() => {
    fetchCharacterSuitability();
    fetchCompletions();
  }, [fetchCharacterSuitability, fetchCompletions]);

  useEffect(() => {
    if (characterSuitability) {
      form.setFieldsValue({
        rules: characterSuitability.rules,
        infoForQualifiedSolicitors: characterSuitability.infoForQualifiedSolicitors,
      });
      setEditingLinks(characterSuitability.externalLinks);
    }
  }, [characterSuitability, form]);

  const handleSave = async (values: {
    rules: string;
    infoForQualifiedSolicitors: string;
    externalLinks: ExternalLink[];
  }) => {
    if (characterSuitability) {
      await updateCharacterSuitability({
        id: characterSuitability.id,
        ...values,
        externalLinks: editingLinks,
      });
    } else {
      await updateCharacterSuitability({
        ...values,
        externalLinks: editingLinks,
      });
    }
  };

  const addLink = () => {
    setEditingLinks([...editingLinks, { title: '', url: '' }]);
  };

  const updateLink = (index: number, field: keyof ExternalLink, value: string) => {
    const newLinks = [...editingLinks];
    newLinks[index] = { ...newLinks[index], [field]: value };
    setEditingLinks(newLinks);
  };

  const removeLink = (index: number) => {
    setEditingLinks(editingLinks.filter((_, i) => i !== index));
  };

  const completionsColumns = [
    {
      title: 'Trainee',
      dataIndex: ['user', 'name'],
      key: 'name',
      render: (name: string) => name || '-',
    },
    {
      title: 'Email',
      dataIndex: ['user', 'email'],
      key: 'email',
      render: (email: string) => email || '-',
    },
    {
      title: 'Status',
      dataIndex: 'completed',
      key: 'completed',
      render: (completed: boolean) => (
        <div className="flex items-center">
          {completed ? (
            <>
              <CheckCircleFilled className="text-green-500! mr-2" />
            </>
          ) : (
            <>
              <MinusCircleOutlined className="text-gray-400! mr-2" />
            </>
          )}
        </div>
      ),
    },
    {
      title: 'Completed At',
      dataIndex: 'completedAt',
      key: 'completedAt',
      render: (date: string) => date ? new Date(date).toLocaleDateString() : '-',
    },
  ];

  if (loading) return <Spin size="large" />;
  if (error) return <Alert type="error" message={error} />;

  return (
    <div className="max-w-4xl mx-auto p-4">
      <div className="flex justify-between items-center mb-4">
        <Title level={2}>Character & Suitability</Title>
        <Button type="primary" htmlType="submit" form="characterSuitabilityForm">
          SAVE
        </Button>
      </div>

      <Card title="Character & Suitability Info" className="mb-4">
        <Form 
          id="characterSuitabilityForm"
          form={form} 
          onFinish={handleSave} 
          layout="vertical"
          initialValues={{
            rules: '',
            infoForQualifiedSolicitors: '',
          }}
        >
          <Title level={4}>Rules around Character & Suitability</Title>
          <Form.Item
            name="rules"
            rules={[{ required: true, message: 'Please input the rules' }]}
          >
            <TextArea rows={6} />
          </Form.Item>

          <Title level={4}>Information for Qualified Solicitors</Title>
          <Form.Item
            name="infoForQualifiedSolicitors"
            rules={[{ required: true, message: 'Please input the information' }]}
          >
            <TextArea rows={6} />
          </Form.Item>

          <Title level={4}>External Links and Information</Title>
          <div className="mb-4">
            {editingLinks.map((link, index) => (
              <div key={index} className="flex gap-2 mb-2">
                <Input
                  placeholder="Title (optional)"
                  value={link.title}
                  onChange={(e) => updateLink(index, 'title', e.target.value)}
                />
                <Input
                  placeholder="URL"
                  value={link.url}
                  onChange={(e) => updateLink(index, 'url', e.target.value)}
                  required
                />
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => removeLink(index)}
                />
              </div>
            ))}
            <Button type="dashed" onClick={addLink} icon={<PlusOutlined />} block>
              Add Link
            </Button>
          </div>
        </Form>
      </Card>

      <Card title="Candidates">
        <Table
          dataSource={completions}
          columns={completionsColumns}
          rowKey="id"
          pagination={{ pageSize: 10 }}
        />
      </Card>
    </div>
  );
} 