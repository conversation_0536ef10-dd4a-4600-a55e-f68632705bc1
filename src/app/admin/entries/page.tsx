'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { UserRole } from '@/types';
import AdminEntriesTable from '@/components/entries/AdminEntriesTable';
import { App as AntdApp } from 'antd';

function AdminEntriesDataWrapper() {
  const router = useRouter();
  const { data: session } = useSession();

  useEffect(() => {
    if (session === null) { // Explicitly check for null (logged out)
      router.push('/auth/signin'); // Redirect to login if not authenticated
    } else if (session && session.user?.role !== UserRole.ADMIN) {
      router.push('/admin/dashboard'); // Redirect if authenticated but not admin
    }
    // Add dependency on session status if available to handle loading state better
  }, [session, router]);

  // Handle loading and unauthorized states before rendering table
  if (session === undefined) {
    return <div>Loading session...</div>; // Or a spinner
  }
  if (!session || session.user?.role !== UserRole.ADMIN) {
    // Although useEffect redirects, this prevents brief rendering flashes
    return null; 
  }

  return (
    <>
      <AdminEntriesTable
        userId={session.user.id}
        isAdmin={true}
        title="Manage All Entries" // Keep or customize title
      />
    </>
  );
}

export default function AdminEntriesPage() {
  // Wrap with AntdApp if message context is needed globally
  return (
    <AntdApp>
      <AdminEntriesDataWrapper />
    </AntdApp>
  );
} 