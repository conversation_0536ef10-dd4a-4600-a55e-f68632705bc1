"use client";

import { useState, useEffect } from "react";
import {
  Button,
  Table,
  Space,
  Modal,
  Form,
  Input,
  App,
  Select,
  Typography,
  Card,
  Row,
  Col,
  Badge,
  Tooltip
} from "antd";
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  SearchOutlined, 
  FilterOutlined,
  EyeOutlined,
  Bar<PERSON><PERSON>Outlined,
  LinkOutlined 
} from "@ant-design/icons";
import { Course, CourseCategory } from "@/types";
import ImageUpload from "@/components/common/ImageUpload";
import RichTextEditor from "@/components/common/RichTextEditor";

import { S3_FOLDERS } from "@/lib/s3";
import { useRouter } from "next/navigation";

const { Title, Text } = Typography;

export default function AdminCoursesPage() {
  const { message } = App.useApp();
  const router = useRouter();
  const [courses, setCourses] = useState<Course[]>([]);
  const [filteredCourses, setFilteredCourses] = useState<Course[]>([]);
  const [categories, setCategories] = useState<CourseCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const [form] = Form.useForm();
  const [filterForm] = Form.useForm();
  const [editingId, setEditingId] = useState<string | null>(null);
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  useEffect(() => {
    fetchCourses();
    fetchCategories();
  }, []);

  useEffect(() => {
    let filtered = courses;
    
    if (searchText) {
      filtered = filtered.filter(course => 
        course.name.toLowerCase().includes(searchText.toLowerCase()) ||
        course.description?.toLowerCase().includes(searchText.toLowerCase())
      );
    }
    
    if (selectedCategory) {
      filtered = filtered.filter(course => course.categoryId === selectedCategory);
    }
    
    setFilteredCourses(filtered);
    setCurrentPage(1);
  }, [searchText, selectedCategory, courses]);

  const fetchCourses = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/admin/courses");
      const data = await response.json();
      setCourses(data);
      setFilteredCourses(data);
    } catch (error) {
      setCourses([]);
      setFilteredCourses([]);
      console.error('Error fetching courses:', error);
      message.error("Failed to fetch courses");
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch("/api/admin/course-categories");
      const data = await response.json();
      setCategories(data);
    } catch (error) {
      setCategories([]);
      console.error("Error fetching categories:", error);
      message.error("Failed to fetch categories");
    }
  };


  const handleSubmit = async (values: any) => {
    try {
      setSubmitting(true);
      const url = editingId
        ? `/api/admin/courses/${editingId}`
        : "/api/admin/courses";

      const method = editingId ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) throw new Error("Failed to save course");

      message.success(
        `Course ${editingId ? "updated" : "created"} successfully`
      );
      setIsModalVisible(false);
      form.resetFields();
      setEditingId(null);
      fetchCourses();
    } catch (error) {
      console.error('Error saving course:', error);
      message.error("Failed to save course");
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/admin/courses/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete course");

      message.success("Course deleted successfully");
      fetchCourses();
    } catch (error) {
      console.error('Error deleting course:', error);
      message.error("Failed to delete course");
    }
  };

  const handleEdit = (record: Course) => {
    setEditingId(record.id);
    const formData = {
      ...record,
      secondCategoryIds: record.secondCategories?.map(sc => sc.categoryId) || []
    };
    form.setFieldsValue(formData);
    setIsModalVisible(true);
  };

  const handleViewAnalytics = (courseId: string) => {
    router.push(`/admin/courses/${courseId}/analytics`);
  };

  const columns = [
    {
      title: "Course Name",
      dataIndex: "name",
      key: "name",
      render: (name: string, record: Course) => (
        <div className="flex items-center gap-3">
          {record.imageUrl && (
            <img 
              src={record.imageUrl} 
              alt={name}
              className="w-12 h-12 rounded object-contain"
            />
          )}
          <div>
            <div className="font-medium">{name}</div>
            <div className="flex items-center gap-2">
              <Text type="secondary" className="text-sm">{record.category.name}</Text>
              {record.secondCategories && record.secondCategories.length > 0 && (
                <div className="flex gap-1">
                  {record.secondCategories.map((sc) => (
                    <span
                      key={sc.id}
                      className="px-2 py-1 text-xs text-white rounded-md"
                      style={{ backgroundColor: sc.category.color || '#666' }}
                    >
                      {sc.category.name}
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: "External Link",
      dataIndex: "externalLink",
      key: "externalLink",
      render: (link: string) => (
        <Tooltip title={link}>
          <a 
            href={link} 
            target="_blank" 
            rel="noopener noreferrer"
            className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-800"
          >
            <LinkOutlined />
            {link.length > 30 ? `${link.substring(0, 30)}...` : link}
          </a>
        </Tooltip>
      ),
    },
    {
      title: "Pricing",
      dataIndex: "pricing",
      key: "pricing",
      render: (pricing: string) => (
        <span className="font-medium text-gray-700">
          {pricing || "Not specified"}
        </span>
      ),
    },
    {
      title: "Click Analytics",
      align: "center" as const,
      key: "analytics",
      render: (_: unknown, record: Course) => (
        <div className="text-center">
          <Badge count={record._count?.courseClicks || 0} showZero>
            <EyeOutlined className="text-lg" />
          </Badge>
          <div className="text-xs text-gray-500 mt-1">
            {record._count?.courseClicks || 0} clicks
          </div>
        </div>
      ),
    },
    {
      title: "Actions",
      key: "actions",
      render: (_: unknown, record: Course) => (
        <Space>
          <Button 
            icon={<BarChartOutlined />} 
            onClick={() => handleViewAnalytics(record.id)}
            title="View Analytics"
            size="small"
          />
          <Button 
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record)}
            size="small"
          />
          <Button
            icon={<DeleteOutlined />}
            danger
            onClick={() => handleDelete(record.id)}
            size="small"
          />
        </Space>
      ),
    },
  ];

  const handleFilterReset = () => {
    filterForm.resetFields();
    setSelectedCategory(null);
    setSearchText('');
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-4">
        <Title level={2}>Courses Management</Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            setEditingId(null);
            form.resetFields();
            setIsModalVisible(true);
          }}
          style={{ backgroundColor: 'rgb(199, 100, 27)' }}
        >
          Add Course
        </Button>
      </div>

      <Card className="mb-4!">
        <Form form={filterForm} layout="horizontal">
          <Row gutter={16}>
            <Col xs={24} sm={8} md={6}>
              <Form.Item label="Search" name="search">
                <Input
                  placeholder="Search courses..."
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  allowClear
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8} md={6}>
              <Form.Item label="Category" name="category">
                <Select 
                  placeholder="Filter by category"
                  allowClear
                  onChange={(value) => setSelectedCategory(value)}
                  value={selectedCategory}
                >
                  {categories.map((category) => (
                    <Select.Option key={category.id} value={category.id}>
                      {category.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={8} md={12}>
              <Form.Item className="flex justify-end">
                <Button icon={<FilterOutlined />} onClick={handleFilterReset}>Reset</Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      <Table
        loading={loading}
        columns={columns}
        dataSource={filteredCourses}
        rowKey="id"
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          onChange: (page, size) => {
            setCurrentPage(page);
            setPageSize(size || 10);
          },
          showSizeChanger: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
        }}
      />

      <Modal
        title={editingId ? "Edit Course" : "Create Course"}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
          setEditingId(null);
        }}
        onOk={form.submit}
        confirmLoading={submitting}
        width={800}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item
            name="name"
            label="Course Name"
            rules={[{ required: true, message: "Please input course name!" }]}
          >
            <Input placeholder="Enter course name" />
          </Form.Item>

          <Form.Item
            name="categoryId"
            label="Primary Category"
            rules={[{ required: true, message: "Please select a category!" }]}
          >
            <Select placeholder="Select primary category">
              {categories.filter(category => !category.isSecondCategory).map((category) => (
                <Select.Option key={category.id} value={category.id}>
                  {category.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="secondCategoryIds"
            label="Secondary Categories"
            extra="Select additional categories for this course (optional)"
          >
            <Select
              placeholder="Select secondary categories"
              allowClear
              showSearch
              filterOption={(input, option) =>
                String(option?.children || '').toLowerCase().includes(input.toLowerCase())
              }
            >
              {categories.filter(category => category.isSecondCategory).map((category) => (
                <Select.Option key={category.id} value={category.id}>
                  {category.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="externalLink"
            label="External Link"
            rules={[
              { required: true, message: "Please input external link!" },
              { type: "url", message: "Please enter a valid URL!" },
            ]}
            extra="This link will be tracked for user clicks and analytics"
          >
            <Input placeholder="https://example.com/course" prefix={<LinkOutlined />} />
          </Form.Item>

          <Form.Item
            name="pricing"
            label="Pricing"
            extra="Enter pricing information (e.g., 'Free', '£50', 'Not applicable', etc.)"
          >
            <Input placeholder="e.g., Free, £50, Not applicable" />
          </Form.Item>

          <Form.Item name="imageUrl" label="Banner Image" extra="Recommended size: 800x450px">
            <ImageUpload
              folder={S3_FOLDERS.COURSES}
              aspectRatio={16 / 9}
              maxSize={5}
              className="w-[300px] h-[169px]"
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
            rules={[{ required: true, message: "Please input description!" }]}
            extra="Use the rich text editor to format content and add images/videos"
          >
            <RichTextEditor
              value={form.getFieldValue('description') || ''}
              onChange={(value) => form.setFieldsValue({ description: value })}
              placeholder="Enter course description..."
            />
          </Form.Item>
        </Form>
      </Modal>


    </div>
  );
}
