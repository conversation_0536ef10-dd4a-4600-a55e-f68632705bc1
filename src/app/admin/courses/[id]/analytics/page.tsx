"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import {
  Card,
  Typography,
  Statistic,
  Table,
  Tag,
  Spin,
  Button,
  Space,
  Row,
  Col,
  Avatar,
  App,
  Breadcrumb,
} from "antd";
import {
  ArrowLeftOutlined,
  EyeOutlined,
  UserOutlined,
  LinkOutlined,
  HomeOutlined,
  Bar<PERSON><PERSON>Outlined,
  BookOutlined,
} from "@ant-design/icons";
import moment from "moment";

const { Title, Text } = Typography;

interface AnalyticsResponse {
  course: {
    id: string;
    name: string;
    externalLink: string;
    category: {
      name: string;
      color: string;
    };
  };
  analytics: {
    totalClicks: number;
    uniqueUsers: number;
    clicksByDay: Array<{
      date: string;
      clicks: number;
    }>;
    clicks: Array<{
      id: string;
      clickedAt: string;
      ipAddress?: string;
      userAgent?: string;
      user: {
        id: string;
        name: string;
        email: string;
        role: string;
      };
    }>;
    pagination: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
    };
  };
}

export default function CourseAnalyticsPage() {
  const router = useRouter();
  const params = useParams();
  const { message } = App.useApp();
  const courseId = params.id as string;

  const [data, setData] = useState<AnalyticsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  useEffect(() => {
    if (courseId) {
      fetchAnalytics();
    }
  }, [courseId, currentPage, pageSize]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const url = `/api/admin/courses/${courseId}/analytics?page=${currentPage}&pageSize=${pageSize}`;
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error("Failed to fetch analytics");
      }
      const responseData = await response.json();
      setData(responseData);
    } catch (error) {
      console.error("Error fetching analytics:", error);
      message.error("Failed to load analytics data");
    } finally {
      setLoading(false);
    }
  };

  const clicksColumns = [
    {
      title: "User",
      key: "user",
      render: (
        _: unknown,
        record: AnalyticsResponse["analytics"]["clicks"][0]
      ) => (
        <Space>
          <Avatar icon={<UserOutlined />} size="small" />
          <div>
            <div className="font-medium">{record.user.name}</div>
            <Text type="secondary" className="text-xs">
              {record.user.email}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: "Role",
      dataIndex: ["user", "role"],
      key: "role",
      render: (role: string) => (
        <Tag
          color={
            role === "TRAINEE" ? "blue" : role === "MENTOR" ? "green" : "purple"
          }
        >
          {role}
        </Tag>
      ),
    },
    {
      title: "Click Time",
      dataIndex: "clickedAt",
      key: "clickedAt",
      render: (date: string) => (
        <div>
          <div>{moment(date).format("DD/MM/YYYY")}</div>
          <Text type="secondary" className="text-xs">
            {moment(date).format("HH:mm:ss")}
          </Text>
        </div>
      ),
    },
    {
      title: "Time Ago",
      dataIndex: "clickedAt",
      key: "timeAgo",
      render: (date: string) => (
        <Text type="secondary">{moment(date).fromNow()}</Text>
      ),
    },
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (!data) {
    return (
      <div className="p-6">
        <Title level={2}>Analytics not found</Title>
        <Button onClick={() => router.back()}>Go Back</Button>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Breadcrumb */}
      <Breadcrumb className="mb-6!">
        <Breadcrumb.Item>
          <HomeOutlined />
        </Breadcrumb.Item>
        <Breadcrumb.Item
          onClick={() => router.push("/admin/courses")}
          className="cursor-pointer hover:text-blue-600"
        >
          <BookOutlined />
          <span>Courses</span>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <BarChartOutlined />
          <span>Analytics</span>
        </Breadcrumb.Item>
      </Breadcrumb>

      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex flex-col gap-4">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => router.push("/admin/courses")}
          >
            Back to Courses
          </Button>
          <div>
            <Title level={2} className="!mb-1">
              Course Analytics
            </Title>
            <Text type="secondary">
              Detailed analytics for &quot;{data.course.name}&quot;
            </Text>
          </div>
        </div>
      </div>

      {/* Course Info Card */}
      <Card className="mb-6!">
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} lg={16}>
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <Title level={4} className="!mb-0">
                    {data.course.name}
                  </Title>
                  <Tag color={data.course.category.color}>
                    {data.course.category.name}
                  </Tag>
                </div>
                <div className="flex items-center gap-2">
                  <LinkOutlined />
                  <a
                    href={data.course.externalLink}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    {data.course.externalLink.length > 60
                      ? `${data.course.externalLink.substring(0, 60)}...`
                      : data.course.externalLink}
                  </a>
                </div>
              </div>
            </div>
          </Col>
        </Row>
      </Card>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} className="mb-6!">
        <Col xs={24} sm={12}>
          <Card>
            <Statistic
              title="Total Clicks"
              value={data.analytics.totalClicks}
              prefix={<EyeOutlined />}
              valueStyle={{ color: "#1890ff" }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12}>
          <Card>
            <Statistic
              title="Unique Users"
              value={data.analytics.uniqueUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: "#52c41a" }}
            />
          </Card>
        </Col>
        {/* <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="Avg Clicks/User"
              value={
                data.analytics.uniqueUsers > 0
                  ? data.analytics.totalClicks / data.analytics.uniqueUsers
                  : 0
              }
              precision={1}
              valueStyle={{ color: "#faad14" }}
            />
            <Progress
              percent={Math.min(
                100,
                (data.analytics.totalClicks /
                  Math.max(1, data.analytics.uniqueUsers)) *
                  20
              )}
              size="small"
              className="mt-2"
              strokeColor="#faad14"
            />
          </Card>
        </Col> */}
      </Row>

      {/* All Clicks Table */}
      <Card
        title={
          <div className="flex items-center gap-2">
            <EyeOutlined />
            <span>All Clicks</span>
            <Tag>{data.analytics.pagination.total} total records</Tag>
          </div>
        }
      >
        <Table
          columns={clicksColumns}
          dataSource={data.analytics.clicks || []}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: data.analytics.pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} clicks`,
            onChange: (page, size) => {
              setCurrentPage(page);
              if (size !== pageSize) {
                setPageSize(size);
                setCurrentPage(1); // Reset to first page when page size changes
              }
            },
            onShowSizeChange: (current, size) => {
              setPageSize(size);
              setCurrentPage(1);
            },
          }}
          scroll={{ x: "max-content" }}
        />
      </Card>
    </div>
  );
}
