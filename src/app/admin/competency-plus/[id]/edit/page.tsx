'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Spin, Card, Typography } from 'antd';
import { UserRole, CompetencyPlus } from '@/types';
import CompetencyPlusForm from '@/components/competency-plus/CompetencyPlusForm';

const { Title } = Typography;

export default function EditCompetencyPlusPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session, status } = useSession();
  const [competencyPlus, setCompetencyPlus] = useState<CompetencyPlus | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
      return;
    }

    if (session && session.user?.role !== UserRole.ADMIN) {
      router.push('/admin/dashboard');
      return;
    }

    if (params.id) {
      fetchCompetencyPlus();
    }
  }, [session, status, router, params.id]);

  const fetchCompetencyPlus = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/competency-plus/${params.id}`);
      if (!response.ok) throw new Error('Failed to fetch competency plus item');
      const data = await response.json();
      setCompetencyPlus(data);
    } catch (error) {
      console.error('Error fetching competency plus item:', error);
      router.push('/admin/competency-plus');
    } finally {
      setLoading(false);
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="p-6">
        <Card>
          <div className="flex justify-center items-center h-32">
            <Spin size="large" />
          </div>
        </Card>
      </div>
    );
  }

  if (!session || session.user?.role !== UserRole.ADMIN) {
    return null;
  }

  if (!competencyPlus) {
    return (
      <div className="p-6">
        <Card>
          <div className="text-center">
            <Title level={4}>Competency Plus item not found</Title>
          </div>
        </Card>
      </div>
    );
  }

  return <CompetencyPlusForm initialData={competencyPlus} isEdit={true} />;
} 