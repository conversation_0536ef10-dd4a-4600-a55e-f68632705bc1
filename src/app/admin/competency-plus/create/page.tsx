'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { UserRole } from '@/types';
import CompetencyPlusForm from '@/components/competency-plus/CompetencyPlusForm';

export default function CreateCompetencyPlusPage() {
  const router = useRouter();
  const { data: session, status } = useSession();

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
      return;
    }

    if (session && session.user?.role !== UserRole.ADMIN) {
      router.push('/admin/dashboard');
      return;
    }
  }, [session, status, router]);

  if (status === 'loading') {
    return <div>Loading...</div>;
  }

  if (!session || session.user?.role !== UserRole.ADMIN) {
    return null;
  }

  return <CompetencyPlusForm />;
} 