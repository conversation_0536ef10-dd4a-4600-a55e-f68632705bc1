'use client';

import { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Input,
  Space,
  Typography,
  Card,
  Tag,
  Popconfirm,
  Tooltip,
  App
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { CompetencyPlus } from '@/types';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Search } = Input;

export default function AdminCompetencyPlusPage() {
  const { message } = App.useApp();
  const [items, setItems] = useState<CompetencyPlus[]>([]);
  const [filteredItems, setFilteredItems] = useState<CompetencyPlus[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const router = useRouter();

  useEffect(() => {
    fetchItems();
  }, []);

  useEffect(() => {
    filterItems();
  }, [items, searchText]);

  const fetchItems = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/competency-plus');
      if (!response.ok) throw new Error('Failed to fetch items');
      const data = await response.json();
      setItems(data);
    } catch (error) {
      console.error('Error fetching competency plus items:', error);
      message.error('Failed to fetch competency plus items');
    } finally {
      setLoading(false);
    }
  };

  const filterItems = () => {
    let filtered = items;
    
    if (searchText) {
      filtered = filtered.filter(item =>
        item.title.toLowerCase().includes(searchText.toLowerCase()) ||
        item.shortDescription.toLowerCase().includes(searchText.toLowerCase())
      );
    }
    
    setFilteredItems(filtered);
  };

  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/competency-plus/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) throw new Error('Failed to delete item');
      
      message.success('Competency Plus item deleted successfully');
      fetchItems();
    } catch (error) {
      console.error('Error deleting item:', error);
      message.error('Failed to delete item');
    }
  };

  const columns = [
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      width: '30%',
      ellipsis: true,
    },
    {
      title: 'Short Description',
      dataIndex: 'shortDescription',
      key: 'shortDescription',
      width: '35%',
      ellipsis: true,
    },
    {
      title: 'Status',
      dataIndex: 'published',
      key: 'published',
      width: '10%',
      render: (published: boolean) => (
        <Tag color={published ? 'green' : 'orange'}>
          {published ? 'Published' : 'Draft'}
        </Tag>
      ),
    },
    {
      title: 'Posted At',
      dataIndex: 'publishedAt',
      key: 'publishedAt',
      width: '15%',
      render: (publishedAt: string, record: CompetencyPlus) => (
        publishedAt ? dayjs(publishedAt).format('DD/MM/YYYY') : 
        record.published ? dayjs(record.createdAt).format('DD/MM/YYYY') : '-'
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: '10%',
      render: (_: unknown, record: CompetencyPlus) => (
        <Space>
          <Tooltip title="View">
            <Button
              icon={<EyeOutlined />}
              size="small"
              onClick={() => router.push(`/trainee/competency-plus/${record.id}`)}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button
              icon={<EditOutlined />}
              size="small"
              onClick={() => router.push(`/admin/competency-plus/${record.id}/edit`)}
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure you want to delete this item?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button
                icon={<DeleteOutlined />}
                size="small"
                danger
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <Title level={2}>Competency Plus Management</Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => router.push('/admin/competency-plus/create')}
        >
          Add Competency Plus
        </Button>
      </div>

      <Card className="mb-4!">
        <Search
          placeholder="Search by title or description..."
          allowClear
          enterButton={<SearchOutlined />}
          size="large"
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ maxWidth: 600 }}
        />
      </Card>

      <Card>
        <Table
          columns={columns}
          dataSource={filteredItems}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
          }}
        />
      </Card>
    </div>
  );
} 