'use client';

import { useState, useEffect } from 'react';
import { Table, Button, Popconfirm, Card, App, Form, Input, DatePicker, Row, Col } from 'antd';
import { SearchOutlined, FilterOutlined } from '@ant-design/icons';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import type { Placement } from '@/types';
import dayjs from 'dayjs';
import PlacementFormModal from '@/components/placement/PlacementFormModal';

export default function PlacementsPage() {
  const { message } = App.useApp();
  const [placements, setPlacements] = useState<Placement[]>([]);
  const [filteredPlacements, setFilteredPlacements] = useState<Placement[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingPlacement, setEditingPlacement] = useState<Placement | null>(null);
  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [filterForm] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [dateRange, setDateRange] = useState<[null | Date, null | Date]>([null, null]);

  const fetchPlacements = async () => {
    setLoading(true);
    try {
      const placementsRes = await fetch('/api/placements');
      if (placementsRes.ok) {
        const placementsData = await placementsRes.json();
        setPlacements(placementsData.placements.edges);
        setFilteredPlacements(placementsData.placements.edges);
      } else {
        message.error('Failed to fetch placements');
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      message.error('Failed to load data');
      setPlacements([]);
      setFilteredPlacements([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPlacements();
  }, []);

  // Filter placements when search text or date range changes
  useEffect(() => {
    let filtered = placements;
    
    // Filter by search text (organisation, trainee, supervisor, mentor)
    if (searchText) {
      filtered = filtered.filter(placement => 
        (placement.client?.name?.toLowerCase().includes(searchText.toLowerCase())) ||
        (placement.user?.name?.toLowerCase().includes(searchText.toLowerCase())) ||
        (placement.supervisor?.name?.toLowerCase().includes(searchText.toLowerCase())) ||
        (placement.mentor?.name?.toLowerCase().includes(searchText.toLowerCase()))
      );
    }
    
    // Filter by date range
    if (dateRange[0] && dateRange[1]) {
      const startDate = dateRange[0];
      const endDate = dateRange[1];
      
      filtered = filtered.filter(placement => {
        const placementStartDate = placement.startDate ? new Date(placement.startDate) : null;
        if (!placementStartDate) return false;
        
        return placementStartDate >= startDate && placementStartDate <= endDate;
      });
    }
    
    setFilteredPlacements(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  }, [searchText, dateRange, placements]);

  const columns = [
    {
      title: 'Organisation Name',
      dataIndex: ['client', 'name'],
      key: 'client',
      render: (clientName: string) => clientName || '',
    },
    {
      title: 'Trainee',
      dataIndex: ['user', 'name'],
      key: 'trainee',
      render: (traineeName: string) => traineeName || '',
    },
    {
      title: 'Supervisor',
      dataIndex: ['supervisor', 'name'],
      key: 'supervisor',
      render: (supervisorName: string) => supervisorName || 'Not assigned',
    },
    {
      title: 'Mentor',
      dataIndex: ['mentor', 'name'],
      key: 'mentor',
      render: (mentorName: string) => mentorName || 'Not assigned',
    },
    {
      title: 'Start Date',
      dataIndex: 'startDate',
      key: 'startDate',
      render: (date: string) => date ? dayjs(date).format('DD/MM/YYYY') : '',
    },
    {
      title: 'End Date',
      dataIndex: 'endDate',
      key: 'endDate',
      render: (date?: string) => date ? dayjs(date).format('DD/MM/YYYY') : 'Ongoing',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: unknown, record: Placement) => (
        <div className="space-x-2">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          />
          <Popconfirm
            title="Are you sure you want to delete this placement?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </div>
      ),
    },
  ];

  const handleAdd = () => {
    setEditingPlacement(null);
    setIsModalVisible(true);
  };

  const handleEdit = (placement: Placement) => {
    setEditingPlacement(placement);
    setIsModalVisible(true);
  };

  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/placements/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        message.success('Placement deleted successfully');
        setPlacements(prev => prev.filter((p) => p.id !== id));
      } else {
        message.error('Failed to delete placement');
      }
    } catch (error) {
      console.error('Error deleting placement:', error);
      message.error('Failed to delete placement');
    }
  };

  const handleSavePlacement = async (values: Partial<Placement>) => {
    setSaveLoading(true);
    try {
      const url = editingPlacement
        ? `/api/placements/${editingPlacement.id}`
        : '/api/placements';
      const method = editingPlacement ? 'PATCH' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (response.ok) {
        const savedPlacement = await response.json();
        if (editingPlacement) {
          setPlacements(prev =>
            prev.map((p) =>
              p.id === editingPlacement.id ? savedPlacement : p
            )
          );
          message.success('Placement updated successfully');
        } else {
          setPlacements(prev => [savedPlacement, ...prev]);
          message.success('Placement created successfully');
        }
        setIsModalVisible(false);
        setEditingPlacement(null);
      } else {
        const errorData = await response.json();
        message.error(errorData.error || `Failed to ${editingPlacement ? 'update' : 'create'} placement`);
      }
    } catch (error) {
      console.error('Error saving placement:', error);
      message.error('Failed to save placement');
    } finally {
      setSaveLoading(false);
    }
  };

  // Handle filter reset
  const handleFilterReset = () => {
    filterForm.resetFields();
    setSearchText('');
    setDateRange([null, null]);
  };

  return (
    <div className="max-w-7xl mx-auto p-4">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Placements</h1>
      </div>

      <Card className="mb-4">
        <Form form={filterForm} layout="horizontal" className="w-full">
          <Row gutter={[16, 12]} className="w-full">
            <Col xs={24} md={8}>
              <Form.Item label="Search" name="search" className="mb-0">
                <Input
                  placeholder="Search by organisation, trainee, supervisor..."
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  allowClear
                  className="w-full"
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item label="Start Date Range" name="dateRange" className="mb-0">
                <DatePicker.RangePicker 
                  style={{ width: '100%' }}
                  onChange={(_, dateStrings) => {
                    const startDate = dateStrings[0] ? new Date(dateStrings[0]) : null;
                    const endDate = dateStrings[1] ? new Date(dateStrings[1]) : null;
                    setDateRange([startDate, endDate]);
                  }}
                  className="w-full"
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={4}>
              <Form.Item className="flex justify-end mb-0">
                <Button 
                  icon={<FilterOutlined />} 
                  onClick={handleFilterReset}
                  className="w-full md:w-auto"
                >
                  Reset Filters
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      <Card>
        <Table
          dataSource={filteredPlacements}
          columns={columns}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 10);
            },
            showSizeChanger: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
          }}
          scroll={{ x: 'max-content' }}
        />
      </Card>

      <PlacementFormModal
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingPlacement(null);
        }}
        onSubmit={handleSavePlacement}
        placement={editingPlacement || undefined}
        loading={saveLoading}
      />
    </div>
  );
} 