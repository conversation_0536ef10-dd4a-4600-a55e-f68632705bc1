'use client';

import { useEffect, useState } from 'react';
import { Card, Row, Col, Typography, Statistic, Spin } from 'antd';
import { UserOutlined, RiseOutlined, BookOutlined } from '@ant-design/icons';
import Link from 'next/link';

const { Title } = Typography;

interface DashboardData {
  totalUsers: number;
  newSignups: number;
  courses: {
    total: number;
  };
}

export default function AdminDashboard() {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<DashboardData | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const response = await fetch('/api/admin/dashboard');
        if (!response.ok) throw new Error('Failed to fetch dashboard data');
        const data = await response.json();
        setData(data);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="p-6">
      <Title level={2} className="mb-6">Dashboard</Title>

      <Row gutter={[16, 16]}>
        {/* Total Users */}
        <Col xs={24} sm={12} lg={8}>
          <Card>
            <Statistic
              title="Total Users"
              value={data?.totalUsers || 0}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>

        {/* New Signups */}
        <Col xs={24} sm={12} lg={8}>
          <Card>
            <Statistic
              title="New Signups This Week"
              value={data?.newSignups || 0}
              prefix={<RiseOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>

        {/* Courses */}
        <Col xs={24} sm={12} lg={8}>
          <Link href="/admin/courses">
            <Card hoverable>
              <Statistic
                title="Total Courses"
                value={data?.courses.total || 0}
                prefix={<BookOutlined />}
              />
            </Card>
          </Link>
        </Col>
      </Row>
      <Row className='mt-4'>
        <div
          className='w-full'
          dangerouslySetInnerHTML={{
              __html: `
                <iframe plausible-embed src="https://cd-analytics.up.railway.app/share/pathways-new.vercel.app?auth=0lpsq4RCNaIfzbnuwop0Q&embed=true&theme=light" scrolling="yes" frameborder="0" loading="lazy" style="width: 100%; height: 2000px; border: none; overflow: visible;"></iframe>
                <script async src="https://plausible.io/js/embed.host.js"></script>
              `
            }}
          />
        {/* <a target='_blank' className='text-blue-500 text-center block py-2 px-4 bg-gray-100! rounded-md hover:bg-gray-200! transition-colors' href="https://cd-analytics.up.railway.app/share/pathways-new.vercel.app?auth=0lpsq4RCNaIfzbnuwop0Q">
          View Trainee Analytics
        </a> */}
      </Row>
    </div>
  );
}
