'use client';

import { useState, useEffect } from 'react';
import { Table, Button, Modal, Form, Input, Tabs, Card, Row, Col, App } from 'antd';
import { SearchOutlined, FilterOutlined } from '@ant-design/icons';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import type { Client, User, Placement } from '@/types';
import ClientSupervisorManager from '@/components/client/ClientSupervisorManager';
import MovePlacementsModal from '@/components/client/MovePlacementsModal';

const { TabPane } = Tabs;

export default function ClientsPage() {
  const { message } = App.useApp();
  const [clients, setClients] = useState<Client[]>([]);
  const [filteredClients, setFilteredClients] = useState<Client[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [filterForm] = Form.useForm();
  const [editingClient, setEditingClient] = useState<Client | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('details');
  const [searchText, setSearchText] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [movePlacementsData, setMovePlacementsData] = useState<{
    sourceClient: Client;
    placements: Placement[];
  } | null>(null);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deletingClient, setDeletingClient] = useState<Client | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deleteErrorData, setDeleteErrorData] = useState<{
    error: string;
    placements: Placement[];
  } | null>(null);

  useEffect(() => {
    fetchClients();
    fetchAvailableSupervisors();
  }, []);

  const fetchAvailableSupervisors = async () => {
    try {
      const response = await fetch('/api/users?role=SUPERVISOR');
      if (!response.ok) throw new Error('Failed to fetch supervisors');
      const supervisors = await response.json();
      
      // Filter out supervisors that are already assigned to other clients
      const response2 = await fetch('/api/supervisors/available');
      if (!response2.ok) throw new Error('Failed to fetch available supervisors');
      const availableSupervisorIds = await response2.json();
      
      return supervisors.filter((sup: User) => availableSupervisorIds.includes(sup.id));
    } catch (error) {
      console.error('Error fetching supervisors:', error);
      message.error('Failed to load available supervisors');
    }
  };

  const fetchClients = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/clients');
      if (!response.ok) throw new Error('Failed to fetch clients');
      const data = await response.json();
      setClients(data);
      setFilteredClients(data);
    } catch (error) {
      console.error('Error fetching clients:', error);
      message.error('Failed to load clients');
      setClients([]);
      setFilteredClients([]);
    } finally {
      setLoading(false);
    }
  };
  
  // Filter clients when search text changes
  useEffect(() => {
    if (!searchText) {
      setFilteredClients(clients);
      return;
    }
    
    const filtered = clients.filter(client => 
      client.name?.toLowerCase().includes(searchText.toLowerCase()) ||
      client.email?.toLowerCase().includes(searchText.toLowerCase()) ||
      client.address?.toLowerCase().includes(searchText.toLowerCase()) ||
      client.phone?.toLowerCase().includes(searchText.toLowerCase())
    );
    
    setFilteredClients(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  }, [searchText, clients]);

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: 'Phone',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: 'Address',
      dataIndex: 'address',
      key: 'address',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: unknown, record: Client) => (
        <div className="space-x-2">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          />
        </div>
      ),
    },
  ];

  const handleAdd = () => {
    setEditingClient(null);
    form.resetFields();
    setIsModalVisible(true);
    setActiveTab('details');
  };

  const handleEdit = (client: Client) => {
    setEditingClient(client);
    form.setFieldsValue(client);
    setIsModalVisible(true);
    setActiveTab('details');
  };

  const handleDelete = (client: Client) => {
    setDeletingClient(client);
    setDeleteModalVisible(true);
  };

  const handleDeleteConfirm = async () => {
    if (!deletingClient) return;

    try {
      setDeleteLoading(true);
      const response = await fetch(`/api/clients/${deletingClient.id}`, {
        method: 'DELETE',
      });

      if (response.status === 204) {
        message.success('Client deleted successfully');
        fetchClients();
        fetchAvailableSupervisors();
        handleDeleteCancel();
        return;
      }

      const data = await response.json();

      if (!response.ok) {
        if (data.code === 'CLIENT_HAS_PLACEMENTS') {
          setDeleteErrorData({
            error: data.error,
            placements: data.placements
          });
          setDeleteLoading(false);
          return;
        }
        
        throw new Error(data.error || 'Failed to delete client');
      }
      
      message.success('Client deleted successfully');
      fetchClients();
      fetchAvailableSupervisors();
      handleDeleteCancel();
    } catch (error) {
      console.error('Error deleting client:', error);
      message.error('Failed to delete client');
      handleDeleteCancel();
    }
  };

  const handleDeleteCancel = () => {
    setDeleteModalVisible(false);
    setDeletingClient(null);
    setDeleteLoading(false);
    setDeleteErrorData(null);
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      const { supervisors, ...clientData } = values;
      
      if (editingClient) {
        // Update existing client
        const response = await fetch(`/api/clients/${editingClient.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(clientData),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to update client');
        }

        // Update supervisors if changed
        if (supervisors?.length) {
          const response2 = await fetch(`/api/clients/${editingClient.id}/supervisors`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ supervisorIds: supervisors }),
          });

          if (!response2.ok) throw new Error('Failed to update supervisors');
        }

        message.success('Client updated successfully');
      } else {
        // Create new client
        const response = await fetch('/api/clients', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(clientData),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create client');
        }

        const newClient = await response.json();

        // Assign supervisors to new client
        if (supervisors?.length) {
          const response2 = await fetch(`/api/clients/${newClient.id}/supervisors`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ supervisorIds: supervisors }),
          });

          if (!response2.ok) throw new Error('Failed to assign supervisors');
        }

        message.success('Client created successfully');
      }
      
      setIsModalVisible(false);
      fetchClients();
      fetchAvailableSupervisors();
    } catch (error) {
      console.error('Error saving client:', error);
      message.error('Failed to save client');
    }
  };

  // Handle filter reset
  const handleFilterReset = () => {
    filterForm.resetFields();
    setSearchText('');
  };

  return (
    <>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Clients</h1>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAdd}
          style={{ backgroundColor: 'rgb(199, 100, 27)' }}
        >
          Add Client
        </Button>
      </div>

      <Card className="mb-4">
        <Form form={filterForm} layout="horizontal" className="w-full">
          <Row gutter={[16, 12]} className="w-full">
            <Col xs={24} md={20}>
              <Form.Item label="Search" name="search" className="mb-0">
                <Input
                  placeholder="Search by name, email, address, phone..."
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  allowClear
                  className="w-full"
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={4}>
              <Form.Item className="flex justify-end mb-0">
                <Button 
                  icon={<FilterOutlined />} 
                  onClick={handleFilterReset}
                  className="w-full md:w-auto"
                >
                  Reset
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      <Table 
        columns={columns} 
        dataSource={filteredClients} 
        rowKey="id" 
        loading={loading}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          onChange: (page, size) => {
            setCurrentPage(page);
            setPageSize(size || 10);
          },
          showSizeChanger: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
        }}
        scroll={{ x: 'max-content' }}
      />

      <Modal
        title={editingClient ? 'Edit Client' : 'Add Client'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={() => setIsModalVisible(false)}
        width={800}
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="Details" key="details">
            <Form form={form} layout="vertical">
              <Form.Item
                name="name"
                label="Name"
                rules={[{ required: true, message: 'Please input client name!' }]}
              >
                <Input />
              </Form.Item>
              <Form.Item
                name="email"
                label="Email"
                rules={[
                  { required: true, message: 'Please input client email!' },
                  { type: 'email', message: 'Please enter a valid email!' },
                ]}
              >
                <Input />
              </Form.Item>
              <Form.Item
                name="phone"
                label="Phone"
              >
                <Input />
              </Form.Item>
              <Form.Item
                name="address"
                label="Address"
              >
                <Input.TextArea />
              </Form.Item>
            </Form>
          </TabPane>
          {editingClient && (
            <TabPane tab="Supervisors" key="supervisors">
              <ClientSupervisorManager 
                clientId={editingClient.id} 
                onUpdate={fetchAvailableSupervisors}
              />
            </TabPane>
          )}
        </Tabs>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        title="Delete Client"
        open={deleteModalVisible}
        onOk={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
        confirmLoading={deleteLoading}
        okText="Delete"
        okButtonProps={{ 
          danger: true,
          disabled: !!deleteErrorData 
        }}
      >
        {deleteErrorData ? (
          <div>
            <p>{deleteErrorData.error}</p>
            <p>This client has {deleteErrorData.placements.length} active placement(s). Please move or delete them first.</p>
            <Button 
              type="primary"
              onClick={() => {
                handleDeleteCancel();
                setMovePlacementsData({
                  sourceClient: deletingClient!,
                  placements: deleteErrorData.placements
                });
              }}
              style={{ marginTop: '16px' }}
            >
              Move Placements
            </Button>
          </div>
        ) : (
          <p>Are you sure you want to delete client &ldquo;{deletingClient?.name}&rdquo;? This action cannot be undone.</p>
        )}
      </Modal>

      {/* Move Placements Modal */}
      {movePlacementsData && (
        <MovePlacementsModal
          sourceClient={movePlacementsData.sourceClient}
          placements={movePlacementsData.placements}
          availableClients={clients.filter(c => c.id !== movePlacementsData.sourceClient.id)}
          onClose={() => setMovePlacementsData(null)}
          onSuccess={() => {
            setMovePlacementsData(null);
            fetchClients();
          }}
        />
      )}
    </>
  );
} 