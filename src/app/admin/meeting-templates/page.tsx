'use client';

import { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Upload, 
  Popconfirm, 
  Tag, 
  Space,
  Typography,
  App
} from 'antd';
import { 
  PlusOutlined, 
  UploadOutlined, 
  DeleteOutlined, 
  DownloadOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined 
} from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';

const { Title } = Typography;
const { TextArea } = Input;

interface MeetingTemplate {
  id: string;
  name: string;
  description?: string;
  fileName: string;
  s3Key: string;
  version: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  uploader: {
    id: string;
    name: string;
    email: string;
  };
}

export default function MeetingTemplatesPage() {
  const [templates, setTemplates] = useState<MeetingTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [modalOpen, setModalOpen] = useState(false);
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const { message: messageApi } = App.useApp();

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/meeting-templates');
      if (!response.ok) throw new Error('Failed to fetch templates');
      const data = await response.json();
      setTemplates(data);
    } catch (error) {
      console.error('Error fetching templates:', error);
      messageApi.error('Failed to fetch templates');
    } finally {
      setLoading(false);
    }
  };

  const handleUpload = async (values: { name: string; description: string; setAsActive: boolean }) => {
    if (fileList.length === 0) {
      messageApi.error('Please select a file to upload');
      return;
    }

    const file = fileList[0].originFileObj || fileList[0];
    if (!file) {
      messageApi.error('File not found. Please select a file again.');
      return;
    }

    console.log('File to upload:', file);
    console.log('File name:', file.name);
    console.log('File type:', file.type);

    try {
      setUploading(true);
      const formData = new FormData();
      formData.append('file', file as File);
      formData.append('name', values.name);
      formData.append('description', values.description || '');
      formData.append('setAsActive', values.setAsActive ? 'true' : 'false');

      const response = await fetch('/api/admin/meeting-templates', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to upload template');
      }

      messageApi.success('Template uploaded successfully');
      setModalOpen(false);
      form.resetFields();
      setFileList([]);
      fetchTemplates();
    } catch (error) {
      console.error('Error uploading template:', error);
      messageApi.error(error instanceof Error ? error.message : 'Failed to upload template');
    } finally {
      setUploading(false);
    }
  };

  const handleActivate = async (id: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/admin/meeting-templates/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive }),
      });

      if (!response.ok) throw new Error('Failed to update template');

      messageApi.success(`Template ${isActive ? 'activated' : 'deactivated'} successfully`);
      fetchTemplates();
    } catch (error) {
      console.error('Error updating template:', error);
      messageApi.error('Failed to update template');
    }
  };

  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/admin/meeting-templates/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Failed to delete template');

      messageApi.success('Template deleted successfully');
      fetchTemplates();
    } catch (error) {
      console.error('Error deleting template:', error);
      messageApi.error('Failed to delete template');
    }
  };

  const handleDownload = async (template: MeetingTemplate) => {
    try {
      const response = await fetch('/api/files', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ documentKey: template.s3Key }),
      });

      if (!response.ok) throw new Error('Failed to generate download URL');

      const data = await response.json();
      const link = document.createElement('a');
      link.href = data.url;
      link.download = template.fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error downloading template:', error);
      messageApi.error('Failed to download template');
    }
  };

  const uploadProps: UploadProps = {
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file) => {
      const isValidType = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ].includes(file.type);

      if (!isValidType) {
        messageApi.error('You can only upload PDF, DOC, or DOCX files!');
        return Upload.LIST_IGNORE;
      }

      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        messageApi.error('File must be smaller than 10MB!');
        return Upload.LIST_IGNORE;
      }

      // Create file object with proper structure
      const fileObj = {
        uid: file.uid || Date.now().toString(),
        name: file.name,
        status: 'done' as const,
        originFileObj: file,
      };
      console.log('fileObj', fileObj);
      setFileList([fileObj]);
      return false;
    },
    fileList,
    maxCount: 1,
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: MeetingTemplate) => (
        <Space direction="vertical" size={0}>
          <strong>{text}</strong>
          {record.description && <small>{record.description}</small>}
        </Space>
      ),
    },
    {
      title: 'File Name',
      dataIndex: 'fileName',
      key: 'fileName',
    },
    {
      title: 'Version',
      dataIndex: 'version',
      key: 'version',
      render: (version: number) => `v${version}`,
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'default'}>
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Uploaded By',
      dataIndex: ['uploader', 'name'],
      key: 'uploader',
    },
    {
      title: 'Upload Date',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: unknown, record: MeetingTemplate) => (
        <Space>
          <Button
            icon={<DownloadOutlined />}
            onClick={() => handleDownload(record)}
            size="small"
          >
            Download
          </Button>
          
          {record.isActive ? (
            <Button
              icon={<CloseCircleOutlined />}
              onClick={() => handleActivate(record.id, false)}
              size="small"
              danger
            >
              Deactivate
            </Button>
          ) : (
            <Button
              icon={<CheckCircleOutlined />}
              onClick={() => handleActivate(record.id, true)}
              size="small"
              type="primary"
            >
              Activate
            </Button>
          )}
          
          <Popconfirm
            title="Are you sure you want to delete this template?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Button
              icon={<DeleteOutlined />}
              size="small"
              danger
            >
              Delete
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <Title level={2}>Meeting Templates</Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setModalOpen(true)}
        >
          Upload New Template
        </Button>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={templates}
          rowKey="id"
          loading={loading}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} templates`,
          }}
        />
      </Card>

      <Modal
        title="Upload New Meeting Template"
        open={modalOpen}
        onCancel={() => {
          setModalOpen(false);
          form.resetFields();
          setFileList([]);
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleUpload}
        >
          <Form.Item
            name="name"
            label="Template Name"
            rules={[{ required: true, message: 'Please enter template name' }]}
          >
            <Input placeholder="e.g., Monthly Mentor Meeting Template" />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
          >
            <TextArea
              rows={3}
              placeholder="Brief description of this template..."
            />
          </Form.Item>

          <Form.Item
            label="Template File"
          >
            <Upload {...uploadProps}>
              <Button icon={<UploadOutlined />}>Select File</Button>
            </Upload>
            <div className="text-gray-500 text-sm mt-2">
              Supported formats: PDF, DOC, DOCX (Max: 10MB)
            </div>
          </Form.Item>

          <Form.Item
            name="setAsActive"
            valuePropName="checked"
          >
            <input type="checkbox" className="mr-2" />
            Set as active template
          </Form.Item>

          <Form.Item className="mb-0">
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={uploading}
              >
                Upload Template
              </Button>
              <Button
                onClick={() => {
                  setModalOpen(false);
                  form.resetFields();
                  setFileList([]);
                }}
              >
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
} 