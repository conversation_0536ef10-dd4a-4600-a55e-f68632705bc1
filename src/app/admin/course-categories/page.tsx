"use client";

import { useState, useEffect } from "react";
import {
  Button,
  Table,
  Space,
  Modal,
  Form,
  Input,
  App,
  Select,
  Typography,
  Card,
  Row,
  Col,
  Switch,
  ColorPicker,
  message
} from "antd";
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  SearchOutlined,
  FilterOutlined
} from "@ant-design/icons";
import { CourseCategory } from "@/types";
import ImageUpload from "@/components/common/ImageUpload";
import RichTextEditor from "@/components/common/RichTextEditor";
import { S3_FOLDERS } from "@/lib/s3";

const { Title, Text } = Typography;

export default function AdminCourseCategoriesPage() {
  const { message: messageApi } = App.useApp();
  const [categories, setCategories] = useState<CourseCategory[]>([]);
  const [filteredCategories, setFilteredCategories] = useState<CourseCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const [form] = Form.useForm();
  const [filterForm] = Form.useForm();
  const [editingId, setEditingId] = useState<string | null>(null);
  const [searchText, setSearchText] = useState('');
  const [selectedType, setSelectedType] = useState<string | null>(null);

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    let filtered = categories;
    
    if (searchText) {
      filtered = filtered.filter(category => 
        category.name.toLowerCase().includes(searchText.toLowerCase()) ||
        category.description?.toLowerCase().includes(searchText.toLowerCase())
      );
    }
    
    if (selectedType) {
      if (selectedType === 'primary') {
        filtered = filtered.filter(category => !category.isSecondCategory);
      } else if (selectedType === 'secondary') {
        filtered = filtered.filter(category => category.isSecondCategory);
      }
    }
    
    setFilteredCategories(filtered);
  }, [searchText, selectedType, categories]);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/admin/course-categories");
      const data = await response.json();
      setCategories(data);
      setFilteredCategories(data);
    } catch (error) {
      setCategories([]);
      setFilteredCategories([]);
      console.error('Error fetching categories:', error);
      messageApi.error("Failed to fetch categories");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      setSubmitting(true);
      const url = editingId
        ? `/api/admin/course-categories/${editingId}`
        : "/api/admin/course-categories";

      const method = editingId ? "PUT" : "POST";
            
      const color = values.color?.metaColor ? 
      `rgb(${values.color.metaColor.r}, ${values.color.metaColor.g}, ${values.color.metaColor.b})` : 
      values.color;


      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ...values, color }),
      });

      if (!response.ok) throw new Error("Failed to save category");

      messageApi.success(
        `Category ${editingId ? "updated" : "created"} successfully`
      );
      setIsModalVisible(false);
      form.resetFields();
      setEditingId(null);
      fetchCategories();
    } catch (error) {
      console.error('Error saving category:', error);
      messageApi.error("Failed to save category");
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/admin/course-categories/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete category");

      messageApi.success("Category deleted successfully");
      fetchCategories();
    } catch (error) {
      console.error('Error deleting category:', error);
      messageApi.error("Failed to delete category");
    }
  };

  const handleEdit = (record: CourseCategory) => {
    setEditingId(record.id);
    form.setFieldsValue(record);
    setIsModalVisible(true);
  };

  const handleFilterReset = () => {
    filterForm.resetFields();
    setSelectedType(null);
    setSearchText('');
  };

  const columns = [
    {
      title: "Category Name",
      dataIndex: "name",
      key: "name",
      render: (name: string, record: CourseCategory) => (
        <div className="flex items-center gap-3">
          {record.imageUrl && (
            <img 
              src={record.imageUrl} 
              alt={name}
              className="w-12 h-12 rounded object-contain"
            />
          )}
          <div>
            <div className="font-medium">{name}</div>
            <div className="flex items-center gap-2">
              <Text type="secondary" className="text-sm">
                {record.description || "No description"}
              </Text>
            </div>
          </div>
        </div>
      ),
    },
    {
      title: "Type",
      key: "type",
      render: (_: unknown, record: CourseCategory) => (
        <div className="flex items-center gap-2">
          <span 
            className="px-2 py-1 rounded text-xs font-medium"
            style={{ 
              backgroundColor: record.isSecondCategory ? '#f0f9ff' : '#f0fdf4',
              color: record.isSecondCategory ? '#0369a1' : '#166534'
            }}
          >
            {record.isSecondCategory ? 'Secondary' : 'Primary'}
          </span>
        </div>
      ),
    },
    {
      title: "Color",
      dataIndex: "color",
      key: "color",
      render: (color: string) => (
        <div className="flex items-center gap-2">
          <div 
            className="w-6 h-6 rounded border"
            style={{ backgroundColor: color || '#666' }}
          />
          <span className="text-sm text-gray-600">{color || '#666'}</span>
        </div>
      ),
    },
    {
      title: "Actions",
      key: "actions",
      render: (_: unknown, record: CourseCategory) => (
        <Space>
          <Button 
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record)}
            size="small"
          />
          <Button
            icon={<DeleteOutlined />}
            danger
            onClick={() => handleDelete(record.id)}
            size="small"
          />
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-4">
        <Title level={2}>Course Categories Management</Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            setEditingId(null);
            form.resetFields();
            setIsModalVisible(true);
          }}
          style={{ backgroundColor: 'rgb(199, 100, 27)' }}
        >
          Add Category
        </Button>
      </div>

      <Card className="mb-4">
        <Form form={filterForm} layout="horizontal">
          <Row gutter={16}>
            <Col xs={24} sm={8} md={6}>
              <Form.Item label="Search" name="search">
                <Input
                  placeholder="Search categories..."
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  allowClear
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8} md={6}>
              <Form.Item label="Type" name="type">
                <Select 
                  placeholder="Filter by type"
                  allowClear
                  onChange={(value) => setSelectedType(value)}
                  value={selectedType}
                >
                  <Select.Option value="primary">Primary Categories</Select.Option>
                  <Select.Option value="secondary">Secondary Categories</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={8} md={12}>
              <Form.Item className="flex justify-end">
                <Button icon={<FilterOutlined />} onClick={handleFilterReset}>Reset</Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      <Table
        loading={loading}
        columns={columns}
        dataSource={filteredCategories}
        rowKey="id"
        pagination={{
          showSizeChanger: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
        }}
      />

      <Modal
        title={editingId ? "Edit Category" : "Create Category"}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
          setEditingId(null);
        }}
        onOk={form.submit}
        confirmLoading={submitting}
        width={600}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item
            name="name"
            label="Category Name"
            rules={[{ required: true, message: "Please input category name!" }]}
          >
            <Input placeholder="Enter category name" />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
          >
            <Input.TextArea 
              placeholder="Enter category description" 
              rows={3}
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="color"
                label="Color"
              >
                <ColorPicker />
              </Form.Item>
            </Col>
            {/* <Col span={12}>
              <Form.Item
                name="isClassroom"
                label="Classroom Course"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col> */}
          </Row>

          <Form.Item
            name="isSecondCategory"
            label="Secondary Category"
            valuePropName="checked"
            extra="Enable this to make this category available as a secondary category for courses"
          >
            <Switch />
          </Form.Item>

          <Form.Item name="imageUrl" label="Category Image">
            <ImageUpload
              folder={S3_FOLDERS.COURSES}
              aspectRatio={16 / 9}
              maxSize={5}
              className="w-[200px] h-[113px]"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}