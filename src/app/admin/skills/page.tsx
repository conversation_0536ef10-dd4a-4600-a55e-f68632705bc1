"use client";

import { useEffect, useState } from "react";
import { Table, Button, Modal, Form, Input, Select, Breadcrumb, App, Space, InputNumber } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined, ArrowLeftOutlined, ExclamationCircleFilled } from "@ant-design/icons";
import { BreadcrumbItemProps } from "antd/lib/breadcrumb/BreadcrumbItem";

const { Option } = Select;

interface PracticeArea {
  id: string;
  name: string;
  createdAt: string;
}

interface PracticeSkill {
  id: string;
  name: string;
  practiceAreaId: string;
  createdAt: string;
}

interface PracticeSkillGroup {
  id: string;
  name: string;
  practiceSkillId: string;
  createdAt: string;
}

interface PracticeSubSkill {
  id: string;
  name: string;
  practiceSkillGroupId: string;
  practiceSubSkillType: 'sqe' | 'tc' | 'both';
  minSuggestedEntryCount: number;
  order: number;
  createdAt: string;
}

interface ModalState {
  open: boolean;
  type: string;
  data: PracticeArea | PracticeSkill | PracticeSkillGroup | PracticeSubSkill | null;
}

interface CustomBreadcrumbItem extends Omit<BreadcrumbItemProps, 'style'> {
  title: string;
  onClick?: () => void;
  style?: React.CSSProperties;
}

export default function AdminSkillsPage() {
  const { message, modal: contextModal } = App.useApp();
  const [areas, setAreas] = useState<PracticeArea[]>([]);
  const [skills, setSkills] = useState<PracticeSkill[]>([]);
  const [groups, setGroups] = useState<PracticeSkillGroup[]>([]);
  const [subSkills, setSubSkills] = useState<PracticeSubSkill[]>([]);

  const [selectedArea, setSelectedArea] = useState<PracticeArea | null>(null);
  const [selectedSkill, setSelectedSkill] = useState<PracticeSkill | null>(null);
  const [selectedGroup, setSelectedGroup] = useState<PracticeSkillGroup | null>(null);

  const [modalState, setModalState] = useState<ModalState>({ open: false, type: '', data: null });
  const [form] = Form.useForm();

  // Fetch functions
  const fetchAreas = async () => {
    const res = await fetch("/api/admin/practice-areas");
    setAreas(await res.json());
  };
  const fetchSkills = async (areaId: string) => {
    if (!areaId) return setSkills([]);
    const res = await fetch(`/api/admin/practice-skills?practiceAreaId=${areaId}`);
    setSkills(await res.json());
  };
  const fetchGroups = async (skillId: string) => {
    if (!skillId) return setGroups([]);
    const res = await fetch(`/api/admin/practice-skill-groups?practiceSkillId=${skillId}`);
    setGroups(await res.json());
  };
  const fetchSubSkills = async (groupId: string) => {
    if (!groupId) return setSubSkills([]);
    const res = await fetch(`/api/admin/practice-sub-skills?practiceSkillGroupId=${groupId}`);
    const data = await res.json();
    // Ensure we're setting an array, even if the response is empty or malformed
    setSubSkills(Array.isArray(data) ? data : []);
  };

  useEffect(() => { fetchAreas(); }, []);
  useEffect(() => { if (selectedArea) fetchSkills(selectedArea.id); else setSkills([]); }, [selectedArea]);
  useEffect(() => { if (selectedSkill) fetchGroups(selectedSkill.id); else setGroups([]); }, [selectedSkill]);
  useEffect(() => { if (selectedGroup) fetchSubSkills(selectedGroup.id); else setSubSkills([]); }, [selectedGroup]);

  // Handlers for navigation
  const handleSelectArea = (area: PracticeArea) => { setSelectedArea(area); setSelectedSkill(null); setSelectedGroup(null); };
  const handleSelectSkill = (skill: PracticeSkill) => { setSelectedSkill(skill); setSelectedGroup(null); };
  const handleSelectGroup = (group: PracticeSkillGroup) => { setSelectedGroup(group); };

  // Modal openers
  const openModal = (type: string, data: PracticeArea | PracticeSkill | PracticeSkillGroup | PracticeSubSkill | null = null) => { 
    setModalState({ open: true, type, data }); 
    form.resetFields(); 
    if (data) form.setFieldsValue(data); 
  };
  const closeModal = () => { setModalState({ open: false, type: '', data: null }); form.resetFields(); };

  // CRUD actions
  // Practice Area
  const handleAreaSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (modalState.type === 'edit-area' && modalState.data) {
        const response = await fetch('/api/admin/practice-areas', { 
          method: 'PUT', 
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ id: (modalState.data as PracticeArea).id, ...values }) 
        });
        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Failed to update practice area');
        }
        message.success('Updated');
      } else {
        const response = await fetch('/api/admin/practice-areas', { 
          method: 'POST', 
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(values) 
        });
        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Failed to create practice area');
        }
        message.success('Created');
      }
      closeModal(); 
      fetchAreas();
    } catch (error: unknown) {
      message.error(error instanceof Error ? error.message : 'Failed to submit practice area');
    }
  };

  const handleAreaDelete = async (id: string) => {
    contextModal.confirm({
      title: 'Are you sure you want to delete this practice area?',
      icon: <ExclamationCircleFilled />,
      content: 'This will also delete all associated skills, skill groups, and sub-skills. This action cannot be undone.',
      okText: 'Yes, Delete',
      okType: 'danger',
      cancelText: 'No, Cancel',
      async onOk() {
        try {
          const response = await fetch('/api/admin/practice-areas', { 
            method: 'DELETE', 
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id }) 
          });
          
          if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || 'Failed to delete practice area');
          }
          
          message.success('Practice area and all associated items deleted successfully');
          if (selectedArea && selectedArea.id === id) setSelectedArea(null);
          fetchAreas();
        } catch (error: unknown) {
          message.error(error instanceof Error ? error.message : 'Failed to delete practice area');
        }
      },
    });
  };
  // Practice Skill
  const handleSkillSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (modalState.type === 'edit-skill' && modalState.data) {
        const response = await fetch('/api/admin/practice-skills', { 
          method: 'PUT', 
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ id: (modalState.data as PracticeSkill).id, ...values }) 
        });
        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Failed to update practice skill');
        }
        message.success('Updated');
      } else if (selectedArea) {
        const response = await fetch('/api/admin/practice-skills', { 
          method: 'POST', 
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ ...values, practiceAreaId: selectedArea.id }) 
        });
        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Failed to create practice skill');
        }
        message.success('Created');
      }
      closeModal(); 
      if (selectedArea) {
        fetchSkills(selectedArea.id);
      }
    } catch (error: unknown) {
      message.error(error instanceof Error ? error.message : 'Failed to submit practice skill');
    }
  };
  const handleSkillDelete = async (id: string) => {
    try {
      const response = await fetch('/api/admin/practice-skills', { 
        method: 'DELETE', 
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id }) 
      });
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete practice skill');
      }
      message.success('Deleted');
      if (selectedSkill && selectedSkill.id === id) setSelectedSkill(null);
      if (selectedArea) {
        fetchSkills(selectedArea.id);
      }
    } catch (error: unknown) {
      message.error(error instanceof Error ? error.message : 'Failed to delete practice skill');
    }
  };
  // Practice Skill Group
  const handleGroupSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (modalState.type === 'edit-group' && modalState.data) {
        const response = await fetch('/api/admin/practice-skill-groups', { 
          method: 'PUT', 
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ id: (modalState.data as PracticeSkillGroup).id, ...values }) 
        });
        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Failed to update practice skill group');
        }
        message.success('Updated');
      } else if (selectedSkill) {
        const response = await fetch('/api/admin/practice-skill-groups', { 
          method: 'POST', 
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ ...values, practiceSkillId: selectedSkill.id }) 
        });
        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Failed to create practice skill group');
        }
        message.success('Created');
      }
      closeModal(); 
      if (selectedSkill) {
        fetchGroups(selectedSkill.id);
      }
    } catch (error: unknown) {
      message.error(error instanceof Error ? error.message : 'Failed to submit practice skill group');
    }
  };
  const handleGroupDelete = async (id: string) => {
    try {
      const response = await fetch('/api/admin/practice-skill-groups', { 
        method: 'DELETE', 
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id }) 
      });
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete practice skill group');
      }
      message.success('Deleted');
      if (selectedGroup && selectedGroup.id === id) setSelectedGroup(null);
      if (selectedSkill) {
        fetchGroups(selectedSkill.id);
      }
    } catch (error: unknown) {
      message.error(error instanceof Error ? error.message : 'Failed to delete practice skill group');
    }
  };
  // Practice Sub-Skill
  const handleSubSkillSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (modalState.type === 'edit-subskill' && modalState.data) {
        const response = await fetch('/api/admin/practice-sub-skills', { 
          method: 'PUT', 
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ id: (modalState.data as PracticeSubSkill).id, ...values }) 
        });
        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Failed to update practice sub-skill');
        }
        message.success('Updated');
      } else if (selectedGroup) {
        const response = await fetch('/api/admin/practice-sub-skills', { 
          method: 'POST', 
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ ...values, practiceSkillGroupId: selectedGroup.id }) 
        });
        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Failed to create practice sub-skill');
        }
        message.success('Created');
      }
      closeModal(); 
      if (selectedGroup) {
        fetchSubSkills(selectedGroup.id);
      }
    } catch (error: unknown) {
      message.error(error instanceof Error ? error.message : 'Failed to submit practice sub-skill');
    }
  };
  const handleSubSkillDelete = async (id: string) => {
    try {
      const response = await fetch('/api/admin/practice-sub-skills', { 
        method: 'DELETE', 
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id }) 
      });
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete practice sub-skill');
      }
      message.success('Deleted');
      if (selectedGroup) {
        fetchSubSkills(selectedGroup.id);
      }
    } catch (error: unknown) {
      message.error(error instanceof Error ? error.message : 'Failed to delete practice sub-skill');
    }
  };

  // Table columns
  const areaColumns = [
    { title: 'Name', dataIndex: 'name', key: 'name' },
    { title: 'Actions', key: 'actions', render: (_: unknown, r: PracticeArea) => (
      <Space>
        <Button icon={<EditOutlined />} onClick={() => openModal('edit-area', r)} />
        <Button icon={<DeleteOutlined />} danger onClick={() => handleAreaDelete(r.id)} />
        <Button onClick={() => handleSelectArea(r)}>View</Button>
      </Space>
    ) },
  ];
  const skillColumns = [
    { title: 'Name', dataIndex: 'name', key: 'name' },
    { title: 'Actions', key: 'actions', render: (_: unknown, r: PracticeSkill) => (
      <Space>
        <Button icon={<EditOutlined />} onClick={() => openModal('edit-skill', r)} />
        <Button icon={<DeleteOutlined />} danger onClick={() => handleSkillDelete(r.id)} />
        <Button onClick={() => handleSelectSkill(r)}>View</Button>
      </Space>
    ) },
  ];
  const groupColumns = [
    { title: 'Name', dataIndex: 'name', key: 'name' },
    { title: 'Actions', key: 'actions', render: (_: unknown, r: PracticeSkillGroup) => (
      <Space>
        <Button icon={<EditOutlined />} onClick={() => openModal('edit-group', r)} />
        <Button icon={<DeleteOutlined />} danger onClick={() => handleGroupDelete(r.id)} />
        <Button onClick={() => handleSelectGroup(r)}>View</Button>
      </Space>
    ) },
  ];
  const subSkillColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      width: '40%',
      ellipsis: true,
      render: (text: string) => (
        <div className="font-medium">
          <div className="truncate hover:text-clip hover:whitespace-normal" title={text}>
            {text}
          </div>
        </div>
      )
    },
    { 
      title: 'Type', 
      dataIndex: 'practiceSubSkillType', 
      key: 'practiceSubSkillType',
      width: '15%',
      align: 'center' as const,
      render: (type: 'sqe' | 'tc' | 'both') => {
        const typeMap: Record<'sqe' | 'tc' | 'both', string> = {
          sqe: 'SQE',
          tc: 'TC',
          both: 'Both'
        };
        return <span className="capitalize">{typeMap[type]}</span>;
      }
    },
    { 
      title: 'Min Entry', 
      dataIndex: 'minSuggestedEntryCount', 
      key: 'minSuggestedEntryCount',
      width: '15%',
      align: 'center' as const,
      render: (count: number) => count
    },
    { 
      title: 'Order', 
      dataIndex: 'order', 
      key: 'order',
      width: '10%',
      align: 'center' as const,
      render: (order: number) => order
    },
    {
      title: 'Actions',
      key: 'actions',
      width: '20%',
      align: 'center' as const,
      render: (_: unknown, r: PracticeSubSkill) => (
        <Space>
          <Button
            icon={<EditOutlined />}
            onClick={() => openModal('edit-subskill', r)}
          />
          <Button
            icon={<DeleteOutlined />}
            danger
            onClick={() => handleSubSkillDelete(r.id)}
          />
        </Space>
      )
    }
  ];

  // Breadcrumbs
  const crumbs: CustomBreadcrumbItem[] = [
    { title: 'Practice Areas', onClick: () => { setSelectedArea(null); setSelectedSkill(null); setSelectedGroup(null); } },
  ];
  if (selectedArea) crumbs.push({ title: selectedArea.name, onClick: () => { setSelectedSkill(null); setSelectedGroup(null); } });
  if (selectedSkill) crumbs.push({ title: selectedSkill.name, onClick: () => { setSelectedGroup(null); } });
  if (selectedGroup) crumbs.push({ title: selectedGroup.name, onClick: () => {} });

  // Modal content
  let modalTitle = '';

  const renderModalContent = () => {
    switch (modalState.type) {
      case 'edit-area':
      case 'add-area':
        modalTitle = modalState.type === 'edit-area' ? 'Edit Practice Area' : 'Add Practice Area';
        return (
          <Form.Item name="name" label="Name" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
        );

      case 'edit-skill':
      case 'add-skill':
        modalTitle = modalState.type === 'edit-skill' ? 'Edit Practice Skill' : 'Add Practice Skill';
        return (
          <Form.Item name="name" label="Name" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
        );

      case 'edit-group':
      case 'add-group':
        modalTitle = modalState.type === 'edit-group' ? 'Edit Practice Skill Group' : 'Add Practice Skill Group';
        return (
          <Form.Item name="name" label="Name" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
        );

      case 'edit-subskill':
      case 'add-subskill':
        modalTitle = modalState.type === 'edit-subskill' ? 'Edit Practice Sub-Skill' : 'Add Practice Sub-Skill';
        return (
          <>
            <Form.Item name="name" label="Name" rules={[{ required: true }]}>
              <Input />
            </Form.Item>
            <Form.Item name="practiceSubSkillType" label="Type" rules={[{ required: true }]}>
              <Select>
                <Option value="sqe">SQE</Option>
                <Option value="tc">TC</Option>
                <Option value="both">Both</Option>
              </Select>
            </Form.Item>
            <Form.Item name="minSuggestedEntryCount" label="Min Suggested Entry Count" rules={[{ required: true }]}>
              <InputNumber min={1} style={{ width: '100%' }} />
            </Form.Item>
            <Form.Item name="order" label="Order" rules={[{ required: true }]}>
              <InputNumber min={0} style={{ width: '100%' }} />
            </Form.Item>
          </>
        );

      default:
        return null;
    }
  };

  return (
    <div style={{ maxWidth: 1200, margin: '0 auto', padding: 24 }}>
      <Breadcrumb style={{ marginBottom: 16 }}>
        {crumbs.map((c, i) => (
          <Breadcrumb.Item 
            key={i} 
            onClick={c.onClick} 
            className={c.onClick ? 'cursor-pointer' : ''}
          >
            {c.title}
          </Breadcrumb.Item>
        ))}
      </Breadcrumb>
      {!selectedArea && (
        <>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
            <h2 className="text-2xl font-bold">Practice Areas</h2>
            <Button icon={<PlusOutlined />} type="primary" onClick={() => openModal('add-area')}>Add</Button>
          </div>
          <Table columns={areaColumns} dataSource={areas} rowKey="id" pagination={false} scroll={{ x: 'max-content' }} />
        </>
      )}
      {selectedArea && !selectedSkill && (
        <>
          <Button icon={<ArrowLeftOutlined />} onClick={() => setSelectedArea(null)} style={{ marginBottom: 16 }}>Back</Button>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
            <h2 className="text-2xl font-bold max-w-md truncate">Practice Skills in {selectedArea.name}</h2>
            <Button icon={<PlusOutlined />} type="primary" onClick={() => openModal('add-skill')}>Add</Button>
          </div>
          <Table columns={skillColumns} dataSource={[...skills].sort((a, b) => a.name.localeCompare(b.name))} rowKey="id" pagination={false} scroll={{ x: 'max-content' }} />
        </>
      )}
      {selectedSkill && !selectedGroup && (
        <>
          <Button icon={<ArrowLeftOutlined />} onClick={() => setSelectedSkill(null)} style={{ marginBottom: 16 }}>Back</Button>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
            <h2 className="text-2xl font-bold max-w-md truncate">Practice Skill Groups in {selectedSkill.name}</h2>
            <Button icon={<PlusOutlined />} type="primary" onClick={() => openModal('add-group')}>Add</Button>
          </div>
          <Table columns={groupColumns} dataSource={groups} rowKey="id" pagination={false} scroll={{ x: 'max-content' }} />
        </>
      )}
      {selectedGroup && (
        <>
          <Button icon={<ArrowLeftOutlined />} onClick={() => setSelectedGroup(null)} style={{ marginBottom: 16 }}>Back</Button>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
            <h2 className="text-2xl font-bold max-w-2xl truncate hover:text-clip hover:whitespace-normal" title={selectedGroup.name}>
              Practice Sub-Skills in {selectedGroup.name}
            </h2>
            <Button icon={<PlusOutlined />} type="primary" onClick={() => openModal('add-subskill')}>Add</Button>
      </div>
          <Table 
            columns={subSkillColumns} 
            dataSource={subSkills} 
            rowKey="id" 
            pagination={false}
            scroll={{ x: 'max-content' }}
          />
        </>
      )}
      <Modal
        open={modalState.open}
        title={modalTitle}
        onCancel={closeModal}
        onOk={async () => {
          if (modalState.type.includes('area')) await handleAreaSubmit();
          else if (modalState.type.includes('skill') && !modalState.type.includes('sub')) await handleSkillSubmit();
          else if (modalState.type.includes('group')) await handleGroupSubmit();
          else if (modalState.type.includes('subskill')) await handleSubSkillSubmit();
        }}
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          {renderModalContent()}
        </Form>
      </Modal>
    </div>
  );
} 