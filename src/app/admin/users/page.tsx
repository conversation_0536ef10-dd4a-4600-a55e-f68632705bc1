'use client';

import { useState, useEffect } from 'react';
import { Table, Button, Modal, Form, Input, Select, App, Checkbox, Space, Card, Row, Col } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined, DownloadOutlined, FilterOutlined, KeyOutlined } from '@ant-design/icons';
import { UserRole, type User } from '@/types';
import { useRouter } from 'next/navigation';
import moment from 'moment';
import * as XLSX from 'xlsx';
import ChangePasswordModal from '@/components/admin/ChangePasswordModal';

const { Option } = Select;

export default function UsersPage() {
  const router = useRouter();
  const { message } = App.useApp();
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [filterForm] = Form.useForm();
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [selectedRole, setSelectedRole] = useState<string | null>(null);
  const [isPasswordModalVisible, setIsPasswordModalVisible] = useState(false);
  const [userToUpdatePassword, setUserToUpdatePassword] = useState<User | null>(null);
  const [isPasswordLoading, setIsPasswordLoading] = useState(false);

  // Fetch users when component mounts
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/users');
        if (response.ok) {
          const data = await response.json();
          setUsers(data);
          setFilteredUsers(data);
        } else {
          message.error('Failed to fetch users');
        }
      } catch (error) {
        console.error('Error fetching users:', error);
        message.error('Failed to fetch users');
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, []);

  // Filter users when search text or role changes
  useEffect(() => {
    let filtered = users.filter(user => 
      (user.name?.toLowerCase().includes(searchText.toLowerCase()) ||
      user.email?.toLowerCase().includes(searchText.toLowerCase()) ||
      user.role?.toLowerCase().includes(searchText.toLowerCase()) ||
      user.address?.toLowerCase().includes(searchText.toLowerCase()))
    );
    
    if (selectedRole) {
      filtered = filtered.filter(user => user.role === selectedRole);
    }
    
    setFilteredUsers(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  }, [searchText, selectedRole, users]);

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
    },
    {
      title: 'Bio',
      dataIndex: 'address',
      key: 'address',
      width: 300,
      render: (address: string) => {
        if (!address) return '-';
        
        return (
          <div 
            style={{ 
              whiteSpace: 'pre-wrap',
              overflow: 'hidden',
              display: '-webkit-box',
              WebkitLineClamp: 4,
              WebkitBoxOrient: 'vertical',
              wordBreak: 'break-word',
              maxWidth: '280px'
            }}
            title={address}
          >
            {address}
          </div>
        );
      },
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => moment(date).format('DD/MM/YYYY HH:mm'),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: unknown, record: User) => (
        <div className="space-x-2">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              handleEdit(record);
            }}
            className="ant-btn"
          />
          <Button
            type="text"
            icon={<KeyOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              handlePasswordUpdate(record);
            }}
            className="ant-btn"
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              showDeleteConfirm(record);
            }}
            className="ant-btn"
          />
          <Button
            type="link"
            onClick={(e) => {
              e.stopPropagation();
              router.push(`/admin/users/${record.id}`);
            }}
            className="ant-btn"
          >
            View
          </Button>
        </div>
      ),
    },
  ];

  const handleAdd = () => {
    setEditingUser(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (user: User) => {
    setEditingUser(user);
    form.setFieldsValue({
      name: user.name,
      email: user.email,
      role: user.role,
      traineeLevel: user.traineeLevel,
      isTrainingPrincipal: user.isTrainingPrincipal,
      address: user.address,
    });
    setIsModalVisible(true);
  };

  const showDeleteConfirm = (user: User) => {
    setUserToDelete(user);
    setDeleteModalVisible(true);
  };

  const handleDelete = async () => {
    if (!userToDelete) return;

    try {
      const response = await fetch(`/api/users/${userToDelete.id}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        message.success('User deleted successfully');
        setUsers(users.filter((u) => u.id !== userToDelete.id));
        setDeleteModalVisible(false);
        setUserToDelete(null);
      } else {
        message.error('Failed to delete user');
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      message.error('Failed to delete user');
    }
  };

  const handleModalOk = async () => {
    try {
      setSaveLoading(true);
      const values = await form.validateFields();
      
      if (editingUser) {
        // Update user
        const response = await fetch(`/api/users/${editingUser.id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(values),
        });
        
        if (response.ok) {
          const updatedUser = await response.json();
          setUsers(
            users.map((u) =>
              u.id === editingUser.id ? updatedUser : u
            )
          );
          message.success('User updated successfully');
          setIsModalVisible(false);
        } else {
          message.error('Failed to update user');
        }
      } else {
        // Create user
        const response = await fetch('/api/users', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(values),
        });
        
        console.log('User creation response:', {
          status: response.status,
          statusText: response.statusText,
        });

        if (response.ok) {
          const newUser = await response.json();
          console.log('New user data:', newUser);
          setUsers([...users, newUser]);
          message.success('User created successfully');
          setIsModalVisible(false);
        } else {
          const errorData = await response.json();
          console.error('User creation error:', errorData);
          message.error(errorData.error || 'Failed to create user');
        }
      }
    } catch (error) {
      console.error('Error saving user:', error);
      message.error('Failed to save user');
    } finally {
      setSaveLoading(false);
    }
  };

  const handlePasswordUpdate = (user: User) => {
    setUserToUpdatePassword(user);
    setIsPasswordModalVisible(true);
  };

  const handlePasswordChange = async (password: string) => {
    if (!userToUpdatePassword) return;
    
    setIsPasswordLoading(true);
    try {
      const response = await fetch(`/api/users/${userToUpdatePassword.id}/password`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password }),
      });

      if (response.ok) {
        message.success('Password updated successfully');
        setIsPasswordModalVisible(false);
        setUserToUpdatePassword(null);
      } else {
        const data = await response.json();
        message.error(data.error || 'Failed to update password');
      }
    } catch (error) {
      console.error('Error updating password:', error);
      message.error('An error occurred while updating the password');
    } finally {
      setIsPasswordLoading(false);
    }
  };

  // Export to Excel function
  const exportToExcel = () => {
    let worksheet;
    if (selectedRole === UserRole.TRAINEE) {
      worksheet = XLSX.utils.json_to_sheet(filteredUsers.map(user => ({
        Name: user.name,
        Email: user.email,
        Role: user.role,
        Bio: user.address || '-',
        'Created At': moment(user.createdAt).format('DD/MM/YYYY HH:mm'),
        'Qualification Route': user.qualificationRoute || '-',
        'Trainee Level': user.traineeLevel || '-',
      })));
    } else if (selectedRole === UserRole.MENTOR) {
      worksheet = XLSX.utils.json_to_sheet(filteredUsers.map(user => ({
        Name: user.name,
        Email: user.email,
        Role: user.role,
        Bio: user.address || '-',
        'Created At': moment(user.createdAt).format('DD/MM/YYYY HH:mm'),
        'Is Training Principal': user.isTrainingPrincipal ? 'Yes' : 'No',
      })));
    } else {
      worksheet = XLSX.utils.json_to_sheet(filteredUsers.map(user => ({
        Name: user.name,
        Email: user.email,
        Role: user.role,
        Bio: user.address || '-',
        'Created At': moment(user.createdAt).format('DD/MM/YYYY HH:mm'),
        ...(user.role === UserRole.TRAINEE && {
          'Qualification Route': user.qualificationRoute || '-',
          'Trainee Level': user.traineeLevel || '-',
        }),
        ...(user.role === UserRole.MENTOR && {
          'Is Training Principal': user.isTrainingPrincipal ? 'Yes' : 'No',
        }),
      })));
    }
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Users');
    XLSX.writeFile(workbook, `Users_${moment().format('YYYY-MM-DD')}.xlsx`);
    message.success('Users exported successfully');
  };

  // Handle filter reset
  const handleFilterReset = () => {
    filterForm.resetFields();
    setSelectedRole(null);
    setSearchText('');
  };

  return (
    <>
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold">Users</h1>
          <div className="flex items-center gap-4">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
              style={{ backgroundColor: 'rgb(199, 100, 27)' }}
            >
              Add User
            </Button>
          </div>
        </div>
        
        <Card className="mb-4">
          <Form form={filterForm} layout="horizontal">
            <Row gutter={16}>
              <Col xs={24} sm={8} md={6}>
                <Form.Item label="Search" name="search">
                  <Input
                    placeholder="Search users..."
                    prefix={<SearchOutlined />}
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    allowClear
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={8} md={6}>
                <Form.Item label="Role" name="role">
                  <Select 
                    placeholder="Filter by role"
                    allowClear
                    onChange={(value) => setSelectedRole(value)}
                    value={selectedRole}
                  >
                    <Option value={UserRole.ADMIN}>Admin</Option>
                    <Option value={UserRole.SUPERVISOR}>Supervisor</Option>
                    <Option value={UserRole.MENTOR}>Mentor</Option>
                    <Option value={UserRole.TRAINEE}>Trainee</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={8} md={12}>
                <Form.Item className="flex justify-end">
                  <Space>
                    <Button icon={<FilterOutlined />} onClick={handleFilterReset}>Reset</Button>
                    <Button 
                      type="primary" 
                      icon={<DownloadOutlined />} 
                      onClick={exportToExcel}
                      style={{ backgroundColor: 'green' }}
                    >
                      Export to Excel
                    </Button>
                  </Space>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Card>
      </div>

      <Table 
        columns={columns} 
        dataSource={filteredUsers} 
        rowKey="id" 
        loading={loading}
        scroll={{ x: 'max-content' }}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          onChange: (page, size) => {
            setCurrentPage(page);
            setPageSize(size || 10);
          },
          showSizeChanger: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
        }}
        onRow={(record) => {
          return {
            onClick: () => router.push(`/admin/users/${record.id}`),
            style: { cursor: 'pointer' } 
          };
        }}
      />

      <Modal
        title={editingUser ? 'Edit User' : 'Add User'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={() => setIsModalVisible(false)}
        confirmLoading={saveLoading}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="Name"
            rules={[{ required: true, message: 'Please input user name!' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="email"
            label="Email"
            rules={[
              { required: true, message: 'Please input user email!' },
              { type: 'email', message: 'Please enter a valid email!' },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="role"
            label="Role"
            rules={[{ required: true, message: 'Please select user role!' }]}
          >
            <Select>
              <Option value="ADMIN">Admin</Option>
              <Option value="SUPERVISOR">Supervisor</Option>
              <Option value="MENTOR">Mentor</Option>
              <Option value="TRAINEE">Trainee</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="address"
            label="Bio"
          >
            <Input.TextArea rows={3} placeholder="Enter bio/address information..." />
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.role !== currentValues.role}
          >
            {({ getFieldValue }) => {
              const role = getFieldValue('role');
              if (role === 'MENTOR') {
                return (
                  <Form.Item
                    name="isTrainingPrincipal"
                    valuePropName="checked"
                  >
                    <Checkbox>Is Training Principal</Checkbox>
                  </Form.Item>
                );
              }
              if (role === 'TRAINEE') {
                return (
                  <Form.Item
                    name="traineeLevel"
                    label="Trainee Level"
                    rules={[
                      { required: true, message: 'Please select trainee level!' },
                      { type: 'number', min: 1, max: 3, message: 'Level must be between 1 and 3' }
                    ]}
                  >
                    <Select>
                      <Option value={1}>Level 1</Option>
                      <Option value={2}>Level 2</Option>
                      <Option value={3}>Level 3</Option>
                    </Select>
                  </Form.Item>
                );
              }
              return null;
            }}
          </Form.Item>
          {!editingUser && (
            <Form.Item
              name="password"
              label="Password"
              rules={[{ required: true, message: 'Please input password!' }]}
            >
              <Input.Password />
            </Form.Item>
          )}
        </Form>
      </Modal>

      <Modal
        title="Delete User"
        open={deleteModalVisible}
        onOk={handleDelete}
        onCancel={() => {
          setDeleteModalVisible(false);
          setUserToDelete(null);
        }}
        okText="Delete"
        okButtonProps={{ danger: true }}
      >
        <p>Are you sure you want to delete user <strong>{userToDelete?.name}</strong>?</p>
        <p>This action cannot be undone.</p>
      </Modal>

      <ChangePasswordModal
        open={isPasswordModalVisible}
        onCancel={() => {
          setIsPasswordModalVisible(false);
          setUserToUpdatePassword(null);
        }}
        onConfirm={handlePasswordChange}
        confirmLoading={isPasswordLoading}
        title={`Change Password for ${userToUpdatePassword?.name}`}
      />
    </>
  );
} 