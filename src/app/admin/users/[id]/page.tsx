'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import { Card, Tabs, Typography, Button, Spin, Space, Breadcrumb, App } from 'antd';
import { DeleteOutlined, LockOutlined } from '@ant-design/icons';
import { User, UserRole, Placement } from '@/types';
import UserPlacementsTab from '@/components/users/UserPlacementsTab';
import AdminEntriesTable from '@/components/entries/AdminEntriesTable';
import UserInfoForm from '@/components/users/UserInfoForm';
import Link from 'next/link';
import AdminPortfolioTab from '@/components/users/AdminPortfolioTab';
import AdminMentorMeetingsTab from '@/components/users/AdminMentorMeetingsTab';
import AdminAppraisalsTab from '@/components/users/AdminAppraisalsTab';
import ChangePasswordModal from '@/components/admin/ChangePasswordModal';
import MentorMenteesTab from '@/components/users/MentorMenteesTab';
import SupervisorTraineesTab from '@/components/users/SupervisorTraineesTab';
import AdminQualificationsList from '@/components/qualifications/AdminQualificationsList';

const { Title, Text } = Typography;

export default function UserDetailPage() {
  const { id } = useParams();
  const { message } = App.useApp();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [placements, setPlacements] = useState<Placement[]>([]);
  const [isPasswordModalVisible, setIsPasswordModalVisible] = useState(false);
  const [isPasswordLoading, setIsPasswordLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  const fetchUserDetails = useCallback(async (tab?: string) => {
    if (!id) return;
    setLoading(true);
    try {
      const response = await fetch(`/api/users/${id}`);
      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
        setPlacements(userData.placements || []);
        if (tab) {
          setActiveTab(tab);
        }
      } else {
        message.error('Failed to fetch user details');
        setUser(null);
        setPlacements([]);
      }
    } catch (error) {
      console.error('Error fetching user details:', error);
      message.error('An error occurred while fetching user details');
      setUser(null);
      setPlacements([]);
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchUserDetails();
  }, [fetchUserDetails]);

  const handleDeleteAccount = async () => {
    if (!user) return;

    if (window.confirm(`Are you sure you want to delete ${user.name}'s account? This action cannot be undone.`)) {
      try {
        const response = await fetch(`/api/users/${id}`, {
          method: 'DELETE',
        });

        if (response.ok) {
          message.success('User account deleted successfully');
          window.location.href = '/admin/users';
        } else {
          message.error('Failed to delete user account');
        }
      } catch (error) {
        console.error('Error deleting user account:', error);
        message.error('An error occurred while deleting the user account');
      }
    }
  };

  const handleUnlockAccount = async () => {
    if (!user) return;

    try {
      message.success('Account unlocked successfully');
    } catch (error) {
      console.error('Error unlocking account:', error);
    }
  };

  const handlePasswordChange = async (password: string) => {
    if (!user) return;
    
    setIsPasswordLoading(true);
    try {
      const response = await fetch(`/api/users/${id}/password`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password }),
      });

      if (response.ok) {
        message.success('Password updated successfully');
        setIsPasswordModalVisible(false);
      } else {
        const data = await response.json();
        message.error(data.error || 'Failed to update password');
      }
    } catch (error) {
      console.error('Error updating password:', error);
      message.error('An error occurred while updating the password');
    } finally {
      setIsPasswordLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="p-6">
        <Card>
          <div className="text-center">
            <Title level={4}>User not found</Title>
            <Text>The requested user could not be found.</Text>
          </div>
        </Card>
      </div>
    );
  }

  const getTabItems = () => {
    if (!user) return [];

    const items = [
      {
        key: 'overview',
        label: 'OVERVIEW',
        children: (
          <UserInfoForm 
            user={user} 
            onUpdate={(updatedUser) => setUser(updatedUser)} 
          />
        )
      }
    ];

    if (user && ![UserRole.SUPERVISOR, UserRole.ADMIN, UserRole.MENTOR].includes(user.role)) {
      items.push({
        key: 'placements',
        label: 'PLACEMENTS',
        children: (
          <UserPlacementsTab 
            placements={placements} 
            userId={user.id}
            onPlacementUpdate={(tab) => fetchUserDetails(tab)}
          />
        )
      });
    }

    if (user.role === UserRole.TRAINEE) {
      items.push(
        {
          key: 'portfolio',
          label: 'PORTFOLIO',
          children: (
            <AdminPortfolioTab userId={user.id} />
          )
        },
        {
          key: 'mentor-meetings',
          label: 'MENTOR MEETINGS',
          children: (
            <AdminMentorMeetingsTab userId={user.id} />
          )
        },
        {
          key: 'appraisals',
          label: 'APPRAISALS',
          children: (
            <AdminAppraisalsTab userId={user.id} />
          )
        },
        {
          key: 'qualifications',
          label: 'QUALIFICATIONS',
          children: (
            <AdminQualificationsList userId={user.id} />
          )
        },
        {
          key: 'signoff-entries',
          label: 'ALL ENTRIES',
          children: (
            <AdminEntriesTable 
              userId={user.id}
              title="All Entries"
            />
          )
        }
      );
    }

    if (user.role === UserRole.MENTOR) {
      items.push({
        key: 'trainees',
        label: 'TRAINEES',
        children: (
          <MentorMenteesTab mentorId={user.id} />
        )
      });
    }

    if (user.role === UserRole.SUPERVISOR) {
      items.push({
        key: 'trainees',
        label: 'TRAINEES',
        children: (
          <SupervisorTraineesTab supervisorId={user.id} />
        )
      });
    }

    items.push({
      key: 'account',
      label: 'ACCOUNT',
      children: (
        <Card>
          <Button
            type="primary"
            icon={<LockOutlined />}
            onClick={() => setIsPasswordModalVisible(true)}
            className="mb-4 mr-2 w-full max-w-xs"
          >
            CHANGE PASSWORD
          </Button>
          {/* <Button
            type="primary"
            icon={<UnlockOutlined />}
            onClick={handleUnlockAccount}
            className="mb-4 w-full max-w-xs"
            style={{ backgroundColor: '#f0ad4e' }}
          >
            UNLOCK ACCOUNT
          </Button> */}
          <div className="text-gray-500 text-sm mb-4">
            Note: Once deleted data cannot be retrieved
          </div>
          <Button
            danger
            icon={<DeleteOutlined />}
            onClick={handleDeleteAccount}
            className="w-full max-w-xs"
          >
            DELETE ACCOUNT
          </Button>
        </Card>
      )
    });

    return items;
  };

  return (
    <>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Breadcrumb
          items={[
            { title: <Link href="/admin/users">Users</Link> },
            { title: user ? `${user.name} (${user.role})` : 'Loading...' },
          ]}
          className="mb-4"
        />
        <Tabs
          style={{ marginTop: 12 }}
          activeKey={activeTab}
          onChange={setActiveTab}
          type="card"
          items={getTabItems()}
          className="user-detail-tabs"
        />
      </Space>

      <ChangePasswordModal
        open={isPasswordModalVisible}
        onCancel={() => setIsPasswordModalVisible(false)}
        onConfirm={handlePasswordChange}
        confirmLoading={isPasswordLoading}
        title={`Change Password for ${user?.name}`}
      />
    </>
  );
} 