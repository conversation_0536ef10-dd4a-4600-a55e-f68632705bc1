'use client';

import { useState, useEffect } from 'react';
import { Table, Input, Typography, Card, Progress, Avatar, Button, Tabs } from 'antd';
import { useRouter } from 'next/navigation';
import dayjs from 'dayjs';
import { EyeOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;
const { Search } = Input;

interface Trainee {
  id: string;
  name: string;
  email: string;
  qualificationRoute: string;
  traineeLevel: number;
  lastActive: string;
  competencyProgress: number;
  relationshipStartDate: string;
  relationshipEndDate?: string;
  isActive: boolean;
  placementCount?: number; // Number of placements with this supervisor
}

export default function SupervisorTraineesPage() {
  const [currentTrainees, setCurrentTrainees] = useState<Trainee[]>([]);
  const [pastTrainees, setPastTrainees] = useState<Trainee[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const router = useRouter();
  
  useEffect(() => {
    let isMounted = true;
    
    const loadTrainees = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/supervisor/trainees?type=all');
        const data = await response.json();
        
        if (isMounted) {
          setCurrentTrainees(data.current || []);
          setPastTrainees(data.past || []);
        }
      } catch (error) {
        if (isMounted) {
          console.error('Error fetching trainees:', error);
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    loadTrainees();

    return () => {
      isMounted = false;
    };
  }, []);


  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase();
  };

  const handleViewSubmissions = (traineeId: string) => {
    router.push(`/supervisor/trainees/${traineeId}/submissions`);
  };

  const columns = [
    {
      title: 'NAME',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => (
        <div className="flex items-center gap-3">
          <Avatar>{getInitials(text)}</Avatar>
          <Text>{text}</Text>
        </div>
      ),
    },
    {
      title: 'EMAIL',
      dataIndex: 'email',
      key: 'email',
      render: (text: string) => <Text className="text-orange-600">{text}</Text>,
    },
    {
      title: 'QUALIFICATION ROUTE',
      dataIndex: 'qualificationRoute',
      key: 'qualificationRoute',
      render: (route: string) => route || 'N/A',
    },
    {
      title: 'LAST ACTIVE',
      dataIndex: 'lastActive',
      key: 'lastActive',
      render: (date: string) => dayjs(date).format('DD/MM/YYYY'),
    },
    {
      title: 'PLACEMENTS',
      dataIndex: 'placementCount',
      key: 'placementCount',
      render: (count: number) => count || 0,
    },
    {
      title: 'COMPETENCIES',
      dataIndex: 'competencyProgress',
      key: 'competencyProgress',
      render: (progress: number, record: Trainee) => (
        <div className="flex items-center gap-2">
          <span className="min-w-[45px]">{progress.toFixed(2)}%</span>
          <Progress 
            percent={progress} 
            showInfo={false}
            strokeColor={progress === 0 ? "#d1d5db" : progress < 5 ? "#ef4444" : progress < 25 ? "#f97316" : "#22c55e"}
            className="flex-1"
          />
        </div>
      ),
    },
    {
      title: 'ACTIONS',
      key: 'actions',
      render: (_: unknown, record: Trainee) => (
        <Button
          type="link"
          icon={<EyeOutlined />}
          onClick={() => handleViewSubmissions(record.id)}
        >
          View Submissions
        </Button>
      ),
    }
  ];

  const pastTraineeColumns = [
    ...columns,
    {
      title: 'RELATIONSHIP START',
      dataIndex: 'relationshipStartDate',
      key: 'relationshipStartDate',
      render: (date: string) => dayjs(date).format('DD/MM/YYYY'),
    },
    {
      title: 'RELATIONSHIP END',
      dataIndex: 'relationshipEndDate',
      key: 'relationshipEndDate',
      render: (date: string | null) => date ? dayjs(date).format('DD/MM/YYYY') : 'N/A',
    },
  ];

  const filteredCurrentTrainees = currentTrainees.filter(trainee =>
    trainee.name.toLowerCase().includes(searchText.toLowerCase()) ||
    trainee.email.toLowerCase().includes(searchText.toLowerCase())
  );

  const filteredPastTrainees = pastTrainees.filter(trainee =>
    trainee.name.toLowerCase().includes(searchText.toLowerCase()) ||
    trainee.email.toLowerCase().includes(searchText.toLowerCase())
  );

  const tabItems = [
    {
      key: 'current',
      label: `Current Trainees (${currentTrainees.length})`,
      children: (
        <Table
          columns={columns}
          dataSource={filteredCurrentTrainees}
          rowKey="id"
          loading={loading}
          scroll={{ x: 'max-content' }}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            pageSizeOptions: ['10', '20', '50'],
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
          }}
        />
      ),
    },
    {
      key: 'past',
      label: `Past Trainees (${pastTrainees.length})`,
      children: (
        <Table
          columns={pastTraineeColumns}
          dataSource={filteredPastTrainees}
          rowKey="id"
          loading={loading}
          scroll={{ x: 'max-content' }}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            pageSizeOptions: ['10', '20', '50'],
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
          }}
        />
      ),
    },
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <Title level={2} className="!mb-0">Trainees</Title>
        <Text>{currentTrainees.length + pastTrainees.length} members total</Text>
      </div>

      <Card>
        <div className="mb-4">
          <Search
            placeholder="Search within trainees..."
            allowClear
            onChange={e => setSearchText(e.target.value)}
            style={{ maxWidth: 400 }}
          />
        </div>

        <Tabs items={tabItems} defaultActiveKey="current" />
      </Card>
    </div>
  );
} 