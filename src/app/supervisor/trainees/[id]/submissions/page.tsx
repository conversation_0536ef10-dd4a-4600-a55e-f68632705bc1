'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import {
  Typography,
  Card,
  Button,
  Tag,
  Breadcrumb,
  Space,
  Row,
  Col,
  Empty,
  Spin,
  App
} from 'antd';
import {
  HomeOutlined,
  TeamOutlined,
  FileTextOutlined,
  EyeOutlined,
  CheckOutlined,
  CloseOutlined,
  RollbackOutlined
} from '@ant-design/icons';
import Link from 'next/link';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

interface Entry {
  id: string;
}

interface Submission {
  id: string;
  status: 'pending' | 'approved' | 'rejected';
  startDate: string;
  endDate: string;
  createdAt: string;
  entries: Entry[];
}

interface GroupedSubmissions {
  [key: string]: Submission[];
}

export default function TraineeSubmissionsPage() {
  const [submissions, setSubmissions] = useState<GroupedSubmissions>({});
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [trainee, setTrainee] = useState<{ name: string; email: string } | null>(null);
  const params = useParams();
  const traineeId = params.id as string;
  const { message } = App.useApp();

  useEffect(() => {
    fetchTraineeAndSubmissions();
  }, [traineeId]);

  const fetchTraineeAndSubmissions = async () => {
    try {
      setLoading(true);
      const [traineeResponse, submissionsResponse] = await Promise.all([
        fetch(`/api/trainees/${traineeId}`),
        fetch(`/api/trainees/${traineeId}/submissions`)
      ]);

      if (!traineeResponse.ok || !submissionsResponse.ok) {
        throw new Error('Failed to fetch data');
      }

      const traineeData = await traineeResponse.json();
      const submissionsData = await submissionsResponse.json();

      setTrainee(traineeData);
      
      const grouped = submissionsData.reduce((acc: GroupedSubmissions, submission: Submission) => {
        const monthYear = dayjs(submission.startDate).format('MMM YYYY');
        if (!acc[monthYear]) {
          acc[monthYear] = [];
        }
        acc[monthYear].push(submission);
        return acc;
      }, {});

      Object.keys(grouped).forEach(month => {
        grouped[month].sort((a: Submission, b: Submission) => 
          dayjs(b.createdAt).valueOf() - dayjs(a.createdAt).valueOf()
        );
      });

      setSubmissions(grouped);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (submissionId: string) => {
    try {
      setActionLoading(true);
      const response = await fetch(`/api/submissions/${submissionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'approved',
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to approve submission');
      }

      message.success('Submission approved successfully');
      fetchTraineeAndSubmissions();
    } catch (error) {
      message.error(error instanceof Error ? error.message : 'Failed to approve submission');
    } finally {
      setActionLoading(false);
    }
  };

  const handleReject = async (submissionId: string) => {
    try {
      setActionLoading(true);
      const response = await fetch(`/api/submissions/${submissionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'rejected',
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to reject submission');
      }

      message.success('Submission rejected successfully');
      fetchTraineeAndSubmissions();
    } catch (error) {
      message.error(error instanceof Error ? error.message : 'Failed to reject submission');
    } finally {
      setActionLoading(false);
    }
  };

  const handleUnapprove = async (submissionId: string) => {
    try {
      setActionLoading(true);
      const response = await fetch(`/api/submissions/${submissionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'pending',
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to unapprove submission');
      }

      message.success('Submission unapproved successfully');
      fetchTraineeAndSubmissions();
    } catch (error) {
      message.error(error instanceof Error ? error.message : 'Failed to unapprove submission');
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusTag = (status: Submission['status']) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return <Tag color="processing">Pending</Tag>;
      case 'approved':
        return <Tag color="success">Approved</Tag>;
      case 'rejected':
        return <Tag color="error">Rejected</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  const handleViewSubmission = (submissionId: string) => {
    window.open(`/signoff-generator/${submissionId}`, '_blank');
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="p-6">
      <Breadcrumb
        className="mb-6!"
        items={[
          {
            title: (
              <Link href="/">
                <HomeOutlined /> Home
              </Link>
            ),
          },
          {
            title: (
              <Link href="/supervisor/trainees">
                <TeamOutlined /> Trainees
              </Link>
            ),
          },
          {
            title: (
              <>
                <FileTextOutlined /> Monthly Sign-offs
              </>
            ),
          },
        ]}
      />

      <div className="flex justify-between items-center mb-6">
        <Space direction="vertical" size={0}>
          <Title level={2} className="mb-6!">Monthly Sign-offs</Title>
          {trainee && (
            <span className="text-gray-500">
              {trainee.name} ({trainee.email})
            </span>
          )}
        </Space>
      </div>

      {Object.keys(submissions).length === 0 ? (
        <Empty description="No submissions found" />
      ) : (
        <div className="space-y-6">
          {Object.entries(submissions)
            .sort((a, b) => dayjs(b[0], 'MMM YYYY').valueOf() - dayjs(a[0], 'MMM YYYY').valueOf())
            .map(([month, monthSubmissions]) => (
              <div key={month} className="space-y-4">
                <Title level={3}>{month}</Title>
                <Row gutter={[16, 16]}>
                  {monthSubmissions.map((submission) => (
                    <Col xs={24} md={12} lg={8} xl={6} key={submission.id}>
                      <Card 
                        hoverable
                        className="h-full"
                      >
                        <div className="flex flex-col h-full">
                          <div className="mb-4">
                            <div className="mb-2">
                              <Text strong className="text-lg block mb-2">
                                {dayjs(submission.startDate).format('MMMM YYYY')}
                              </Text>
                              {getStatusTag(submission.status)}
                            </div>
                            <div className="text-gray-500">
                              <div className="mt-2">
                                {submission.entries.length} {submission.entries.length === 1 ? 'Entry' : 'Entries'}
                              </div>
                              <div>
                                Sent {dayjs(submission.createdAt).format('DD/MM/YYYY')}
                              </div>
                            </div>
                          </div>
                          <div className="mt-auto space-y-2">
                            {submission.status === 'pending' && (
                              <Space className="w-full flex justify-between">
                                <Button
                                  type="primary"
                                  icon={<CheckOutlined />}
                                  onClick={() => handleApprove(submission.id)}
                                  loading={actionLoading}
                                >
                                  Approve
                                </Button>
                                <Button
                                  danger
                                  icon={<CloseOutlined />}
                                  onClick={() => handleReject(submission.id)}
                                  loading={actionLoading}
                                >
                                  Reject
                                </Button>
                              </Space>
                            )}
                            {submission.status === 'approved' && (
                              <Button
                                danger
                                icon={<RollbackOutlined />}
                                onClick={() => handleUnapprove(submission.id)}
                                loading={actionLoading}
                                block
                              >
                                Unapprove
                              </Button>
                            )}
                            <Button
                              type="default"
                              icon={<EyeOutlined />}
                              onClick={() => handleViewSubmission(submission.id)}
                              block
                            >
                              View Details
                            </Button>
                          </div>
                        </div>
                      </Card>
                    </Col>
                  ))}
                </Row>
              </div>
            ))}
        </div>
      )}
    </div>
  );
}