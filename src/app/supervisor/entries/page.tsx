'use client';

import { useState } from 'react';
import { Table, Button, Tag, Card, Modal, App } from 'antd';
import type { Entry } from '@/types';
import moment from 'moment';

type EntryWithDocuments = Entry & { documents?: { id: string; name: string; s3Key: string }[] };

export default function EntriesPage() {
  const { message } = App.useApp();
  const [entries, setEntries] = useState<EntryWithDocuments[]>([]);
  const [selectedEntry, setSelectedEntry] = useState<EntryWithDocuments | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);

  const columns = [
    {
      title: 'Trainee',
      dataIndex: ['placement', 'user', 'name'],
      key: 'trainee',
    },
    {
      title: 'Client',
      dataIndex: ['placement', 'client', 'name'],
      key: 'client',
    },
    {
      title: 'Skills',
      key: 'skills',
      render: (_: unknown, record: Entry) => (
        <div className="flex flex-wrap gap-2">
          {record.entrySubSkills.map((skill) => (
            <Tag key={skill.subSkill.id} color="blue">
              {skill.subSkill.name}
            </Tag>
          ))}
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const colors = {
          draft: 'default',
          submitted: 'gold',
          signedoff: 'green',
          rejected: 'red',
        };
        return <Tag color={colors[status as keyof typeof colors]}>{status}</Tag>;
      },
    },
    {
      title: 'Submitted',
      dataIndex: 'submittedAt',
      key: 'submittedAt',
      render: (date: Date | null) => date ? moment(date).format('DD/MM/YYYY') : '-',
    },
    {
      title: 'Position',
      dataIndex: ['placement', 'name'],
      key: 'position',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: unknown, record: EntryWithDocuments) => (
        <Button type="link" onClick={() => handleViewEntry(record)}>
          View Details
        </Button>
      ),
    },
  ];

  const handleViewEntry = (entry: EntryWithDocuments) => {
    setSelectedEntry(entry);
    setIsModalVisible(true);
  };

  const handleApprove = async () => {
    try {
      // TODO: Implement approve API call
      setEntries(
        entries.map((e) =>
          e.id === selectedEntry?.id
            ? { ...e, status: 'signedoff', reviewedAt: new Date() }
            : e
        )
      );
      message.success('Entry approved successfully');
      setIsModalVisible(false);
    } catch (error) {
      console.error('Error approving entry:', error);
      message.error('Failed to approve entry');
    }
  };

  const handleReject = async () => {
    try {
      // TODO: Implement reject API call
      setEntries(
        entries.map((e) =>
          e.id === selectedEntry?.id
            ? { ...e, status: 'rejected', reviewedAt: new Date() }
            : e
        )
      );
      message.success('Entry rejected successfully');
      setIsModalVisible(false);
    } catch (error) {
      console.error('Error rejecting entry:', error);
      message.error('Failed to reject entry');
    }
  };

  return (
    <>
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Entry Reviews</h1>
      </div>

      <Table columns={columns} dataSource={entries} rowKey="id" scroll={{ x: 'max-content' }} />

      <Modal
        title="Entry Details"
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={[
          <Button key="reject" danger onClick={handleReject}>
            Reject
          </Button>,
          <Button
            key="approve"
            type="primary"
            onClick={handleApprove}
            style={{ backgroundColor: 'rgb(199, 100, 27)' }}
          >
            Approve
          </Button>,
        ]}
      >
        {selectedEntry && (
          <div className="space-y-4">
            <Card title="Trainee Information">
              <p>
                <strong>Name:</strong>{' '}
                {selectedEntry.placement.user.name}
              </p>
              <p>
                <strong>Client:</strong>{' '}
                {selectedEntry.placement.client.name}
              </p>
            </Card>

            <Card title="Entry Details">
              <p><strong>Title:</strong> {selectedEntry.title}</p>
              <p><strong>Experience:</strong> {selectedEntry.experience}</p>
              <p><strong>Learnt:</strong> {selectedEntry.learnt}</p>
              {selectedEntry.moreExperience && (
                <p><strong>More Experience Needed:</strong> {selectedEntry.moreExperience}</p>
              )}
              {selectedEntry.needMoreExperience && (
                <p><strong>Areas for Improvement:</strong> {selectedEntry.needMoreExperience}</p>
              )}
            </Card>

            <Card title="Documents">
              <div className="space-y-2">
                {(selectedEntry?.documents ?? []).map((doc) => (
                  <a
                    key={doc.id}
                    href={doc.s3Key}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:text-blue-700"
                  >
                    {doc.name}
                  </a>
                ))}
              </div>
            </Card>

            <Card title="Selected Skills">
              <div className="flex flex-wrap gap-2">
                {selectedEntry.entrySubSkills.map((skill) => (
                  <Tag key={skill.subSkill.id} color="blue">
                    {skill.subSkill.name}
                  </Tag>
                ))}
              </div>
            </Card>

            <Card title="Submission Details">
              <p>
                <strong>Submitted:</strong>{' '}
                {selectedEntry.submittedAt ? moment(selectedEntry.submittedAt).format('DD/MM/YYYY HH:mm') : '-'}
              </p>
              {selectedEntry.reviewedAt && (
                <p>
                  <strong>Reviewed:</strong>{' '}
                  {moment(selectedEntry.reviewedAt).format('DD/MM/YYYY HH:mm')}
                </p>
              )}
            </Card>
          </div>
        )}
      </Modal>
    </>
  );
} 