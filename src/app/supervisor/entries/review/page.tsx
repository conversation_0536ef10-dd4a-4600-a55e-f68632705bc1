'use client';

import { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Tag,
  message as messageHook,
  Card,
  Modal,
  Form,
  Input,
  Popconfirm,
  Space
} from 'antd';
import { CheckCircleOutlined, IssuesCloseOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { UserRole, Entry } from '@/types';
import dayjs from 'dayjs';
import { viewDocument } from '@/lib/utils';

const { TextArea } = Input;

function SupervisorSignOffTable() {
  const [entries, setEntries] = useState<Entry[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedEntry, setSelectedEntry] = useState<Entry | null>(null);
  const [form] = Form.useForm();
  const router = useRouter();
  const { data: session } = useSession();
  const [messageApi, contextHolder] = messageHook.useMessage();

  useEffect(() => {
    if (session && session.user?.role !== UserRole.SUPERVISOR) {
      router.push('/');
      return;
    }
    if (session?.user?.role === UserRole.SUPERVISOR) {
      fetchEntries();
    }
  }, [session, router]);

  const fetchEntries = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/entries');
      if (!response.ok) throw new Error('Failed to fetch entries for sign-off');
      const data = await response.json();
      setEntries(data);
    } catch (error) {
      console.error('Error fetching entries:', error);
      messageApi.error('Failed to load entries for sign-off');
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetailsClick = (entry: Entry) => {
    setSelectedEntry(entry);
    form.setFieldsValue({
      feedback: ''
    });
    setIsModalVisible(true);
  };

  const handleSignOff = async () => {
    if (!selectedEntry) return;
    updateEntryStatus('signedoff', form.getFieldValue('feedback'));
  };
  
  const handleRequestRevision = async () => {
    if (!selectedEntry) return;
    try {
      await form.validateFields(['feedback']);
      updateEntryStatus('rejected', form.getFieldValue('feedback'));
    } catch (error) {
      console.error('Error validating feedback:', error);
      messageApi.warning('Feedback is required to request revision.');
    }
  };

  const updateEntryStatus = async (newStatus: string, feedbackText?: string) => {
    if (!selectedEntry) return;
    try {
      const response = await fetch(`/api/entries/${selectedEntry.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus, feedback: feedbackText }),
      });

      if (response.ok) {
        const updatedEntry = await response.json();
        setEntries(
          entries.filter((e) => e.id !== updatedEntry.id)
        );
        messageApi.success(`Entry status updated to ${newStatus}`);
        setIsModalVisible(false);
        setSelectedEntry(null);
        form.resetFields();
      } else {
        const errorData = await response.json();
        messageApi.error(errorData.error || 'Failed to update status');
      }
    } catch (error) {
      messageApi.error('Error updating entry status');
      console.error('Status update error:', error);
    }
  }

  const handleModalCancel = () => {
    setIsModalVisible(false);
    setSelectedEntry(null);
    form.resetFields();
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'signedoff':
        return 'success';
      case 'rejected':
        return 'error';
      case 'submitted':
        return 'processing';
      case 'draft':
        return 'default';
      default:
        return 'default';
    }
  };

  const columns = [
    {
      title: 'Trainee',
      dataIndex: ['placement', 'user', 'name'],
      key: 'trainee',
      render: (name: string | null) => name || '',
    },
    {
      title: 'Placement',
      dataIndex: ['placement', 'name'],
      key: 'placement',
      render: (_: unknown, record: Entry) => 
        `${record.placement.name} at ${record.placement.client.name}`,
    },
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status.charAt(0).toUpperCase() + status.slice(1).toLowerCase()}
        </Tag>
      ),
    },
    {
      title: 'Submitted',
      dataIndex: 'submittedAt',
      key: 'submittedAt',
      render: (date?: string) => date ? dayjs(date).format('DD/MM/YYYY HH:mm') : '-',
    },
    {
      title: 'Reviewed',
      dataIndex: 'reviewedAt',
      key: 'reviewedAt',
      render: (date?: string) => date ? dayjs(date).format('DD/MM/YYYY HH:mm') : '-',
    },
    {
      title: 'Reviewer',
      dataIndex: ['reviewer', 'name'],
      key: 'reviewer',
      render: (name?: string | null) => name || '',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: unknown, record: Entry) => (
        <Button icon={<CheckCircleOutlined />} onClick={() => handleViewDetailsClick(record)}>
          Sign Off / Revise
        </Button>
      ),
    },
  ];

  if (session === undefined) return null;
  if (session?.user?.role !== UserRole.SUPERVISOR) return null;

  return (
    <div className="p-6">
      {contextHolder}
      <Card title="Sign Off Approved Entries">
        <Table
          columns={columns}
          dataSource={entries}
          loading={loading}
          rowKey="id"
          scroll={{ x: 'max-content' }}
        />
      </Card>

      <Modal
        title={`Review Entry from ${selectedEntry?.placement?.user?.name || 'Trainee'}`}
        open={isModalVisible}
        onCancel={handleModalCancel}
        footer={[
          <Button key="revise" icon={<IssuesCloseOutlined />} onClick={handleRequestRevision}>
            Request Revision
          </Button>,
          <Popconfirm
            key="signoff"
            title="Sign off this entry?"
            description="This confirms the entry meets requirements."
            onConfirm={handleSignOff}
            okText="Yes, Sign Off"
            cancelText="Cancel"
          >
             <Button type="primary" icon={<CheckCircleOutlined />}>
              Sign Off
            </Button>
          </Popconfirm>,
        ]}
      >
        <Form form={form} layout="vertical">
          <Form.Item label="Entry Details">
            <Space direction="vertical">
              <div><strong>Title:</strong> {selectedEntry?.title}</div>
              <div><strong>Experience:</strong> {selectedEntry?.experience}</div>
              <div><strong>What I Learned:</strong> {selectedEntry?.learnt}</div>
            </Space>
          </Form.Item>
          <Form.Item label="Documents">
            <Space direction="vertical">
              {selectedEntry?.documentKey &&
                <a onClick={() => selectedEntry.documentKey && viewDocument(selectedEntry.documentKey)}>{selectedEntry.documentName}</a>
              }
            </Space>
          </Form.Item>
          <Form.Item label="Previous Feedback">
            <p>{selectedEntry?.feedback || "No feedback provided."}</p>
          </Form.Item>
          <Form.Item
            name="feedback"
            label="Supervisor Feedback / Revision Notes (Optional for Sign-Off, Required for Revision)"
          >
            <TextArea rows={4} placeholder="Add comments for sign-off or specify required revisions" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}

export default function SupervisorReviewPage() {
  return <SupervisorSignOffTable />;
} 