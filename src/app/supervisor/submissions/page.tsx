'use client';

import { useState, useEffect } from 'react';
import { Card, Typography, Button, Tag, Row, Col, Empty, Spin, Space, Modal, App } from 'antd';
import { EyeOutlined, CheckOutlined, CloseOutlined, RollbackOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { Submission } from '@/types';

const { Title, Text } = Typography;

interface GroupedSubmissions {
  [key: string]: Submission[];
}

export default function SupervisorSubmissionsPage() {
  const [submissions, setSubmissions] = useState<GroupedSubmissions>({});
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [rejectModalVisible, setRejectModalVisible] = useState(false);
  const [rejectFeedback, setRejectFeedback] = useState('');
  const [currentSubmissionId, setCurrentSubmissionId] = useState<string | null>(null);
  const [rejectSubmitting, setRejectSubmitting] = useState(false);
  const { message } = App.useApp();

  useEffect(() => {
    fetchSubmissions();
  }, []);

  const fetchSubmissions = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/supervisor/submissions');
      const data = await response.json();
      
      // Group submissions by month
      const grouped = data.reduce((acc: GroupedSubmissions, submission: Submission) => {
        const monthYear = dayjs(submission.startDate).format('MMM YYYY');
        if (!acc[monthYear]) {
          acc[monthYear] = [];
        }
        acc[monthYear].push(submission);
        return acc;
      }, {});

      // Sort submissions within each month by date
      Object.keys(grouped).forEach(month => {
        grouped[month].sort((a: Submission, b: Submission) => 
          dayjs(b.createdAt).valueOf() - dayjs(a.createdAt).valueOf()
        );
      });

      setSubmissions(grouped);
    } catch (error) {
      console.error('Error fetching submissions:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (submissionId: string) => {
    try {
      setActionLoading(true);
      const response = await fetch(`/api/submissions/${submissionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'approved',
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to approve submission');
      }

      message.success('Submission approved successfully');
      fetchSubmissions();
    } catch (error) {
      message.error(error instanceof Error ? error.message : 'Failed to approve submission');
    } finally {
      setActionLoading(false);
    }
  };

  const handleReject = async (submissionId: string, feedback: string) => {
    try {
      setActionLoading(true);
      const response = await fetch(`/api/submissions/${submissionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'rejected',
          feedback: feedback,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to reject submission');
      }

      message.success('Submission rejected successfully');
      fetchSubmissions();
    } catch (error) {
      message.error(error instanceof Error ? error.message : 'Failed to reject submission');
    } finally {
      setActionLoading(false);
    }
  };

  const handleUnapprove = async (submissionId: string) => {
    try {
      setActionLoading(true);
      const response = await fetch(`/api/submissions/${submissionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'pending',
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to unapprove submission');
      }

      message.success('Submission unapproved successfully');
      fetchSubmissions();
    } catch (error) {
      message.error(error instanceof Error ? error.message : 'Failed to unapprove submission');
    } finally {
      setActionLoading(false);
    }
  };

  const showRejectConfirm = (submissionId: string) => {
    setCurrentSubmissionId(submissionId);
    setRejectFeedback('');
    setRejectModalVisible(true);
  };

  const handleRejectSubmit = async () => {
    if (!currentSubmissionId) return;
    
    setRejectSubmitting(true);
    try {
      await handleReject(currentSubmissionId, rejectFeedback);
      setRejectModalVisible(false);
      setCurrentSubmissionId(null);
      setRejectFeedback('');
    } catch {
      // Error is already handled in handleReject function
    } finally {
      setRejectSubmitting(false);
    }
  };

  const getStatusTag = (status: Submission['status']) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return <Tag color="processing">Pending</Tag>;
      case 'approved':
        return <Tag color="success">Approved</Tag>;
      case 'rejected':
        return <Tag color="error">Rejected</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  const handleViewSubmission = (submissionId: string) => {
    // open in new tab
    window.open(`/signoff-generator/${submissionId}`, '_blank');
    // router.push(`/signoff-generator/${submissionId}`);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <Title level={2}>Supervisor Sign-off</Title>

      {Object.keys(submissions).length === 0 ? (
        <Empty description="No submissions found" />
      ) : (
        Object.entries(submissions)
          .sort((a, b) => dayjs(b[0], 'MMM YYYY').valueOf() - dayjs(a[0], 'MMM YYYY').valueOf())
          .map(([month, monthSubmissions]) => (
            <div key={month} className="space-y-4">
              <Title level={3}>{month}</Title>
              <Row gutter={[16, 16]}>
                {monthSubmissions.map((submission) => (
                  <Col xs={24} md={12} lg={8} xl={6} key={submission.id}>
                    <Card 
                      hoverable
                      className="h-full"
                    >
                      <div className="flex flex-col h-full">
                        <div className="mb-4">
                          <div className="mb-2">
                            <Text strong className="text-lg block mb-2">{`${submission.trainee.name}`}</Text>
                            {getStatusTag(submission.status)}
                          </div>
                          <div className="text-gray-500">
                            <div>{submission.trainee.email}</div>
                            <div className="mt-2">
                              {submission.entries.length} {submission.entries.length === 1 ? 'Entry' : 'Entries'}
                            </div>
                            <div>
                              Sent {dayjs(submission.submittedAt).format('DD/MM/YYYY')}
                            </div>
                          </div>
                        </div>
                        <div className="mt-auto space-y-2">
                          {submission.status === 'pending' && (
                            <Button
                              type="primary"
                              className="w-full"
                              icon={<CheckOutlined />}
                              onClick={() => handleApprove(submission.id)}
                              loading={actionLoading}
                            >
                              Approve
                            </Button>
                          )}
                          {submission.status === 'approved' && (
                            <Button
                              danger
                              icon={<RollbackOutlined />}
                              onClick={() => handleUnapprove(submission.id)}
                              loading={actionLoading}
                              block
                            >
                              Unapprove
                            </Button>
                          )}
                          <Button
                            type="default"
                            icon={<EyeOutlined />}
                            onClick={() => handleViewSubmission(submission.id)}
                            block
                          >
                            View Details
                          </Button>
                        </div>
                      </div>
                    </Card>
                  </Col>
                ))}
              </Row>
            </div>
          ))
      )}
      
      {/* Reject Modal */}
      <Modal
        title="Reject Submission"
        open={rejectModalVisible}
        onCancel={() => setRejectModalVisible(false)}
        footer={null}
        centered
        width={500}
      >
        <div className="space-y-4">
          <p>Are you sure you want to reject this submission?</p>
          {/* <Input.TextArea
            placeholder="Please provide feedback for the rejection..."
            onChange={(e) => setRejectFeedback(e.target.value)}
            value={rejectFeedback}
            rows={4}
            autoFocus
          /> */}
          <div className="flex justify-end space-x-2 mt-4">
            <Button onClick={() => setRejectModalVisible(false)}>Cancel</Button>
            <Button 
              type="primary" 
              danger 
              onClick={handleRejectSubmit}
              loading={rejectSubmitting}
            >
              Reject
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
} 
