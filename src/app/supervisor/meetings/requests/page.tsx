'use client';

import { useState, useEffect, useMemo } from 'react';
import { Card, Typography, App, Modal, Input, Form } from 'antd';
import MeetingsList from '@/components/mentoring/MeetingsList';
import { Meeting, MeetingFormat, MeetingStatus } from '@/types';

const { Title } = Typography;
const { TextArea } = Input;

export default function MentorMeetingRequestsPage() {
  const { message } = App.useApp();
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [declineModalVisible, setDeclineModalVisible] = useState(false);
  const [acceptModalVisible, setAcceptModalVisible] = useState(false);
  const [declineNotes, setDeclineNotes] = useState('');
  const [selectedMeetingId, setSelectedMeetingId] = useState<string | null>(null);
  const selectedMeeting = useMemo(() => {
    if (!selectedMeetingId) return null;
    return meetings.find(meeting => meeting.id === selectedMeetingId);
  }, [meetings, selectedMeetingId]);

  const [form] = Form.useForm();

  useEffect(() => {
    fetchMeetings();
  }, []);

  const fetchMeetings = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/meetings');
      if (!response.ok) throw new Error('Failed to fetch meetings');

      const data = await response.json();
      const requestMeetings = data.filter(
        (meeting: Meeting) => meeting.status === MeetingStatus.PENDING
      );
      setMeetings(requestMeetings);
    } catch (error) {
      console.error('Error fetching meetings:', error);
      message.error('Failed to fetch meetings');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (meetingId: string, status: MeetingStatus) => {
    if (status === MeetingStatus.DECLINED) {
      setSelectedMeetingId(meetingId);
      setDeclineModalVisible(true);
      return;
    }

    if (status === MeetingStatus.ACCEPTED) {
      setSelectedMeetingId(meetingId);
      const defaultUrl = `${window.location.origin}/meeting/${meetingId}`;
      form.setFieldsValue({ meetingUrl: defaultUrl });
      setAcceptModalVisible(true);
      return;
    }

    try {
      const response = await fetch(`/api/meetings/${meetingId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update meeting status');
      }

      message.success(`Meeting ${status.toLowerCase()} successfully`);

      setMeetings(prevMeetings =>
        prevMeetings.map(meeting =>
          meeting.id === meetingId ? { ...meeting, status } : meeting
        )
      );

    } catch (error) {
      console.error('Error updating meeting status:', error);
      message.error(error instanceof Error ? error.message : 'Failed to update meeting status');
    }
  };

  const handleAcceptMeeting = async () => {
    if (!selectedMeetingId) return;

    try {
      setUpdating(true);
      const values = await form.validateFields();
      const response = await fetch(`/api/meetings/${selectedMeetingId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          status: MeetingStatus.ACCEPTED,
          meetingUrl: values.meetingUrl,
          meetingLocation: values.meetingLocation
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to accept meeting');
      }

      message.success('Meeting accepted successfully');
      setMeetings(prevMeetings =>
        prevMeetings.filter(meeting => meeting.id !== selectedMeetingId)
      );

      // Reset accept modal state
      setAcceptModalVisible(false);
      form.resetFields();
      setSelectedMeetingId(null);
    } catch (error) {
      console.error('Error accepting meeting:', error);
      message.error(error instanceof Error ? error.message : 'Failed to accept meeting');
    } finally {
      setUpdating(false);
    }
  };

  const handleDeclineMeeting = async () => {
    if (!selectedMeetingId) return;

    try {
      const response = await fetch(`/api/meetings/${selectedMeetingId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          status: MeetingStatus.DECLINED,
          notes: declineNotes
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to decline meeting');
      }

      message.success('Meeting declined successfully');
      setMeetings(prevMeetings =>
        prevMeetings.filter(meeting => meeting.id !== selectedMeetingId)
      );

      // Reset decline modal state
      setDeclineModalVisible(false);
      setDeclineNotes('');
      setSelectedMeetingId(null);
    } catch (error) {
      console.error('Error declining meeting:', error);
      message.error(error instanceof Error ? error.message : 'Failed to decline meeting');
    }
  };

  const handleDeleteMeeting = async (meetingId: string) => {
    try {
      const response = await fetch(`/api/meetings/${meetingId}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Failed to delete meeting');

      message.success('Meeting deleted successfully');
      setMeetings(prevMeetings =>
        prevMeetings.filter(meeting => meeting.id !== meetingId)
      );
    } catch (error) {
      console.error('Error deleting meeting:', error);
      message.error('Failed to delete meeting');
    }
  };

  return (
    <div>
      <Title level={2}>Meeting Requests</Title>
      <Card>
        <MeetingsList
          meetings={meetings}
          loading={loading}
          onStatusChange={handleStatusChange}
          onRefresh={fetchMeetings}
          isMentor={true}
          mode="requests"
        />
      </Card>

      <Modal
        title="Decline Meeting"
        open={declineModalVisible}
        onOk={handleDeclineMeeting}
        onCancel={() => {
          setDeclineModalVisible(false);
          setDeclineNotes('');
          setSelectedMeetingId(null);
        }}
        okText="Decline"
        okButtonProps={{ danger: true }}
        confirmLoading={loading}
      >
        <p>Please provide a reason for declining this meeting:</p>
        <TextArea
          rows={4}
          value={declineNotes}
          onChange={(e) => setDeclineNotes(e.target.value)}
          placeholder="Enter reason for declining..."
        />
      </Modal>

      <Modal
        title="Accept Meeting"
        open={acceptModalVisible}
        onOk={handleAcceptMeeting}
        onCancel={() => {
          setAcceptModalVisible(false);
          form.resetFields();
          setSelectedMeetingId(null);
        }}
        okText="Accept"
        okButtonProps={{ type: 'primary' }}
        confirmLoading={updating}
      >
        <Form form={form} layout="vertical" style={{ marginTop: 30 }}>
          {selectedMeeting?.availability?.meetingMethod !== MeetingFormat.VIRTUAL && (
            <Form.Item
              name="meetingLocation"
              label="Meeting Location"
              rules={[
                { required: true, message: 'Please enter a meeting location' }
              ]}
            >
              <Input
                placeholder="Enter meeting location"
              />
            </Form.Item>
          )}
          <Form.Item
            name="meetingUrl"
            label="Meeting URL"
            rules={[
              { type: 'url', message: 'Please enter a valid URL' }
            ]}
          >
            <Input
              placeholder="Enter meeting URL"
              type="url"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
} 