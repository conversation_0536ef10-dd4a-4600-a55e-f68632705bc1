'use client';

import { useState, useEffect } from 'react';
import { Card, Typography, App } from 'antd';
import MeetingsList from '@/components/mentoring/MeetingsList';
import { Meeting, MeetingStatus } from '@/types';

const { Title } = Typography;

export default function MentorCompletedMeetingsPage() {
  const { message } = App.useApp();
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchMeetings();
  }, []);

  const fetchMeetings = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/meetings');
      if (!response.ok) throw new Error('Failed to fetch meetings');
      
      const data = await response.json();
      const completedMeetings = data.filter(
        (meeting: Meeting) => meeting.status === MeetingStatus.COMPLETED || meeting.status === MeetingStatus.ACCEPTED
      );
      console.log('completedMeetings', completedMeetings);
      setMeetings(completedMeetings);
    } catch (error) {
      console.error('Error fetching meetings:', error);
      message.error('Failed to fetch meetings');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (meetingId: string, status: MeetingStatus) => {
    try {
      const response = await fetch(`/api/meetings/${meetingId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update meeting status');
      }
      
      message.success(`Meeting status updated successfully`);
      
      setMeetings(prevMeetings => 
        prevMeetings.map(meeting => 
          meeting.id === meetingId ? { ...meeting, status } : meeting
        )
      );

      if (status !== MeetingStatus.COMPLETED && status !== MeetingStatus.ACCEPTED) {
        setMeetings(prevMeetings => 
          prevMeetings.filter(meeting => meeting.id !== meetingId)
        );
      }
    } catch (error) {
      console.error('Error updating meeting status:', error);
      message.error(error instanceof Error ? error.message : 'Failed to update meeting status');
    }
  };

  const handleDeleteMeeting = async (meetingId: string) => {
    try {
      const response = await fetch(`/api/meetings/${meetingId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete meeting');
      }
      
      message.success('Meeting deleted successfully');
      setMeetings(prevMeetings => 
        prevMeetings.filter(meeting => meeting.id !== meetingId)
      );
    } catch (error) {
      console.error('Error deleting meeting:', error);
      message.error(error instanceof Error ? error.message : 'Failed to delete meeting');
    }
  };

  return (
    <div>
      <Title level={2}>Meetings</Title>
      <Card>
        <MeetingsList
          meetings={meetings}
          loading={loading}
          onStatusChange={handleStatusChange}
          onRefresh={fetchMeetings}
          isMentor={true}
          mode="completed"
        />
      </Card>
    </div>
  );
} 