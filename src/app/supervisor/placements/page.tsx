'use client';

import { useState, useEffect } from 'react';
import { Table, Card, Tag, App } from 'antd';
import { useSession } from 'next-auth/react';
import { UserRole, Placement } from '@/types';
import { useRouter } from 'next/navigation';

// Define columns for the Supervisor Placements table
const columns = [
  {
    title: 'Position',
    dataIndex: 'position',
    key: 'position',
  },
  {
    title: 'Client',
    dataIndex: ['client', 'name'],
    key: 'client',
  },
  {
    title: 'Trainee',
    dataIndex: ['user', 'name'], // Display the trainee name
    key: 'trainee',
  },
  {
    title: '<PERSON>tor',
    dataIndex: ['mentor', 'name'],
    key: 'mentor',
    render: (name?: string) => name || '',
  },
  {
    title: 'Start Date',
    dataIndex: 'startDate',
    key: 'startDate',
    render: (date: string) => new Date(date).toLocaleDateString(),
  },
  {
    title: 'End Date',
    dataIndex: 'endDate',
    key: 'endDate',
    render: (date?: string) => (date ? new Date(date).toLocaleDateString() : 'Ongoing'),
  },
  {
    title: 'Status',
    dataIndex: 'status',
    key: 'status',
    render: (status: string) => {
      let color = 'blue';
      if (status === 'COMPLETED') color = 'green';
      if (status === 'PENDING') color = 'orange';
      if (status === 'CANCELLED') color = 'red';
      return <Tag color={color}>{status}</Tag>;
    },
  },
];

export default function SupervisorPlacementsPage() {
  const [placements, setPlacements] = useState<Placement[]>([]);
  const [loading, setLoading] = useState(true);
  const { data: session } = useSession();
  const router = useRouter();
  const { message } = App.useApp();

  useEffect(() => {
    // Redirect if not a Supervisor
    if (session && session.user?.role !== UserRole.SUPERVISOR) {
      router.push('/');
      return;
    }

    // Fetch placements if session is available and user is Supervisor
    if (session?.user?.role === UserRole.SUPERVISOR) {
      const fetchPlacements = async () => {
        try {
          setLoading(true);
          const response = await fetch('/api/placements'); // API route now filters based on session
          if (!response.ok) {
            throw new Error('Failed to fetch placements');
          }
          const data = await response.json();
          setPlacements(data.placements.edges);
        } catch (error) {
          message.error('Failed to load placements');
        } finally {
          setLoading(false);
        }
      };

      fetchPlacements();
    }
  }, [session, router]);

  return (
    <div className="p-6">
      <Card title="My Placements (Supervisor)">
        <Table
          columns={columns}
          dataSource={placements}
          loading={loading}
          rowKey="id"
          scroll={{ x: 'max-content' }}
        />
      </Card>
    </div>
  );
} 