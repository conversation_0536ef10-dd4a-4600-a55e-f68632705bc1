'use client';

import { useState, useEffect } from 'react';
import { Table, Button, Tag, Space, App } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
// import MainLayout from '@/components/layouts/MainLayout'; // Removed
import type { Placement, Entry } from '@/types';

export default function SupervisorDashboard() {
  const [placements, setPlacements] = useState<Placement[]>([]);
  const [loading, setLoading] = useState(false);
  const { message } = App.useApp();

  useEffect(() => {
    fetchPlacements();
  }, []);

  const fetchPlacements = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/placements/supervisor');
      if (response.ok) {
        const data = await response.json();
        setPlacements(data);
      } else {
        message.error('Failed to fetch placements');
      }
    } catch (error) {
      console.error('Error fetching placements:', error);
      message.error('Failed to fetch placements');
    } finally {
      setLoading(false);
    }
  };

  const handleApproveEntry = async (entryId: string) => {
    try {
      const response = await fetch(`/api/entries/${entryId}/review`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'approved' }),
      });

      if (response.ok) {
        message.success('Entry approved successfully');
        fetchPlacements(); // Refresh the data
      } else {
        const errorData = await response.json();
        message.error(errorData.error || 'Failed to approve entry');
      }
    } catch (error) {
      console.error('Error approving entry:', error);
      message.error('Failed to approve entry');
    }
  };

  const handleRejectEntry = async (entryId: string) => {
    try {
      const response = await fetch(`/api/entries/${entryId}/review`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'rejected' }),
      });

      if (response.ok) {
        message.success('Entry rejected successfully');
        fetchPlacements(); // Refresh the data
      } else {
        const errorData = await response.json();
        message.error(errorData.error || 'Failed to reject entry');
      }
    } catch (error) {
      console.error('Error rejecting entry:', error);
      message.error('Failed to reject entry');
    }
  };

  const columns = [
    {
      title: 'Trainee',
      dataIndex: ['user', 'name'],
      key: 'trainee',
    },
    {
      title: 'Position',
      dataIndex: 'position',
      key: 'position',
    },
    {
      title: 'Client',
      dataIndex: ['client', 'name'],
      key: 'client',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        let color = 'blue';
        if (status === 'COMPLETED') color = 'green';
        if (status === 'PENDING') color = 'orange';
        if (status === 'CANCELLED') color = 'red';
        return <Tag color={color}>{status}</Tag>;
      },
    },
    {
      title: 'Entries',
      key: 'entries',
      render: (_: any, record: Placement) => (
        <Table
          size="small"
          dataSource={record.entries}
          rowKey="id"
          pagination={false}
          scroll={{ x: 'max-content' }}
          columns={[
            {
              title: 'File',
              dataIndex: 'fileUrl',
              key: 'file',
              render: (url: string) => (
                <a href={url} target="_blank" rel="noopener noreferrer">
                  View File
                </a>
              ),
            },
            {
              title: 'Status',
              dataIndex: 'status',
              key: 'status',
              render: (status: string) => {
                let color = 'default';
                if (status === 'approved') color = 'success';
                if (status === 'rejected') color = 'error';
                return <Tag color={color}>{status}</Tag>;
              },
            },
            {
              title: 'Submitted At',
              dataIndex: 'submittedAt',
              key: 'submittedAt',
              render: (date: string) => new Date(date).toLocaleDateString(),
            },
            {
              title: 'Actions',
              key: 'actions',
              render: (_: any, entry: Entry) => (
                <Space>
                  {/* Supervisor dashboard likely shouldn't approve/reject directly */}
                  {/* This logic might belong on the Review Entries page */}
                  {/* {entry.status === 'pending' && ( ... )} */}
                </Space>
              ),
            },
          ]}
        />
      ),
    },
  ];

  return (
    // Removed <MainLayout>
    <>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Supervisor Dashboard</h1>
      </div>

      <Table
        columns={columns}
        dataSource={placements}
        rowKey="id"
        loading={loading}
        scroll={{ x: 'max-content' }}
        expandable={{
          expandedRowRender: (record) => (
            <div className="p-4">
              <h3 className="font-semibold mb-2">Placement Details</h3>
              <p><strong>Start Date:</strong> {new Date(record.startDate).toLocaleDateString()}</p>
              {record.endDate && (
                <p><strong>End Date:</strong> {new Date(record.endDate).toLocaleDateString()}</p>
              )}
              <p><strong>Mentor:</strong> {record.mentor?.name || 'Not assigned'}</p>
            </div>
          ),
        }}
      />
    </>
    // Removed </MainLayout>
  );
} 