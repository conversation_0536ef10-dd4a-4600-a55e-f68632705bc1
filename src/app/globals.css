@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* Rest of your existing styles... */
body {
  overflow-y: auto!important;
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* User Detail Tabs */
.user-detail-tabs .ant-tabs-nav {
  margin-bottom: 24px;
}

.user-detail-tabs .ant-tabs-tab {
  padding: 8px 16px;
  font-weight: 600;
  border-radius: 16px 16px 0 0 !important;
  margin-right: 4px;
}

.user-detail-tabs .ant-tabs-tab-active {
  background-color: #fff;
  border-bottom-color: #fff;
}

.user-detail-tabs .ant-tabs-nav::before {
  border-bottom: 2px solid #e8e8e8;
}

.user-detail-tabs .ant-tabs-ink-bar {
  height: 3px;
  background-color: #52c41a;
}

/* Custom position classes */
.pos-1 {
  top: 29px;
  left: 155px;
}

.pos-2 {
  top: 29px;
  left: 320px;
}

.pos-3 {
  top: 29px;
  left: 488px;
}

.pos-4 {
  top: 170px;
  left: 220px;
  /* left: 465px; */
}

.pos-5 {
  top: 316px;
  left: 245px;
}

.pos-6 {
  top: 316px;
  left: 440px;
}

.custom-error-message {
  width: 400px !important;
  padding: 16px !important;
  position: fixed !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  top: 24px !important;
}

.custom-error-message .ant-message-notice-content {
  background-color: #fff2f0 !important;
  border: 1px solid #ffccc7 !important;
}

/* Markdown Editor Styles */
.w-md-editor {
  background-color: #fff;
}

.w-md-editor-text-container .w-md-editor-text-input {
  font-size: 14px !important;
  line-height: 1.6 !important;
}

.w-md-editor-text {
  font-size: 14px !important;
}

.wmde-markdown {
  background-color: transparent !important;
}

/* Prose styles for Quill content */
.prose {
  color: inherit;
  max-width: none;
}

.prose p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
  line-height: 1.3;
}

.prose h1 { font-size: 2rem; }
.prose h2 { font-size: 1.75rem; }
.prose h3 { font-size: 1.5rem; }
.prose h4 { font-size: 1.25rem; }
.prose h5 { font-size: 1.125rem; }
.prose h6 { font-size: 1rem; }

.prose ul, .prose ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.5rem;
}

.prose strong {
  font-weight: 600;
}

.prose em {
  font-style: italic;
}

.prose a {
  color: #1890ff;
  text-decoration: underline;
}

.prose a:hover {
  color: #40a9ff;
}

.prose blockquote {
  border-left: 4px solid #d9d9d9;
  margin: 1rem 0;
  padding-left: 1rem;
  font-style: italic;
  color: #666;
}