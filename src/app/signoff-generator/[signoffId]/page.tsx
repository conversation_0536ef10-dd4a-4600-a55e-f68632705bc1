import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { redirect } from 'next/navigation';
import SignoffGenerator from '@/components/signoff/SignoffGenerator';

export default async function SignoffGeneratorPage({ params }: { params: Promise<{ signoffId: string }> }) {
  const { signoffId } = await params;
  const session = await getServerSession(authOptions);
  
  if (!session) {
    redirect('/auth/signin');
  }

  // Allow access for all authenticated users regardless of role
  if (!session.user?.role || !['TRAINEE', 'SUPERVISOR', 'MENTOR'].includes(session.user.role)) {
    redirect('/unauthorized');
  }

  return <SignoffGenerator signoffId={signoffId} />;
} 