'use client';

import { useState, useEffect } from 'react';
import { Table, Input, Typography, Card, Progress, Avatar, Tabs } from 'antd';
import dayjs from 'dayjs';
import { useRouter } from 'next/navigation';

const { Title, Text } = Typography;
const { Search } = Input;

interface Mentee {
  id: string;
  name: string;
  email: string;
  qualificationRoute: string;
  traineeLevel: number;
  lastActivity: string | null;
  competencyProgress: number;
  relationshipStartDate: string;
  relationshipEndDate?: string;
  isActive: boolean;
}

export default function MentorMenteesPage() {
  const router = useRouter();
  const [currentMentees, setCurrentMentees] = useState<Mentee[]>([]);
  const [pastMentees, setPastMentees] = useState<Mentee[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    fetchMentees();
  }, []);

  const fetchMentees = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/mentor/mentees?type=all');
      const data = await response.json();
      setCurrentMentees(data.current || []);
      setPastMentees(data.past || []);
    } catch (error) {
      console.error('Error fetching mentees:', error);
    } finally {
      setLoading(false);
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase();
  };

  const columns = [
    {
      title: 'NAME',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => (
        <div className="flex items-center gap-3">
          <Avatar>{getInitials(text)}</Avatar>
          <Text>{text}</Text>
        </div>
      ),
    },
    {
      title: 'EMAIL',
      dataIndex: 'email',
      key: 'email',
      render: (text: string) => <Text className="text-orange-600">{text}</Text>,
    },
    {
      title: 'QUALIFICATION ROUTE',
      dataIndex: 'qualificationRoute',
      key: 'qualificationRoute',
      render: (route: string) => route || 'N/A',
    },
    {
      title: 'LAST ACTIVE',
      dataIndex: 'lastActivity',
      key: 'lastActivity',
      render: (date: string | null) => date ? dayjs(date).format('DD/MM/YYYY') : 'Never',
    },
    {
      title: 'COMPETENCIES',
      dataIndex: 'competencyProgress',
      key: 'competencyProgress',
      render: (progress: number) => (
        <div className="flex items-center gap-2">
          <span className="min-w-[30px]">{progress}%</span>
          <Progress 
            percent={progress} 
            showInfo={false}
            strokeColor="#f97316"
            className="flex-1"
          />
        </div>
      ),
    }
  ];

  const pastMenteeColumns = [
    ...columns,
    {
      title: 'RELATIONSHIP START',
      dataIndex: 'relationshipStartDate',
      key: 'relationshipStartDate',
      render: (date: string) => dayjs(date).format('DD/MM/YYYY'),
    },
    {
      title: 'RELATIONSHIP END',
      dataIndex: 'relationshipEndDate',
      key: 'relationshipEndDate',
      render: (date: string | null) => date ? dayjs(date).format('DD/MM/YYYY') : 'N/A',
    },
  ];

  const filteredCurrentMentees = currentMentees.filter(mentee =>
    mentee.name.toLowerCase().includes(searchText.toLowerCase()) ||
    mentee.email.toLowerCase().includes(searchText.toLowerCase())
  );

  const filteredPastMentees = pastMentees.filter(mentee =>
    mentee.name.toLowerCase().includes(searchText.toLowerCase()) ||
    mentee.email.toLowerCase().includes(searchText.toLowerCase())
  );

  const tabItems = [
    {
      key: 'current',
      label: `Current Mentees (${currentMentees.length})`,
      children: (
        <Table
          columns={columns}
          dataSource={filteredCurrentMentees}
          onRow={(record) => {
            return {
              onClick: () => router.push(`/mentor/mentees/${record.id}`),
              style: { cursor: 'pointer' } 
            };
          }}
          rowKey="id"
          loading={loading}
          scroll={{ x: 'max-content' }}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            pageSizeOptions: ['10', '20', '50'],
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
          }}
        />
      ),
    },
    {
      key: 'past',
      label: `Past Mentees (${pastMentees.length})`,
      children: (
        <Table
          columns={pastMenteeColumns}
          dataSource={filteredPastMentees}
          onRow={(record) => {
            return {
              onClick: () => router.push(`/mentor/mentees/${record.id}`),
              style: { cursor: 'pointer' } 
            };
          }}
          rowKey="id"
          loading={loading}
          scroll={{ x: 'max-content' }}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            pageSizeOptions: ['10', '20', '50'],
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
          }}
        />
      ),
    },
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <Title level={2} className="!mb-0">Mentees</Title>
        <Text>{currentMentees.length + pastMentees.length} members total</Text>
      </div>

      <Card>
        <div className="mb-4">
          <Search
            placeholder="Search within mentees..."
            allowClear
            onChange={e => setSearchText(e.target.value)}
            style={{ maxWidth: 400 }}
          />
        </div>

        <Tabs items={tabItems} defaultActiveKey="current" />
      </Card>
    </div>
  );
}
