'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import { Card, Tabs, Typography, Spin, Space, Breadcrumb, App } from 'antd';
import { User } from '@/types';
import AdminEntriesTable from '@/components/entries/AdminEntriesTable';
import UserInfoForm from '@/components/users/UserInfoForm';
import Link from 'next/link';
import AdminPortfolioTab from '@/components/users/AdminPortfolioTab';
import AdminMentorMeetingsTab from '@/components/users/AdminMentorMeetingsTab';
import AdminAppraisalsTab from '@/components/users/AdminAppraisalsTab';

const { Title, Text } = Typography;

export default function UserDetailPage() {
  const { id } = useParams();
  const { message } = App.useApp();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  const fetchUserDetails = useCallback(async (tab?: string) => {
    if (!id) return;
    setLoading(true);
    try {
      const response = await fetch(`/api/users/${id}`);
      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
        if (tab) {
          setActiveTab(tab);
        }
      } else {
        message.error('Failed to fetch user details');
        setUser(null);
      }
    } catch (error) {
      console.error('Error fetching user details:', error);
      message.error('An error occurred while fetching user details');
      setUser(null);
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchUserDetails();
  }, [fetchUserDetails]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="p-6">
        <Card>
          <div className="text-center">
            <Title level={4}>User not found</Title>
            <Text>The requested user could not be found.</Text>
          </div>
        </Card>
      </div>
    );
  }

  const getTabItems = () => {
    if (!user) return [];

    const items = [
      {
        key: 'overview',
        label: 'OVERVIEW',
        children: (
          <UserInfoForm 
            user={user} 
            onUpdate={(updatedUser) => setUser(updatedUser)} 
            isMentorView={true}
          />
        )
      },
      {
        key: 'portfolio',
        label: 'PORTFOLIO',
        children: (
          <AdminPortfolioTab userId={user.id} />
        )
      },
      {
        key: 'mentor-meetings',
        label: 'MEETINGS',
        children: (
          <AdminMentorMeetingsTab userId={user.id} />
        )
      },
      {
        key: 'appraisals',
        label: 'APPRAISALS',
        children: (
          <AdminAppraisalsTab userId={user.id} />
        )
      },
      {
        key: 'signoff-entries',
        label: 'ALL ENTRIES',
        children: (
          <AdminEntriesTable 
            userId={user.id}
            isShowDraft={false}
            title="All Entries"
          />
        )
      }
    ];
    return items;
  };

  return (
    <>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Breadcrumb
          items={[
            { title: <Link href="/admin/users">Users</Link> },
            { title: user ? `${user.name} (${user.role})` : 'Loading...' },
          ]}
          className="mb-4"
        />
        <Tabs
          style={{ marginTop: 12 }}
          activeKey={activeTab}
          onChange={setActiveTab}
          type="card"
          items={getTabItems()}
          className="user-detail-tabs"
        />
      </Space>
    </>
  );
} 