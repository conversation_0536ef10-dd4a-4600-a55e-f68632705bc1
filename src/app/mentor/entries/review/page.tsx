'use client';

import { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Tag,
  message as messageHook,
  Card,
  Form,
  Input,
  Space,
  Typography
} from 'antd';
import { EditOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { UserRole, Entry, Submission } from '@/types';
import dayjs from 'dayjs';
import Link from 'next/link';

function MentorReviewTable() {
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [loading, setLoading] = useState(true);
  console.log('submissions', submissions);
  const router = useRouter();
  const { data: session } = useSession();
  const [messageApi, contextHolder] = messageHook.useMessage();

  useEffect(() => {
    if (session && session.user?.role !== UserRole.MENTOR) {
      router.push('/');
      return;
    }
    if (session?.user?.role === UserRole.MENTOR) {
      fetchSubmissions();
    }
  }, [session, router]);

  const fetchSubmissions = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/mentor/submissions');
      if (!response.ok) throw new Error('Failed to fetch submissions for review');
      const data = await response.json();
      setSubmissions(data);
    } catch (error) {
      console.error('Error fetching submissions:', error);
      messageApi.error('Failed to load submissions for review');
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: 'Trainee',
      dataIndex: ['trainee', 'name'],
      key: 'trainee',
      render: (name: string | null) => name || '',
    },
    {
      title: 'Placement',
      key: 'placement',
      render: (_: unknown, record: Submission) => {
        // Try to get placement from submission first, then from first entry
        const placement = record.placement || record.entries?.[0]?.placement;
        if (!placement) return '-';

        const clientName = placement.client?.name || '';
        const placementName = placement.name || '';

        return placementName || clientName;
      },
    },
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: 'Status', 
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusColors = {
          pending: 'orange',
          approved: 'green', 
          rejected: 'red'
        };
        return (
          <Tag color={statusColors[status as keyof typeof statusColors] || 'default'}>
            {status.toUpperCase()}
          </Tag>
        );
      },
    },
    {
      title: 'Entries Count',
      key: 'entriesCount',
      render: (_: unknown, record: Submission) => record.entries?.length || 0,
    },
    {
      title: 'Submitted',
      dataIndex: 'submittedAt',
      key: 'submittedAt',
      render: (date?: string) => date ? dayjs(date).format('DD/MM/YYYY HH:mm') : '-',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: unknown, record: Submission) => (
        <Space>
          {record.entries?.[0] && (
            <Link 
              href={`/signoff-generator/${record.id}`}
              target="_blank"
            >
              View
            </Link>
          )}
        </Space>
      ),
    },
  ];

  if (session === undefined) return <div>Loading session...</div>;
  if (session?.user?.role !== UserRole.MENTOR) return null;

  return (
    <div className="p-6">
      {contextHolder} 
      <Card title="Review Trainee Submissions">
        <Table
          columns={columns}
          dataSource={submissions}
          loading={loading}
          rowKey="id"
          scroll={{ x: 'max-content' }}
        />
      </Card>
    </div>
  );
}

export default function MentorReviewPage() {
  return <MentorReviewTable />;
} 