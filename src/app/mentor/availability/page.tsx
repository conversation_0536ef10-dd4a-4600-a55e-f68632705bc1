'use client';

import { useState, useEffect } from 'react';
import { App } from 'antd';
import { useSession } from 'next-auth/react';
import MenteeAvailabilityCalendar from '@/components/mentoring/MenteeAvailabilityCalendar';
import { MentorAvailability } from '@/types';
import dayjs from 'dayjs';

export default function MentorAvailabilityPage() {
  const { message } = App.useApp();
  const { data: session } = useSession();
  const [availabilities, setAvailabilities] = useState<MentorAvailability[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchAvailabilities = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/mentor/availability');
      if (!response.ok) {
        throw new Error('Failed to fetch availability slots');
      }
      const data = await response.json();
      setAvailabilities(data);
    } catch (error) {
      message.error('Failed to fetch availability slots');
      console.error('Error fetching availability slots:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (session?.user?.id) {
      fetchAvailabilities();
    }
  }, [session?.user?.id]);

  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/mentor/availability?id=${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete availability slot');
      }

      // Update local state by removing the deleted slot
      setAvailabilities(prev => prev.filter(slot => slot.id !== id));
    } catch (error) {
      throw error;
    }
  };

  const handleUpdateDateSlots = async (
    date: Date,
    slots: {
      startTime: Date;
      endTime: Date;
      meetingMethod: "IN_PERSON" | "VIRTUAL";
      duration?: number;
      bufferTime?: number;
      meetingLocation?: string;
      meetingMessage?: string;
      key?: string;
    }[],
  ) => {
    try {
      const response = await fetch('/api/mentor/availability/date', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          date,
          slots
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update availability slots');
      }

      const createdSlots = await response.json();

      // Update local state
      setAvailabilities(prev => {
        const startOfDay = dayjs(date).startOf('day').toDate();
        const endOfDay = dayjs(date).endOf('day').toDate();

        // Get IDs of slots to keep (those with keys in the incoming slots array)
        const slotsToKeep = slots
          .filter(slot => slot.key)
          .map(slot => slot.key);

        // Filter out slots that:
        // 1. Are on a different date, OR
        // 2. Are booked, OR
        // 3. Are on the same date but not in the slotsToKeep list
        const filteredSlots = prev.filter(slot => {
          const slotDate = new Date(slot.startTime);
          const isOnSameDate = slotDate >= startOfDay && slotDate <= endOfDay;
          
          if (!isOnSameDate || slot.isBooked) {
            return true; // Keep slots from other dates and booked slots
          }
          
          return slotsToKeep.includes(slot.id); // Keep only slots that are in the keep list
        });

        // Add new slots
        return [...filteredSlots, ...createdSlots].sort((a, b) =>
          new Date(a.startTime).getTime() - new Date(b.startTime).getTime()
        );
      });
    } catch (error) {
      throw error;
    }
  };

  return (
    <MenteeAvailabilityCalendar
      availabilities={availabilities}
      onUpdateDateSlots={handleUpdateDateSlots}
      onDelete={handleDelete}
    />
  );
} 