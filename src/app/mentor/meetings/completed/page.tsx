'use client';

import { useState, useEffect } from 'react';
import { Card, Typography, Button, App } from 'antd';
import { PlusOutlined, DownloadOutlined } from '@ant-design/icons';
import { useSession } from 'next-auth/react';
import MeetingsList from '@/components/mentoring/MeetingsList';
import CompleteMeetingModal from '@/components/mentoring/CompleteMeetingModal';
import { Meeting, MeetingStatus } from '@/types';

const { Title } = Typography;

export default function MentorCompletedMeetingsPage() {
  const { message } = App.useApp();
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddMeetingModalOpen, setIsAddMeetingModalOpen] = useState(false);
  const [isEditMeetingModalOpen, setIsEditMeetingModalOpen] = useState(false);
  const [selectedMeetingForEdit, setSelectedMeetingForEdit] = useState<Meeting | null>(null);
  const [templateLoading, setTemplateLoading] = useState(false);
  const { data: session } = useSession();

  const isTrainingPrincipal = session?.user?.isTrainingPrincipal;
  useEffect(() => {
    fetchMeetings();
  }, []);

  const fetchMeetings = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/meetings');
      if (!response.ok) throw new Error('Failed to fetch meetings');
      
      const data = await response.json();
      const completedMeetings = data.filter(
        (meeting: Meeting) => meeting.status === MeetingStatus.COMPLETED || meeting.status === MeetingStatus.ACCEPTED
      );
      console.log('completedMeetings', completedMeetings);
      setMeetings(completedMeetings);
    } catch (error) {
      console.error('Error fetching meetings:', error);
      message.error('Failed to fetch meetings');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (meetingId: string, status: MeetingStatus) => {
    try {
      const response = await fetch(`/api/meetings/${meetingId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update meeting status');
      }
      
      message.success(`Meeting status updated successfully`);
      
      setMeetings(prevMeetings => 
        prevMeetings.map(meeting => 
          meeting.id === meetingId ? { ...meeting, status } : meeting
        )
      );

      if (status !== MeetingStatus.COMPLETED && status !== MeetingStatus.ACCEPTED) {
        setMeetings(prevMeetings => 
          prevMeetings.filter(meeting => meeting.id !== meetingId)
        );
      }
    } catch (error) {
      console.error('Error updating meeting status:', error);
      message.error(error instanceof Error ? error.message : 'Failed to update meeting status');
    }
  };

  const handleAddMeeting = async (values: {
    menteeId: string;
    meetingType: string;
    meetingMethod: string;
    meetingDateTime: Date;
    documentName: string;
    action?: string;
    s3Key?: string;
    feedbackFormId?: string;
    documentType?: string;
    tempMonthlyReviewId?: string;
  }) => {
    try {
      if (!session?.user?.id) {
        message.error('User session not found');
        return;
      }

      // First create availability
      const availabilityResponse = await fetch('/api/mentor/availability', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          startTime: values.meetingDateTime.toISOString(),
          endTime: new Date(values.meetingDateTime.getTime() + 60 * 60 * 1000).toISOString(), // 1 hour duration
          meetingMethod: values.meetingMethod,
          duration: 60,
          bufferTime: 0,
        }),
      });

      if (!availabilityResponse.ok) {
        const errorData = await availabilityResponse.json();
        throw new Error(errorData.error || 'Failed to create availability');
      }

      const availabilityData = await availabilityResponse.json();

      // Then create meeting with availability
      const meetingResponse = await fetch('/api/meetings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          candidateId: values.menteeId,
          proposedTime: values.meetingDateTime.toISOString(),
          type: values.meetingType,
          status: MeetingStatus.COMPLETED,
          availabilityId: availabilityData.id,
        }),
      });

      if (!meetingResponse.ok) {
        const errorData = await meetingResponse.json();
        throw new Error(errorData.error || 'Failed to create meeting');
      }
      
      const meetingData = await meetingResponse.json();

      // Mark availability as booked
      await fetch(`/api/mentor/availability/${availabilityData.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isBooked: true }),
      });

      // Link temp monthly review to the created meeting if exists
      if (values.tempMonthlyReviewId) {
        const linkResponse = await fetch(`/api/monthly-reviews/${values.tempMonthlyReviewId}/link-meeting`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            meetingId: meetingData.id,
          }),
        });

        if (!linkResponse.ok) {
          console.warn('Failed to link temp monthly review to meeting');
          // Continue anyway since meeting was created successfully
        }
      }

      if (values.documentName || values.s3Key || values.feedbackFormId) {
        const completeResponse = await fetch(`/api/meetings/${meetingData.id}/complete`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            documentName: values.documentName,
            action: values.action,
            s3Key: values.s3Key,
            feedbackFormId: values.feedbackFormId,
            documentType: values.documentType,
          }),
        });

        if (!completeResponse.ok) throw new Error('Failed to complete meeting');
      }

      message.success('Meeting added successfully');
      setIsAddMeetingModalOpen(false);
      fetchMeetings();
    } catch (error) {
      console.error('Error adding meeting:', error);
      message.error(error instanceof Error ? error.message : 'Failed to add meeting');
    }
  };

  const handleEditMeeting = (meeting: Meeting) => {
    setSelectedMeetingForEdit(meeting);
    setIsEditMeetingModalOpen(true);
  };

  const handleUpdateMeeting = async (values: {
    meetingId: string;
    menteeId?: string;
    meetingType?: string;
    meetingMethod?: string;
    meetingDateTime?: Date;
    documentName: string;
    action?: string;
    s3Key?: string;
    feedbackFormId?: string;
    documentType?: string;
  }) => {
    try {
      if (!selectedMeetingForEdit) return;

      if (values.menteeId && values.meetingType && values.meetingDateTime) {
        // Update availability if meeting method changed
        if (values.meetingMethod && selectedMeetingForEdit.availabilityId) {
          const availabilityUpdateResponse = await fetch(`/api/mentor/availability/${selectedMeetingForEdit.availabilityId}`, {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              meetingMethod: values.meetingMethod,
              startTime: values.meetingDateTime.toISOString(),
              endTime: new Date(values.meetingDateTime.getTime() + 60 * 60 * 1000).toISOString(),
            }),
          });

          if (!availabilityUpdateResponse.ok) {
            console.warn('Failed to update availability, continuing...');
          }

          // Always update meeting details
          const meetingUpdateResponse = await fetch(`/api/meetings/${values.meetingId}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              candidateId: values.menteeId,
              proposedTime: values.meetingDateTime.toISOString(),
              type: values.meetingType,
              availabilityId: selectedMeetingForEdit.availabilityId,
            }),
          });

          if (!meetingUpdateResponse.ok) {
            const errorData = await meetingUpdateResponse.json();
            throw new Error(errorData.error || 'Failed to update meeting details');
          }
        } else if (values.meetingMethod && !selectedMeetingForEdit.availabilityId) {
          // Create new availability if meeting didn't have one
          const availabilityResponse = await fetch('/api/mentor/availability', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              startTime: values.meetingDateTime.toISOString(),
              endTime: new Date(values.meetingDateTime.getTime() + 60 * 60 * 1000).toISOString(),
              meetingMethod: values.meetingMethod,
              duration: 60,
              bufferTime: 0,
            }),
          });

          if (availabilityResponse.ok) {
            const availabilityData = await availabilityResponse.json();
            
            // Update meeting to link to new availability
            const meetingUpdateResponse = await fetch(`/api/meetings/${values.meetingId}`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                candidateId: values.menteeId,
                proposedTime: values.meetingDateTime.toISOString(),
                type: values.meetingType,
                availabilityId: availabilityData.id,
              }),
            });

            if (meetingUpdateResponse.ok) {
              // Mark availability as booked
              await fetch(`/api/mentor/availability/${availabilityData.id}`, {
                method: 'PATCH',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ isBooked: true }),
              });
            }
          }
        } else {
          // Update meeting when no meetingMethod provided or no availability exists
          const meetingUpdateResponse = await fetch(`/api/meetings/${values.meetingId}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              candidateId: values.menteeId,
              proposedTime: values.meetingDateTime.toISOString(),
              type: values.meetingType,
              availabilityId: selectedMeetingForEdit.availabilityId || null,
            }),
          });

          if (!meetingUpdateResponse.ok) {
            const errorData = await meetingUpdateResponse.json();
            throw new Error(errorData.error || 'Failed to update meeting details');
          }

          // If meeting has availability but no meetingMethod was provided, update availability time
          if (selectedMeetingForEdit.availabilityId) {
            await fetch(`/api/mentor/availability/${selectedMeetingForEdit.availabilityId}`, {
              method: 'PATCH',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                startTime: values.meetingDateTime.toISOString(),
                endTime: new Date(values.meetingDateTime.getTime() + 60 * 60 * 1000).toISOString(),
              }),
            });
          }
        }
      }

      const response = await fetch(`/api/meetings/${values.meetingId}/complete`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          documentName: values.documentName,
          action: values.action,
          s3Key: values.s3Key,
          feedbackFormId: values.feedbackFormId,
          documentType: values.documentType,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update meeting');
      }

      message.success('Meeting updated successfully');
      setIsEditMeetingModalOpen(false);
      setSelectedMeetingForEdit(null);
      fetchMeetings();
    } catch (error) {
      console.error('Error updating meeting:', error);
      message.error(error instanceof Error ? error.message : 'Failed to update meeting');
    }
  };

  const handleDownloadTemplate = async () => {
    try {
      setTemplateLoading(true);
      const response = await fetch('/api/meeting-templates/active');
      
      if (!response.ok) {
        if (response.status === 404) {
          message.error('No active meeting template found. Please contact admin.');
          return;
        }
        throw new Error('Failed to fetch template');
      }

      const template = await response.json();
      
      // Create download link
      const link = document.createElement('a');
      link.target = '_blank';
      link.href = template.downloadUrl;
      link.download = template.fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      message.success('Template downloaded successfully');
    } catch (error) {
      console.error('Error downloading template:', error);
      message.error('Failed to download template');
    } finally {
      setTemplateLoading(false);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <Title level={2}>Meetings</Title>
        <div className="flex gap-2">
          <Button 
            icon={<DownloadOutlined />}
            onClick={handleDownloadTemplate}
            loading={templateLoading}
          >
            Download Template
          </Button>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => setIsAddMeetingModalOpen(true)}
          >
            Add Meeting
          </Button>
        </div>
      </div>
      <Card>
        <MeetingsList
          meetings={meetings}
          loading={loading}
          onStatusChange={handleStatusChange}
          onRefresh={fetchMeetings}
          isMentor={true}
          mode="completed"
          onEdit={handleEditMeeting}
        />
      </Card>

      <CompleteMeetingModal
        open={isAddMeetingModalOpen}
        onClose={() => setIsAddMeetingModalOpen(false)}
        mode="create"
        onComplete={handleAddMeeting}
        onSuccess={() => {
          setIsAddMeetingModalOpen(false);
          fetchMeetings();
        }}
        isTrainingPrincipal={isTrainingPrincipal}
      />

      <CompleteMeetingModal
        open={isEditMeetingModalOpen}
        onClose={() => {
          setIsEditMeetingModalOpen(false);
          setSelectedMeetingForEdit(null);
        }}
        mode="edit"
        meeting={selectedMeetingForEdit || undefined}
        onComplete={handleUpdateMeeting}
        onSuccess={() => {
          setIsEditMeetingModalOpen(false);
          setSelectedMeetingForEdit(null);
          fetchMeetings();
        }}
        isTrainingPrincipal={isTrainingPrincipal}
      />
    </div>
  );
} 