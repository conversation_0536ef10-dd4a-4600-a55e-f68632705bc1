import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { Providers } from './providers';
import { App } from 'antd';
import QualificationRouteModal from '@/components/QualificationRouteModal';
import PlausibleAnalytics from '@/components/analytics/PlausibleAnalytics';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Pathways',
  description: 'Training Contract Management',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <Providers>
          <App>
            <QualificationRouteModal />
            <PlausibleAnalytics />
            {children}
          </App>
        </Providers>
      </body>
    </html>
  );
}
