"use client";

import { useState, useEffect } from "react";
import { <PERSON>ton, Typography, <PERSON>, Card, Tag, App, Breadcrumb } from "antd";
import { ArrowLeftOutlined, LinkOutlined, HomeOutlined, BookOutlined } from "@ant-design/icons";
import { Course } from "@/types";
import { useRouter, useParams } from "next/navigation";
import ContentRenderer from "@/components/common/ContentRenderer";
import "@uiw/react-md-editor/markdown-editor.css";
import "@uiw/react-markdown-preview/markdown.css";

const { Title, Text } = Typography;

export default function CourseDetailPage() {
  const { message } = App.useApp();
  const router = useRouter();
  const params = useParams();
  const courseId = params.id as string;
  
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(true);
  const [imageUrl, setImageUrl] = useState<string>('');

  useEffect(() => {
    if (courseId) {
      fetchCourse();
    }
  }, [courseId]);

  const fetchCourse = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/trainee/courses/${courseId}`);
      
      if (!response.ok) {
        throw new Error('Course not found');
      }
      
      const courseData = await response.json();
      setCourse(courseData);

      // Get signed URL for image if exists
      if (courseData.imageUrl) {
        if (courseData.imageUrl.startsWith('http')) {
          setImageUrl(courseData.imageUrl);
        } else {
          try {
            const response = await fetch('/api/get-signed-url?key=' + courseData.imageUrl);
            const data = await response.json();
            setImageUrl(data.url);
          } catch (error) {
            console.error('Error getting signed URL:', error);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching course:", error);
      message.error("Failed to load course details");
      router.push('/trainee/courses');
    } finally {
      setLoading(false);
    }
  };

  const handleExternalLinkClick = async () => {
    if (!course) return;
    
    // Open external link
    window.open(course.externalLink, '_blank', 'noopener,noreferrer');
    
    try {
      // Track the click
      await fetch(`/api/courses/${course.id}/click`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userAgent: navigator.userAgent,
        }),
      });
    } catch (error) {
      console.error('Error tracking click:', error);
    }
    
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (!course) {
    return (
      <div className="p-6">
        <Title level={2}>Course not found</Title>
      </div>
    );
  }

  return (
    <div className="p-6 mx-auto">
      <Button 
        icon={<ArrowLeftOutlined />} 
        onClick={() => router.back()}
        className="mb-4!"
      >
        Back to Courses
      </Button>

      {/* Course Header */}
      <Card className="mb-6!">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Course Image */}
          {imageUrl && (
            <div className="lg:w-1/3">
              <img
                src={imageUrl}
                alt={course.name}
                className="w-full h-48 lg:h-64 object-contain rounded-lg"
              />
            </div>
          )}
          
          {/* Course Info */}
          <div className={`${imageUrl ? 'lg:w-2/3' : 'w-full'}`}>
            <div className="mb-4">
              <Tag 
                color={course.category.color || 'blue'}
                className="mb-2"
              >
                {course.category.name}
              </Tag>
              <Title level={1} className="!mb-2">
                {course.name}
              </Title>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 mb-4">
              <Button
                type="primary"
                size="large"
                icon={<LinkOutlined />}
                onClick={handleExternalLinkClick}
                className="bg-[#E67E22] hover:bg-[#D35400] border-none"
              >
                Go to Course
              </Button>
            </div>

            {/* External Link Info */}
            <div className="p-3 bg-gray-50 mb-4 rounded-lg">
              <Text strong>Course Link: </Text>
              <a 
                href={course.externalLink} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 break-all"
              >
                {course.externalLink}
              </a>
            </div>

            {/* Pricing course */}
            <div className="p-3 bg-gray-50 rounded-lg">
              <Text strong>Pricing: </Text>
              <Text>{course.pricing}</Text>
            </div>
          </div>
        </div>
      </Card>

      {/* Course Description */}
      <Card title="Course Description" className="mb-6!">
        <div className="prose max-w-none">
          <ContentRenderer content={course.description} />
        </div>
      </Card>

      {/* Bottom Actions */}
      <div className="flex justify-center">
        <Button
          type="primary"
          size="large"
          icon={<LinkOutlined />}
          onClick={handleExternalLinkClick}
          className="bg-[#E67E22] hover:bg-[#D35400] border-none px-8"
        >
          Start Learning Now
        </Button>
      </div>
    </div>
  );
} 