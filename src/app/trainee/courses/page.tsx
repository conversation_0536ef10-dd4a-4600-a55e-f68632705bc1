"use client";

import { useState, useEffect, useMemo } from "react";
import { <PERSON>, Typo<PERSON>, Button, Row, Col, Spin, Empty, App, Tooltip, Input, Select, Space } from "antd";
import { LinkOutlined, EyeOutlined, SearchOutlined, FilterOutlined } from "@ant-design/icons";
import { Course } from "@/types";
import { useRouter } from "next/navigation";
import ReactMarkdown from "react-markdown";

const { Title, Paragraph } = Typography;
const { Search } = Input;

export default function TraineeCoursesPage() {
  const { message } = App.useApp();
  const router = useRouter();
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [imageUrls, setImageUrls] = useState<Record<string, string>>({});
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  useEffect(() => {
    fetchCourses();
  }, []);

  const fetchCourses = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/trainee/courses");
      const data = await response.json();
      setCourses(data);

      // Fetch signed URLs for all course images
      const urls: Record<string, string> = {};
      await Promise.all(
        data.map(async (course: Course) => {
          if (course.imageUrl) {
            if (course.imageUrl.startsWith('http')) {
              urls[course.id] = course.imageUrl;
            } else {
              try {
                const response = await fetch('/api/get-signed-url?key=' + course.imageUrl);
                const data = await response.json();
                urls[course.id] = data.url;
              } catch (error) {
                console.error('Error getting signed URL:', error);
              }
            }
          }
        })
      );
      setImageUrls(urls);
    } catch (error) {
      message.error("Failed to fetch courses");
      setCourses([]);
      console.error("Error fetching courses:", error);
    } finally {
      setLoading(false);
    }
  };



  const handleViewDetails = (courseId: string) => {
    router.push(`/trainee/courses/${courseId}`);
  };

  const handleGoToCourse = async (course: Course) => {
    window.open(course.externalLink, '_blank', 'noopener,noreferrer');
    try {
      await fetch(`/api/courses/${course.id}/click`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userAgent: navigator.userAgent,
        }),
      });
    } catch (error) {
      console.error('Error tracking click:', error);
    }
  };

  const filteredCourses = useMemo(() => {
    return courses.filter((course) => {
      const matchesSearch = searchTerm === "" || 
        course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        course.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        course.category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (course.secondCategories && course.secondCategories.some(sc => 
          sc.category.name.toLowerCase().includes(searchTerm.toLowerCase())
        ));
      
      const matchesCategory = selectedCategory === null || 
        course.category.id === selectedCategory ||
        (course.secondCategories && course.secondCategories.some(sc => 
          sc.category.id === selectedCategory
        ));
      
      return matchesSearch && matchesCategory;
    });
  }, [courses, searchTerm, selectedCategory]);

  const categories = useMemo(() => {
    const allCategories = courses.reduce((acc, course) => {
      if (!acc.find(cat => cat.id === course.category.id)) {
        acc.push(course.category);
      }
      if (course.secondCategories) {
        course.secondCategories.forEach(sc => {
          if (!acc.find(cat => cat.id === sc.category.id)) {
            acc.push(sc.category);
          }
        });
      }
      return acc;
    }, [] as Course['category'][]);
    return allCategories.sort((a, b) => a.name.localeCompare(b.name));
  }, [courses]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (courses.length === 0) {
    return (
      <div className="p-6">
        <Title level={2}>Courses</Title>
        <Empty description="No courses available" />
      </div>
    );
  }

  return (
    <div className="p-6">
      <Title level={2}>Courses</Title>
      <Paragraph className="mb-6">
        Choose from a variety of courses and exercises to further your technical
        knowledge and practice skills.
      </Paragraph>

      <div className="mb-6">
        <Space direction="vertical" size="middle" className="w-full">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Search
                placeholder="Search courses by name, description, or category..."
                allowClear
                enterButton={<SearchOutlined />}
                size="large"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="sm:w-64">
              <Select
                placeholder="Filter by category"
                size="large"
                value={selectedCategory}
                onChange={setSelectedCategory}
                className="w-full"
                suffixIcon={<FilterOutlined />}
                options={[
                  {value: null, label: "All Categories"},
                  ...categories.map(category => ({
                    value: category.id,
                    label: category.name,
                  }))
                ]}
              />
            </div>
          </div>
          
          {(searchTerm || selectedCategory) && (
            <div className="text-sm text-gray-600">
              Showing {filteredCourses.length} of {courses.length} courses
              {searchTerm && ` matching "${searchTerm}"`}
              {selectedCategory && ` in ${categories.find(c => c.id === selectedCategory)?.name || 'selected category'}`}
            </div>
          )}
        </Space>
      </div>

      <Row gutter={[16, 16]}>
        {filteredCourses.length === 0 ? (
          <Col span={24}>
            <Empty 
              description="No courses found matching your criteria" 
              className="py-12"
            />
          </Col>
        ) : (
          filteredCourses.map((course) => (
          <Col xs={24} sm={12} lg={8} xl={6} key={course.id}>
            <Card
              hoverable
              className="h-full overflow-hidden relative"
              styles={{
                body: {
                  padding: 0,
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column'
                }
              }}
            >
              {/* Category Banner */}
              <div className="absolute top-0 left-0 z-10 flex flex-col gap-1">
                <div 
                  className="px-3 py-1 text-white text-sm font-medium w-fit rounded-br-md truncate max-w-[200px]"
                  style={{ backgroundColor: course.category.color || '#666' }}
                >
                  <Tooltip title={course.category.name}>
                    {course.category.name}
                  </Tooltip>
                </div>
                {course.secondCategories && course.secondCategories.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {course.secondCategories.slice(0, 2).map((sc) => (
                      <div
                        key={sc.id}
                        className="px-2 py-1 text-xs text-white rounded-md truncate max-w-[100px]"
                        style={{ backgroundColor: sc.category.color || '#666' }}
                      >
                        <Tooltip title={sc.category.name}>
                          {sc.category.name}
                        </Tooltip>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Course Image */}
              <div className="h-[200px] w-full bg-gray-100">
                <img
                  alt={course.name}
                  src={course.imageUrl ? (imageUrls[course.id] || '/placeholder-image.jpg') : '/placeholder-image.jpg'}
                  className="w-full h-full object-contain p-2"
                />
              </div>

              {/* Course Content */}
              <div className="p-4 flex flex-col flex-grow">
                <div className="flex-grow">
                  <Title level={4} className="!mb-2 !text-lg">
                    {course.name}
                  </Title>

                  {/* {course.pricing && (
                    <div className="mb-3">
                      <span className="inline-block bg-green-100 text-green-800 text-sm font-semibold px-3 py-1 rounded-full">
                        {course.pricing}
                      </span>
                    </div>
                  )} */}

                  <div className="mb-4">
                    <div className="text-sm text-gray-600 !mb-0 leading-relaxed prose prose-sm max-w-none line-clamp-4">
                      {(() => {
                        const cleanDescription = course.description
                          .replace(/<video[\s\S]*?<\/video>/gi, '')
                          .replace(/<iframe[\s\S]*?<\/iframe>/gi, '')
                          .replace(/<audio[\s\S]*?<\/audio>/gi, '')
                          .replace(/<object[\s\S]*?<\/object>/gi, '');
                        return (
                          <ReactMarkdown>
                            {cleanDescription}
                          </ReactMarkdown>
                        );
                      })()}
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="mt-auto space-y-2">
                  <Button
                    block
                    icon={<EyeOutlined />}
                    onClick={() => handleViewDetails(course.id)}
                    className="bg-blue-50 border-blue-200 text-blue-600 hover:bg-blue-100 hover:border-blue-300"
                  >
                    View Details
                  </Button>
                  <Button
                    type="primary"
                    block
                    icon={<LinkOutlined />}
                    onClick={() => handleGoToCourse(course)}
                    className="bg-[#E67E22] hover:bg-[#D35400] border-none"
                  >
                    Go to Course
                  </Button>
                </div>
              </div>
            </Card>
          </Col>
          ))
        )}
      </Row>
    </div>
  );
}
