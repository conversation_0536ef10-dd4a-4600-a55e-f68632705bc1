'use client';

import { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation';
import { Card, Descriptions, Tag, Button, Space, message, Spin, Alert, Typography, Collapse, Tooltip } from 'antd';
import { ArrowLeftOutlined, CaretRightOutlined, DownloadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { useSession } from 'next-auth/react';
import { UserRole } from '@/types';

const { Title, Text } = Typography;

// Define interfaces for the new skill structure
interface PracticeSubSkill {
  id: string;
  name: string;
  practiceSubSkillType: 'sqe' | 'tc' | 'both';
  minSuggestedEntryCount: number;
  order: number;
}

interface PracticeSkillGroup {
  id: string;
  name: string;
  practiceSubSkills: PracticeSubSkill[];
}

interface PracticeSkill {
  id: string;
  name: string;
  practiceSkillGroups: PracticeSkillGroup[];
}

interface Entry {
  id: string;
  title: string;
  startDate: string;
  endDate: string;
  experience: string;
  evidence: string;
  learnt: string;
  moreExperience?: string;
  needMoreExperience?: string;
  contentiousType: string;
  status: string;
  taskedBy?: string;
  documentKey?: string;
  documentName?: string;
  practiceAreas: string[];
  submittedAt?: string;
  reviewedAt?: string;
  feedback?: string;
  entrySubSkills: {
    subSkill: PracticeSubSkill & {
      practiceSkillGroup: PracticeSkillGroup & {
        practiceSkill: PracticeSkill;
      };
    };
  }[];
  placement: {
    id: string;
    name: string;
    client: {
      id: string;
      name: string;
    };
    supervisor?: {
      id: string;
      name: string;
      email: string;
    };
    mentor?: {
      id: string;
      name: string;
      email: string;
    };
  };
  reviewer?: {
    id: string;
    name: string;
    email: string;
  };
}

export default function EntryDetailPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [entry, setEntry] = useState<Entry | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { data: session } = useSession();
  const [messageApi, contextHolder] = message.useMessage();
  const returnTo = searchParams.get('returnTo');

  useEffect(() => {
    if (session && session.user?.role !== UserRole.TRAINEE) {
      router.push('/');
      return;
    }
    
    if (session?.user?.role === UserRole.TRAINEE) {
      fetchEntry();
    }
  }, [session, router]);

  const fetchEntry = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/entries/${params.id}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch entry');
      }
      
      const data = await response.json();
      setEntry(data);
    } catch (error) {
      console.error('Error fetching entry:', error);
      messageApi.error('Failed to fetch entry details');
      setError('Failed to fetch entry details');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'signedoff':
        return 'success';
      case 'rejected':
        return 'error';
      case 'submitted':
        return 'processing';
      case 'draft':
        return 'default';
      default:
        return 'default';
    }
  };

  const downloadDocument = async (documentKey: string) => {
    try {
      messageApi.loading('Downloading document...');
      
      const response = await fetch('/api/files', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ documentKey }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to get download URL');
      }
      
      const data = await response.json();
      
      if (!data.url) {
        throw new Error('No download URL returned');
      }
      
      const a = document.createElement('a');
      a.target = '_blank';
      a.href = data.url;
      const filename = documentKey.split('/').pop() || entry?.documentName || 'document';
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      messageApi.success('Document downloaded successfully');
    } catch (error) {
      console.error('Error downloading document:', error);
      messageApi.error('Failed to download document');
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <Card>
          <div className="flex justify-center items-center h-32">
            <Spin size="large" />
          </div>
        </Card>
      </div>
    );
  }

  if (error || !entry) {
    return (
      <div className="p-6">
        <Alert
          message="Error"
          description={error || 'Entry not found'}
          type="error"
          showIcon
        />
      </div>
    );
  }

  // Group skills by PracticeSkill
  const groupedSkills = entry.entrySubSkills.reduce((acc, { subSkill }) => {
    const practiceSkill = subSkill.practiceSkillGroup.practiceSkill;
    if (!acc[practiceSkill.id]) {
      acc[practiceSkill.id] = {
        skill: practiceSkill,
        groups: {}
      };
    }
    
    const group = subSkill.practiceSkillGroup;
    if (!acc[practiceSkill.id].groups[group.id]) {
      acc[practiceSkill.id].groups[group.id] = {
        group,
        subSkills: []
      };
    }
    
    acc[practiceSkill.id].groups[group.id].subSkills.push(subSkill);
    return acc;
  }, {} as Record<string, {
    skill: PracticeSkill;
    groups: Record<string, {
      group: PracticeSkillGroup;
      subSkills: PracticeSubSkill[];
    }>;
  }>);

  return (
    <div className="space-y-6 p-6">
      {contextHolder}
      
      <div className="flex justify-between items-center">
        <Button 
          icon={<ArrowLeftOutlined />}
          onClick={() => router.push(returnTo || '/trainee/entries')}
        >
          Back to {returnTo ? 'QWE Portfolio' : 'Entries'}
        </Button>
        
        {entry.status === 'draft' && (
          <Button
            type="primary"
            onClick={() => router.push(`/trainee/entries/${entry.id}/edit${returnTo ? `?returnTo=${returnTo}` : ''}`)}
          >
            Edit Entry
          </Button>
        )}
      </div>

      <Card title="Entry Details">
        {/* <Title level={5}>{entry.title}</Title>
        <Text className="mb-4 block">{entry.experience}</Text>
         */}
        <Descriptions bordered column={1} className="mb-6">
          <Descriptions.Item label="Status">
            <Tag color={getStatusColor(entry.status)}>
              {entry.status.charAt(0).toUpperCase() + entry.status.slice(1).toLowerCase()}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Placement">
            {entry.placement.client.name}
          </Descriptions.Item>
          <Descriptions.Item label="Start Date">
            {entry.startDate ? dayjs(entry.startDate).format('DD/MM/YYYY') : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="End Date">
            {entry.endDate ? dayjs(entry.endDate).format('DD/MM/YYYY') : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="Submitted">
            {entry.submittedAt ? dayjs(entry.submittedAt).format('DD/MM/YYYY HH:mm') : '-'}
          </Descriptions.Item>
          {entry.reviewedAt && (
            <Descriptions.Item label="Reviewed">
              {dayjs(entry.reviewedAt).format('DD/MM/YYYY HH:mm')}
            </Descriptions.Item>
          )}
          {entry.reviewer && (
            <Descriptions.Item label="Reviewed By">
              {entry.reviewer.name} ({entry.reviewer.email})
            </Descriptions.Item>
          )}
          <Descriptions.Item label="Skills">
            {entry.entrySubSkills.length > 0 ? (
              <Collapse
                ghost
                expandIcon={({ isActive }) => (
                  <CaretRightOutlined rotate={isActive ? 90 : 0} style={{ fontSize: '12px' }} />
                )}
                style={{ width: '100%', maxWidth: '600px' }}
              >
                {Object.values(groupedSkills).map(({ skill, groups }) => (
                  <Collapse.Panel 
                    key={skill.id}
                    header={
                      <Space wrap style={{ width: '100%' }}>
                        <Typography.Text strong>{skill.name}</Typography.Text>
                        <Tag color="blue">
                          {Object.values(groups).reduce((sum, { subSkills }) => sum + subSkills.length, 0)} skills
                        </Tag>
                      </Space>
                    }
                  >
                    <Collapse
                      ghost
                      expandIcon={({ isActive }) => (
                        <CaretRightOutlined rotate={isActive ? 90 : 0} style={{ fontSize: '12px' }} />
                      )}
                    >
                      {Object.values(groups).map(({ group, subSkills }) => (
                        <Collapse.Panel
                          key={group.id}
                          header={
                            <Space wrap>
                              <Typography.Text>{group.name}</Typography.Text>
                              <Tag color="cyan">{subSkills.length} sub-skills</Tag>
                            </Space>
                          }
                        >
                          <div style={{ 
                            paddingLeft: '24px',
                            display: 'flex',
                            flexWrap: 'wrap',
                            gap: '8px'
                          }}>
                            {subSkills.map((subSkill) => (
                              <Tooltip key={subSkill.id} title={`${subSkill.name}`}>
                                <Tag 
                                  color={subSkill.practiceSubSkillType === 'sqe' ? 'purple' : 'green'}
                                  style={{ 
                                    margin: 0,
                                    maxWidth: '180px',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap'
                                  }}
                                >
                                  {subSkill.name}
                                </Tag>
                              </Tooltip>
                            ))}
                          </div>
                        </Collapse.Panel>
                      ))}
                    </Collapse>
                  </Collapse.Panel>
                ))}
              </Collapse>
            ) : (
              <Text type="secondary">None</Text>
            )}
          </Descriptions.Item>
          <Descriptions.Item label="Documents">
            <Space direction="vertical">
              {entry?.documentName && (
                <Space>
                  {entry.documentName}
                  {entry.documentKey && (
                    <Button 
                      type="link" 
                      icon={<DownloadOutlined />} 
                      onClick={() => entry.documentKey && downloadDocument(entry.documentKey)}
                      size="small"
                    >
                      Download
                    </Button>
                  )}
                </Space>
              )}
            </Space>
          </Descriptions.Item>
        </Descriptions>

        <div className="mt-6">
          <Title level={5}>Entry Details</Title>
          <Descriptions bordered column={1}>
            <Descriptions.Item label="Task Completed">
              {entry.experience}
            </Descriptions.Item>
            <Descriptions.Item label="What I Learned">
              {entry.learnt}
            </Descriptions.Item>
            {entry.moreExperience && (
              <Descriptions.Item label="More Experience Needed">
                {entry.moreExperience}
              </Descriptions.Item>
            )}
            {entry.needMoreExperience && (
              <Descriptions.Item label="Areas for Improvement">
                {entry.needMoreExperience}
              </Descriptions.Item>
            )}
          </Descriptions>
        </div>

        <div className="mt-6">
          <Title level={5}>Placement Details</Title>
          <Descriptions bordered column={1}>
            <Descriptions.Item label="Position">
              {entry.placement.name}
            </Descriptions.Item>
            <Descriptions.Item label="Client">
              {entry.placement.client.name}
            </Descriptions.Item>
            {entry.placement.supervisor && (
              <Descriptions.Item label="Supervisor">
                {entry.placement.supervisor.name} ({entry.placement.supervisor.email})
              </Descriptions.Item>
            )}
            {entry.placement.mentor && (
              <Descriptions.Item label="Mentor">
                {entry.placement.mentor.name} ({entry.placement.mentor.email})
              </Descriptions.Item>
            )}
          </Descriptions>
        </div>
      </Card>
    </div>
  );
}
