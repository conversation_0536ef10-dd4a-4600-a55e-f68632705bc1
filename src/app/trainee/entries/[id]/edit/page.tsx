'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { Card, Breadcrumb, Spin } from 'antd';
import { HomeOutlined, FileTextOutlined } from '@ant-design/icons';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { UserRole, Entry } from '@/types';
import EntryForm from '@/components/entry/EntryForm';
import QualificationRouteModal from '@/components/QualificationRouteModal';

export default function EditEntryPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session, status, update } = useSession();
  const [entry, setEntry] = useState<Entry | null>(null);
  const [loading, setLoading] = useState(true);
  const [showQualificationRouteModal, setShowQualificationRouteModal] = useState(false);
  const [userChecked, setUserChecked] = useState(false);
  const returnTo = searchParams.get('returnTo');

  useEffect(() => {
    // Redirect if not a Trainee
    if (session && session.user?.role !== UserRole.TRAINEE) {
      router.push('/');
    }
  }, [session, router]);

  // Check if user has a qualification route
  useEffect(() => {
    const checkQualificationRoute = async () => {
      // Skip if already checked
      if (userChecked) return;
      
      // Skip if session is loading
      if (status === 'loading') return;
      
      // Skip if not authenticated
      if (!session?.user) return;
      
      // Check if user already has a qualification route in session
      if (session.user.qualificationRoute) {
        setUserChecked(true);
        return;
      }
      
      try {
        const response = await fetch('/api/users/me');
        if (response.ok) {
          const userData = await response.json();
          if (!userData.qualificationRoute) {
            setShowQualificationRouteModal(true);
          }
          setUserChecked(true);
        }
      } catch (error) {
        console.error('Error checking qualification route:', error);
        setUserChecked(true);
      }
    };
    
    checkQualificationRoute();
  }, [session, status, userChecked]);

  useEffect(() => {
    const fetchEntry = async () => {
      try {
        const response = await fetch(`/api/entries/${params.id}`);
        if (!response.ok) throw new Error('Failed to fetch entry');
        const data = await response.json();
        setEntry(data);
      } catch (error) {
        console.error('Error fetching entry:', error);
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchEntry();
    }
  }, [params.id]);

  const handleSuccess = () => {
    if (returnTo) {
      router.push(returnTo);
    } else {
    router.push(`/trainee/entries/${params.id}`);
    }
  };

  const handleQualificationRouteComplete = async () => {
    setShowQualificationRouteModal(false);
    
    // Refresh session to get updated qualification route
    await update();
    
    // Force a re-check
    setUserChecked(false);
  };

  // Show loading state while session is loading
  if (status === 'loading' || loading) {
    return (
      <div className="p-6">
        <Card>
          <div className="flex justify-center items-center h-32">
            <Spin />
          </div>
        </Card>
      </div>
    );
  }

  // If user is not a Trainee, don't render anything (redirect will happen in useEffect)
  if (session?.user?.role !== UserRole.TRAINEE) {
    return null;
  }

  return (
    <div className="p-6">
      <QualificationRouteModal 
        isOpen={showQualificationRouteModal} 
        onComplete={handleQualificationRouteComplete}
      />
      
      <Breadcrumb
        items={[
          {
            title: (
              <Link href="/trainee/training">
                <HomeOutlined /> Training Record
              </Link>
            ),
          },
          {
            title: (
              <Link href={returnTo || '/trainee/training?tab=portfolio'}>
                <FileTextOutlined /> {returnTo ? 'Portfolio' : 'Entries'}
              </Link>
            ),
          },
          {
            title: 'Edit Entry',
          },
        ]}
      />

      <h1 className="text-2xl font-bold mb-6">Edit Entry</h1>

      {entry && (
        <EntryForm 
          entryId={params.id as string} 
          onSuccess={handleSuccess}
        />
      )}
    </div>
  );
} 