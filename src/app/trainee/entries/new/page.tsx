'use client';

import { useState, useEffect, Suspense } from 'react';
import { 
  Typography, 
  Breadcrumb, 
  Spin 
} from 'antd';
import { 
  HomeOutlined, 
  FileTextOutlined, 
  FormOutlined 
} from '@ant-design/icons';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { UserRole } from '@/types';
import EntryForm from '@/components/entry/EntryForm';

const { Title } = Typography;

// This component will use searchParams
function NewEntryContent() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(true);
  const placementId = searchParams.get('placementId') || undefined;
  const returnTo = searchParams.get('returnTo');

  useEffect(() => {
    // Redirect if not authenticated or not a trainee
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
      return;
    }

    if (session && session.user?.role !== UserRole.TRAINEE) {
      router.push('/trainee/trainee-dashboard');
      return;
    }

    setLoading(false);
  }, [session, status, router]);

  const handleSuccess = () => {
    if (returnTo) {
      router.push(returnTo);
    } else {
      router.push('/trainee/training');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link href="/trainee/training">
                <HomeOutlined /> Training Record
              </Link>
            ),
          },
          {
            title: (
              <Link href={returnTo || '/trainee/training'}>
                <FileTextOutlined /> {returnTo ? 'Portfolio' : 'Entries'}
              </Link>
            ),
          },
          {
            title: (
              <>
                <FormOutlined /> New Entry
              </>
            ),
          },
        ]}
      />

      <Title level={2}>Create New Entry</Title>

      <EntryForm placementId={placementId} onSuccess={handleSuccess} />
    </div>
  );
}

// Main page component with Suspense
export default function NewEntryPage() {
  return (
    <Suspense fallback={
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    }>
      <NewEntryContent />
    </Suspense>
  );
}
