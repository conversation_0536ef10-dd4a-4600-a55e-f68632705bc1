'use client';

import { useEffect, Suspense, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, Alert, Spin } from 'antd';
import { useSession } from 'next-auth/react';
import { UserRole } from '@/types';
import EntryForm from '@/components/entry/EntryForm';
import QualificationRouteModal from '@/components/QualificationRouteModal';

// Component that uses searchParams
function CreateEntryContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const placementId = searchParams.get('placementId') || undefined;
  const { data: session, status, update } = useSession();
  const [showQualificationRouteModal, setShowQualificationRouteModal] = useState(false);
  const [userChecked, setUserChecked] = useState(false);

  useEffect(() => {
    // Redirect if not a Trainee
    if (session && session.user?.role !== UserRole.TRAINEE) {
      router.push('/');
    }
  }, [session, router]);

  // Check if user has a qualification route
  useEffect(() => {
    const checkQualificationRoute = async () => {
      // Skip if already checked
      if (userChecked) return;
      
      // Skip if session is loading
      if (status === 'loading') return;
      
      // Skip if not authenticated
      if (!session?.user) return;
      
      // Check if user already has a qualification route in session
      if (session.user.qualificationRoute) {
        setUserChecked(true);
        return;
      }
      
      try {
        const response = await fetch('/api/users/me');
        if (response.ok) {
          const userData = await response.json();
          if (!userData.qualificationRoute) {
            setShowQualificationRouteModal(true);
          }
          setUserChecked(true);
        }
      } catch (error) {
        console.error('Error checking qualification route:', error);
        setUserChecked(true);
      }
    };
    
    checkQualificationRoute();
  }, [session, status, userChecked]);

  const handleSuccess = () => {
    router.push('/trainee/entries');
  };

  const handleQualificationRouteComplete = async () => {
    setShowQualificationRouteModal(false);
    
    // Refresh session to get updated qualification route
    await update();
    
    // Force a re-check
    setUserChecked(false);
  };

  // Show loading state while session is loading
  if (status === 'loading') {
    return (
      <div className="p-6">
        <Card>
          <div className="flex justify-center items-center h-32">
            <p>Loading...</p>
          </div>
        </Card>
      </div>
    );
  }

  // If user is not a Trainee, don't render anything (redirect will happen in useEffect)
  if (session?.user?.role !== UserRole.TRAINEE) {
    return null;
  }

  return (
    <div className="p-6">
      <QualificationRouteModal 
        isOpen={showQualificationRouteModal} 
        onComplete={handleQualificationRouteComplete}
      />
      
      <h1 className="text-2xl font-bold mb-6">Submit New Entry</h1>

      <Alert
        message="Important"
        description="Please fill in all the required fields and upload a file to submit your entry."
        type="info"
        showIcon
        className="mb-6"
      />

      <EntryForm placementId={placementId} onSuccess={handleSuccess} />
    </div>
  );
}

// Main page component with Suspense
export default function CreateEntryPage() {
  return (
    <Suspense fallback={
      <div className="p-6">
        <Card>
          <div className="flex justify-center items-center h-32">
            <Spin size="large" />
          </div>
        </Card>
      </div>
    }>
      <CreateEntryContent />
    </Suspense>
  );
}