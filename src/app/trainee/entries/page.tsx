'use client';

import { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Card,
  Typography,
  Breadcrumb,
  Input,
  Spin,
  Tooltip,
  Badge,
  App
} from 'antd';
import {
  PlusOutlined,
  HomeOutlined,
  FileTextOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  FilePdfOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { UserRole, Entry, EntryStatus } from '@/types';
import Link from 'next/link';
import dayjs from 'dayjs';
import { practiceAreaOptions } from "@/helpers/practiceAreas";

const { Title, Text } = Typography;

export default function TraineeEntriesPage() {
  const [entries, setEntries] = useState<Entry[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const router = useRouter();
  const { data: session, status } = useSession();
  const { message, modal } = App.useApp();

  useEffect(() => {
    // Redirect if not authenticated or not a trainee
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
      return;
    }

    if (session?.user?.role !== UserRole.TRAINEE) {
      router.push('/trainee/trainee-dashboard');
      return;
    }
    fetchEntries();
  }, [session, status, router]);

  const fetchEntries = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/entries');
      if (!response.ok) throw new Error('Failed to fetch entries');
      const data = await response.json();
      setEntries(data);
    } catch (error) {
      message.error('Failed to load entries');
      console.error('Error fetching entries:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusTag = (status: EntryStatus) => {
    switch (status) {
      case 'draft':
        return <Tag color="default">Draft</Tag>;
      case 'submitted':
        return <Tag color="processing">Submitted</Tag>;
      case 'signedoff':
        return <Tag color="success">Signed Off</Tag>;
      case 'rejected':
        return <Tag color="error">Rejected</Tag>;
      default:
        return <Tag color="default">{status}</Tag>;
    }
  };

  const filteredEntries = entries
    .filter(entry =>
      entry.title.toLowerCase().includes(searchText.toLowerCase()) ||
      (entry.placement?.name || '').toLowerCase().includes(searchText.toLowerCase())
    )
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

  const viewDocument = async (documentKey: string) => {
    try {
      message.loading('Getting document URL...');
      const response = await fetch('/api/files', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ documentKey }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to get document URL');
      }
      
      const data = await response.json();
      
      if (!data.url) {
        throw new Error('No URL returned');
      }
      
      window.open(data.url, '_blank');
      message.success('Document opened successfully');
    } catch (error) {
      console.error('Error viewing document:', error);
      message.error('Failed to view document');
    }
  };

  const columns = [
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      minWidth: 180,
      render: (text: string, record: Entry) => (
        <Space direction="vertical" size={0}>
          <Text strong>{text}</Text>
          <Text type="secondary">{record.placement?.name}</Text>
        </Space>
      ),
    },
    {
      title: 'Date Range',
      key: 'dateRange',
      minWidth: 120,
      render: (_: unknown, record: Entry) => (
        <Space direction="vertical" size={0}>
          <Text>{dayjs(record.startDate).format('DD/MM/YYYY')}</Text>
          <Text>{dayjs(record.endDate).format('DD/MM/YYYY')}</Text>
        </Space>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: EntryStatus) => getStatusTag(status),
    },
    {
      title: 'Practice Areas',
      key: 'practiceAreas',
      width: 150,
      render: (_: unknown, record: Entry) => {
        // Create a map of ID to title for quick lookup
        const practiceAreaMap = new Map(
          practiceAreaOptions.map(area => [area.id.toString(), area.title])
        );
        
        const areaCount = record.practiceAreas?.length || 0;
        
        if (areaCount === 0) {
          return <Text type="secondary">None</Text>;
        }
        
        const tooltipContent = (
          <div style={{ 
            maxWidth: '300px',
            maxHeight: '300px', 
            overflow: 'auto',
            padding: '8px 4px',
            fontSize: '14px',
          }}>
            <div style={{ 
              marginBottom: '8px', 
              fontWeight: 500,
              borderBottom: '1px solid rgba(255,255,255,0.2)',
              paddingBottom: '4px'
            }}>
              Practice Areas ({areaCount})
            </div>
            {record.practiceAreas?.map((areaId) => (
              <div key={areaId} style={{ 
                padding: '6px 8px',
                margin: '4px 0',
                backgroundColor: 'rgba(255,255,255,0.1)',
                borderRadius: '4px',
                display: 'flex',
                alignItems: 'center',
                gap: '6px'
              }}>
                <div style={{ 
                  width: '6px', 
                  height: '6px', 
                  backgroundColor: '#52c41a',
                  borderRadius: '50%' 
                }} />
                {practiceAreaMap.get(String(areaId)) || String(areaId)}
              </div>
            ))}
          </div>
        );

        return (
          <Tooltip 
            title={tooltipContent} 
            overlayStyle={{ maxWidth: 'none' }}
            overlayInnerStyle={{ 
              borderRadius: '6px',
              padding: '12px'
            }}
          >
            <Badge 
              count={areaCount} 
              style={{ backgroundColor: '#52c41a' }}
              overflowCount={99}
            >
              <Tag color="success">Areas</Tag>
            </Badge>
          </Tooltip>
        );
      },
    },
    {
      title: 'Skills',
      key: 'skills',
      width: 120,
      render: (_: unknown, record: Entry) => {
        const skillCount = record.entrySubSkills?.length || 0;
        
        if (skillCount === 0) {
          return <Text type="secondary">None</Text>;
        }

        const tooltipContent = (
          <div style={{ 
            maxWidth: '300px',
            maxHeight: '300px', 
            overflow: 'auto',
            padding: '8px 4px',
            fontSize: '14px',
          }}>
            <div style={{ 
              marginBottom: '8px', 
              fontWeight: 500,
              borderBottom: '1px solid rgba(255,255,255,0.2)',
              paddingBottom: '4px'
            }}>
              Skills ({skillCount})
            </div>
            {record.entrySubSkills?.map((skill) => (
              <div key={skill.subSkillId} style={{ 
                padding: '6px 8px',
                margin: '4px 0',
                backgroundColor: 'rgba(255,255,255,0.1)',
                borderRadius: '4px',
                display: 'flex',
                gap: '6px',
                flexDirection: 'column',
                alignItems: 'flex-start'
              }}>
                <div style={{ 
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  width: '100%'
                }}>
                  <div style={{ 
                    width: '6px', 
                    height: '6px', 
                    backgroundColor: '#1890ff',
                    borderRadius: '50%',
                    flexShrink: 0
                  }} />
                  <div>
                    <div style={{ fontWeight: 500 }}>{skill.subSkill?.name || 'Unknown Skill'}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        );

        return (
          <Tooltip 
            title={tooltipContent} 
            overlayStyle={{ maxWidth: 'none' }}
            overlayInnerStyle={{ 
              borderRadius: '6px',
              padding: '12px'
            }}
          >
            <Badge 
              count={skillCount} 
              style={{ backgroundColor: '#1890ff' }}
              overflowCount={99}
            >
              <Tag color="blue">Skills</Tag>
            </Badge>
          </Tooltip>
        );
      },
    },
    {
      title: 'Document',
      key: 'document',
      render: (_: unknown, record: Entry) => (
        record.documentKey ? (
          <Space className="flex justify-center items-center">
            <Badge status="success" text="Attached" />
            <Button
              type="link"
              icon={<FilePdfOutlined />}
              size="small"
              onClick={() => record.documentKey && viewDocument(record.documentKey)}
            >
              View
            </Button>
          </Space>
        ) : (
          <Badge status="default" text="None" />
        )
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 110,
      render: (_: unknown, record: Entry) => (
        <Space>
          <Tooltip title="View">
            <Button
              icon={<EyeOutlined />}
              size="small"
              onClick={() => router.push(`/trainee/entries/${record.id}`)}
            />
          </Tooltip>
          {record.status === 'draft' && (
            <Tooltip title="Edit">
              <Button
                icon={<EditOutlined />}
                size="small"
                onClick={() => router.push(`/trainee/entries/${record.id}/edit`)}
              />
            </Tooltip>
          )}
          {record.status === 'draft' && (
            <Tooltip title="Delete">
              <Button
                icon={<DeleteOutlined />}
                size="small"
                danger
                onClick={() => handleDelete(record.id)}
              />
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  const handleDelete = async (id: string) => {
    // Show confirmation dialog
    modal.confirm({
      title: 'Are you sure you want to delete this entry?',
      content: 'This action cannot be undone.',
      okText: 'Yes, Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      async onOk() {
        try {
          // Show loading message
          message.loading('Deleting entry...');
          
          // Make DELETE request to API
          const response = await fetch(`/api/entries/${id}`, {
            method: 'DELETE',
          });
          
          // Check if response is okay
          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to delete entry');
          }
          
          // If successful, remove entry from state
          setEntries(entries.filter(entry => entry.id !== id));
          
          // Show success message
          message.success('Entry deleted successfully');
        } catch (error) {
          console.error('Error deleting entry:', error);
          message.error(error instanceof Error ? error.message : 'Failed to delete entry');
        }
      },
    });
  };

  return (
    <div className="space-y-6 p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link href="/trainee/training">
                <HomeOutlined /> Portfolio
              </Link>
            ),
          },
          {
            title: (
              <>
                <FileTextOutlined /> Entries
              </>
            ),
          },
        ]}
      />

      <div className="flex justify-between items-center">
        <Title level={2}>My Entries</Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => router.push('/trainee/entries/new')}
        >
          Create Entry
        </Button>
      </div>

      <Card>
        <div className="mb-4">
          <Input
            placeholder="Search entries..."
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={e => setSearchText(e.target.value)}
            allowClear
          />
        </div>

        <Spin spinning={loading}>
          <Table
            columns={columns}
            dataSource={filteredEntries}
            rowKey="id"
            pagination={{ pageSize: 10 }}
            scroll={{ x: 'max-content' }}
          />
        </Spin>
      </Card>
    </div>
  );
}