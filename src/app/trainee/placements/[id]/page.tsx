'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import {
  Card,
  Descriptions,
  Button,
  Upload,
  Spin,
  Divider,
  Tag,
  Typography,
  Alert,
  App
} from 'antd';
import { UploadOutlined, DownloadOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd';
import dayjs from 'dayjs';
import type { Placement } from '@/types';

const { Title, Text } = Typography;

export default function PlacementDetail() {
  const params = useParams();
  const router = useRouter();
  const [placement, setPlacement] = useState<Placement | null>(null);
  const [loading, setLoading] = useState(true);
  const [uploadLoading, setUploadLoading] = useState(false);
  const { message } = App.useApp();

  useEffect(() => {
    fetchPlacement();
  }, [params.id]);

  const fetchPlacement = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/placements/${params.id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch placement');
      }
      const data = await response.json();
      setPlacement(data);
    } catch (error) {
      message.error('Failed to load placement details');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const uploadProps: UploadProps = {
    name: 'file',
    action: `/api/placements/${params.id}/document`,
    headers: {
      authorization: 'authorization-text',
    },
    beforeUpload: (file) => {
      const isValidType = [
        'application/pdf',
        'image/jpeg',
        'image/png',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ].includes(file.type);

      if (!isValidType) {
        message.error('You can only upload PDF, JPG, PNG, DOC, or DOCX files!');
        return Upload.LIST_IGNORE;
      }

      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('File must be smaller than 10MB!');
        return Upload.LIST_IGNORE;
      }

      setUploadLoading(true);
      return true;
    },
    onChange(info) {
      if (info.file.status === 'done') {
        setUploadLoading(false);
        message.success(`${info.file.name} file uploaded successfully`);
        // Refresh placement data
        fetchPlacement();
      } else if (info.file.status === 'error') {
        setUploadLoading(false);
        message.error(`${info.file.name} file upload failed.`);
      }
    },
  };

  const downloadTemplate = () => {
    window.open('/templates/qwe_document_template.pdf', '_blank');
  };

  const downloadDocument = () => {
    if (placement?.documentKey) {
      window.open(`/api/documents/${placement.documentKey}`, '_blank');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (!placement) {
    return <Alert message="Placement not found" type="error" />;
  }

  // Convert part time days from numbers to day names
  const getDayName = (dayNumber: number): string => {
    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    return days[dayNumber - 1] || '';
  };

  return (
    <div className="space-y-6">
      <Button
        icon={<ArrowLeftOutlined />}
        onClick={() => router.push('/trainee/placements')}
      >
        Back to Placements
      </Button>

      <Title level={2}>{placement.name}</Title>

      <Card title="Placement Details">
        <Descriptions bordered column={{ xs: 1, sm: 2, md: 2, lg: 2 }}>
          <Descriptions.Item label="Start Date">
            {dayjs(placement.startDate).format('DD/MM/YYYY')}
          </Descriptions.Item>
          <Descriptions.Item label="End Date">
            {placement.endDate ? dayjs(placement.endDate).format('DD/MM/YYYY') : 'Ongoing'}
          </Descriptions.Item>
          <Descriptions.Item label="Supervisor">
            {placement.supervisorName}
          </Descriptions.Item>
          <Descriptions.Item label="Supervisor Email">
            {placement.supervisorEmail}
          </Descriptions.Item>
          <Descriptions.Item label="Organisation Name">
            {placement.orgPosition || ''}
          </Descriptions.Item>
          <Descriptions.Item label="Organisation SRA Number (if applicable)">
            {placement.orgSraNumber || ''}
          </Descriptions.Item>
          <Descriptions.Item label="Schedule">
            {placement.isFullTime ? 'Full Time' : 'Part Time'}
            {!placement.isFullTime && placement.partTimeDays && (
              <div className="mt-2">
                {placement.partTimeDays.map((day: number) => (
                  <Tag key={day} className="mr-1 mb-1">{getDayName(day)}</Tag>
                ))}
              </div>
            )}
          </Descriptions.Item>
          <Descriptions.Item label="Days Missed">
            {placement.daysMissed}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      <Card title="QWE Document">
        {placement.documentKey ? (
          <div className="space-y-4">
            <p><strong>Document:</strong> {placement.documentName}</p>
            <Button
              icon={<DownloadOutlined />}
              onClick={downloadDocument}
              type="primary"
            >
              Download Document
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <Alert
              message="No QWE document uploaded yet"
              description="Please upload your QWE document to complete your placement record."
              type="info"
              showIcon
            />
            <Upload {...uploadProps}>
              <Button
                icon={<UploadOutlined />}
                loading={uploadLoading}
              >
                Upload QWE Document
              </Button>
            </Upload>
            <Text type="secondary">
              Accepted file types: PDF, JPG, PNG, DOC, DOCX (Max: 10MB)
            </Text>
          </div>
        )}
        <Divider />
        <div>
          <Text strong>Need a template?</Text>
          <div className="mt-2">
            <Button
              icon={<DownloadOutlined />}
              onClick={downloadTemplate}
            >
              Download QWE Template
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}
