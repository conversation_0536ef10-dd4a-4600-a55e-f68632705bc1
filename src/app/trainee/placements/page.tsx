'use client';

import { useState, useEffect } from 'react';
import { Card, Typography, Breadcrumb, Spin, Table, Button, App } from 'antd';
import { HomeOutlined, EnvironmentOutlined, EditOutlined } from '@ant-design/icons';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { UserRole, Placement } from '@/types';
import dayjs from 'dayjs';
import PlacementFormModal from '@/components/placement/PlacementFormModal';

const { Title } = Typography;

export default function TraineePlacementsPage() {
  const { data: session, status } = useSession();
  const { message } = App.useApp();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [placements, setPlacements] = useState<Placement[]>([]);
  const [editingPlacement, setEditingPlacement] = useState<Placement | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const fetchPlacements = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/placements/trainee');
      if (!response.ok) throw new Error('Failed to fetch placements');
      const data = await response.json();
      setPlacements(data);
    } catch (error) {
      message.error('Failed to load placements');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
      return;
    }

    if (session && session.user?.role !== UserRole.TRAINEE) {
      router.push('/');
      return;
    }

    fetchPlacements();
  }, [session, status, router]);

  const handleSubmit = async (values: Partial<Placement>) => {
    if (!session?.user?.id) return;
    
    setSubmitting(true);
    try {
      const url = '/api/placements';
      const method = editingPlacement?.id ? 'PATCH' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...values,
          userId: session.user.id,
          ...(editingPlacement?.id && { id: editingPlacement.id })
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        if (data.code === 'SUPERVISOR_CLIENT_MISMATCH') {
          message.error({
            content: (
              <div>
                <div className="font-medium">Your supervisor already exists in the platform, please contact admin</div>
              </div>
            ),
            duration: 10,
            className: 'custom-error-message'
          });
          throw new Error(data.error);
        }

        throw new Error(data.error || 'Failed to save placement');
      }

      message.success(`Placement ${editingPlacement?.id ? 'updated' : 'created'} successfully`);
      setIsModalOpen(false);
      setEditingPlacement(null);
      fetchPlacements();
    } catch (error) {
      console.error('Error saving placement:', error);
      if (!(error instanceof Error && error.message.includes('already registered'))) {
        message.error({
          content: error instanceof Error ? error.message : 'Failed to save placement',
          duration: 5
        });
      }
    } finally {
      setSubmitting(false);
    }
  };

  const columns = [
    {
      title: 'Position',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Start Date',
      dataIndex: 'startDate',
      key: 'startDate',
      render: (date: string) => dayjs(date).format('DD/MM/YYYY'),
    },
    {
      title: 'End Date',
      dataIndex: 'endDate',
      key: 'endDate',
      render: (date?: string) => date ? dayjs(date).format('DD/MM/YYYY') : 'Ongoing',
    },
    {
      title: 'Schedule',
      key: 'schedule',
      render: (_: unknown, record: Placement) => (
        record.isFullTime ? 'Full Time' : 'Part Time'
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: unknown, record: Placement) => (
        <div className="space-x-2">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => {
              setEditingPlacement(record);
              setIsModalOpen(true);
            }}
          />
        </div>
      ),
    },
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (!session || session.user?.role !== UserRole.TRAINEE) {
    return null;
  }

  return (
    <div className="max-w-4xl mx-auto p-4">
      <Breadcrumb className="mb-4">
        <Breadcrumb.Item>
          <Link href="/">
            <HomeOutlined /> Home
          </Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <EnvironmentOutlined /> Placements
        </Breadcrumb.Item>
      </Breadcrumb>

      <div className="flex justify-between items-center mb-4">
        <Title level={2}>My Placements</Title>
        <Button 
          type="primary" 
          onClick={() => {
            setEditingPlacement(null);
            setIsModalOpen(true);
          }}
        >
          Add Placement
        </Button>
      </div>

      <Card>
        <Table
          dataSource={placements}
          columns={columns}
          rowKey="id"
          pagination={false}
        />
      </Card>

      <PlacementFormModal
        open={isModalOpen}
        onCancel={() => {
          setIsModalOpen(false);
          setEditingPlacement(null);
        }}
        onSubmit={handleSubmit}
        placement={editingPlacement || undefined}
        loading={submitting}
      />
    </div>
  );
}