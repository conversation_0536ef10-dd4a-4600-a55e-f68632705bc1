'use client';

import React, { Suspense } from 'react';
import { Tabs } from 'antd';
import { useSearchParams, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import PlacementsTab from './components/PlacementsTab';
import CompetencyTab from './components/CompetencyTab';
import PortfolioTab from './components/PortfolioTab';

const QWEPortfolioComponent = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { data: session } = useSession();
  const qualificationRoute = session?.user?.qualificationRoute?.toUpperCase();

  const getActiveTab = () => {
    const tab = searchParams.get('tab');
    switch (tab) {
      case 'placements':
        return '1';
      case 'competency':
        return '2';
      case 'portfolio':
        return '3';
      default:
        return '1';
    }
  };

  const handleTabChange = (activeKey: string) => {
    let tab;
    switch (activeKey) {
      case '1':
        tab = 'placements';
        break;
      case '2':
        tab = 'competency';
        break;
      case '3':
        tab = 'portfolio';
        break;
      default:
        tab = 'placements';
    }
    router.push(`/trainee/training?tab=${tab}`);
  };

  const getTitle = () => {
    if (qualificationRoute === 'TC') {
      return 'Training Record';
    }
    return 'QWE Portfolio';
  };

  const getSubtitle = () => {
    if (qualificationRoute === 'TC') {
      return 'Use your portfolio to accurately record your training contract and track your experiences against the practice skills standards.';
    }
    return "Record, track and monitor all your QWE against the SRA competencies. Use your entries to support confirmation of your QWE by a solicitor.";
  };

  const items = [
    {
      key: '1',
      label: 'Placements',
      children: <PlacementsTab qualificationRoute={qualificationRoute} />,
    },
    {
      key: '2',
      label: qualificationRoute === 'TC' ? 'Practice Skills Standards' : 'Training Record',
      children: <CompetencyTab />,
    },
    {
      key: '3',
      label: 'Portfolio',
      children: <PortfolioTab />,
    },
  ];

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">{getTitle()}</h1>
        <p className="text-gray-600">{getSubtitle()}</p>
      </div>
      <Tabs
        activeKey={getActiveTab()}
        items={items}
        onChange={handleTabChange}
        className="qwe-portfolio-tabs"
        type="card"
      />
    </div>
  );
};

export default function QWEPortfolio() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <QWEPortfolioComponent />
    </Suspense>
  );
} 