'use client';

import React from 'react';
import { Table, Progress, Space, Typography, Card, Row, Col, Statistic, App } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { useSession } from 'next-auth/react';

const { Text } = Typography;

interface SubSkill {
  id: string;
  name: string;
  doneEntryCount: number;
  minSuggestedEntryCount: number;
  progress: number;
  isCompleted: boolean;
}

interface SkillGroup {
  id: string;
  name: string;
  subSkills: SubSkill[];
  progress: number;
  isCompleted: boolean;
}

interface Skill {
  id: string;
  name: string;
  groups: SkillGroup[];
  progress: number;
  isCompleted: boolean;
}

interface PracticeArea {
  id: string;
  name: string;
  skills: Skill[];
  progress: number;
  totalSkills: number;
  completedSkills: number;
}

interface ProgressData {
  trainee: {
    name: string;
    email: string;
    qualificationRoute: string;
  };
  overallProgress: number;
  totalSkills: number;
  completedSkills: number;
  practiceAreas: PracticeArea[];
}

interface TableItem {
  key: string;
  id: string;
  name: string;
  progress: number;
  isCompleted: boolean;
  doneEntryCount?: number;
  minSuggestedEntryCount?: number;
  totalSuggested?: number;
  totalDone?: number;
  children?: TableItem[];
  level: 'skill' | 'group' | 'subskill';
}

const CompetencyTable: React.FC = () => {
  const [loading, setLoading] = React.useState(true);
  const [progressData, setProgressData] = React.useState<ProgressData | null>(null);
  const { message } = App.useApp();
  const { data: session } = useSession();

  const fetchProgressData = React.useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/trainee-skills/progress');
      if (!response.ok) throw new Error('Failed to fetch progress data');
      const data = await response.json();
      setProgressData(data);
    } catch (error) {
      console.error('Error fetching progress data:', error);
      message.error('Failed to load progress data');
    } finally {
      setLoading(false);
    }
  }, [message]);

  React.useEffect(() => {
    fetchProgressData();
  }, [fetchProgressData]);

  const transformDataToTable = (practiceAreas: PracticeArea[]): TableItem[] => {
    // Filter practice areas based on qualification route
    const filteredArea = practiceAreas.find(
      area => area.name.includes(session?.user?.qualificationRoute === 'TC' ? 'Training Contract' : 'Solicitors Qualifying Examination')
    );

    if (!filteredArea) return [];
    
    const skills = filteredArea.skills
      .sort((a, b) => a.name.localeCompare(b.name))
      .map(skill => {
      // Calculate total and completed suggested entries for the entire skill
      const skillTotals = skill.groups.flatMap(group => 
        group.subSkills.map(subSkill => ({
          suggested: subSkill.minSuggestedEntryCount,
          done: subSkill.doneEntryCount
        }))
      );
      
      const totalSuggested = skillTotals.reduce((sum, curr) => sum + curr.suggested, 0);
      const totalDone = skillTotals.reduce((sum, curr) => sum + curr.done, 0);
      
      return {
        key: `skill-${skill.id}`,
        id: skill.id,
        name: skill.name,
        progress: skill.progress,
        isCompleted: skill.isCompleted,
        level: 'skill' as const,
        totalSuggested,
        totalDone,
        children: skill.groups.map(group => {
          // Calculate total and completed suggested entries for each group
          const groupTotals = group.subSkills.map(subSkill => ({
            suggested: subSkill.minSuggestedEntryCount,
            done: subSkill.doneEntryCount
          }));
          
          const groupTotalSuggested = groupTotals.reduce((sum, curr) => sum + curr.suggested, 0);
          const groupTotalDone = groupTotals.reduce((sum, curr) => sum + curr.done, 0);
          
          return {
            key: `group-${group.id}`,
            id: group.id,
            name: group.name,
            progress: group.progress,
            isCompleted: group.isCompleted,
            level: 'group' as const,
            totalSuggested: groupTotalSuggested,
            totalDone: groupTotalDone,
            children: group.subSkills.map(subSkill => ({
              key: `subskill-${subSkill.id}`,
              id: subSkill.id,
              name: subSkill.name,
              progress: subSkill.progress,
              isCompleted: subSkill.isCompleted,
              doneEntryCount: subSkill.doneEntryCount,
              minSuggestedEntryCount: subSkill.minSuggestedEntryCount,
              level: 'subskill' as const,
            }))
          };
        })
      };
    });

    return skills;
  };

  const columns: ColumnsType<TableItem> = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      width: '60%',
      render: (text: string, record: TableItem) => (
        <Text strong={record.level === 'skill'}>
          {text}
        </Text>
      ),
    },
    {
      title: 'Progress',
      key: 'progress',
      width: '40%',
      render: (_: unknown, record: TableItem) => {
        if (record.level === 'subskill') {
          return (
            <Space direction="vertical" style={{ width: '100%' }} size="small">
              <div style={{ textAlign: 'right' }}>
                <Text type="secondary" className="text-sm">
                  {record.doneEntryCount} / {record.minSuggestedEntryCount}
                </Text>
              </div>
              <Progress 
                percent={Math.floor(record.progress)} 
                size="small"
                status={record.isCompleted ? 'success' : 'active'}
                showInfo={false}
              />
            </Space>
          );
        }
        
        return (
          <Space direction="vertical" style={{ width: '100%' }} size="small">
            <div style={{ textAlign: 'right' }}>
              <Text type="secondary" className="text-sm">
                {record.totalDone} / {record.totalSuggested}
              </Text>
            </div>
            <Progress 
              percent={Math.floor(record.progress)} 
              size="small"
              status={record.isCompleted ? 'success' : 'active'}
              showInfo={false}
            />
          </Space>
        );
      },
    },
  ];

  if (!progressData) {
    return (
      <div className="p-6">
        <Card loading={loading}>
          <div style={{ height: '400px' }} />
        </Card>
      </div>
    );
  }

  // Get the filtered practice area for statistics
  const filteredArea = progressData.practiceAreas.find(
    area => area.name.includes(session?.user?.qualificationRoute === 'TC' ? 'Training Contract (TC)' : 'Solicitors Qualifying Examination (SQE)')
  );

  return (
    <div className="space-y-6">
      <Row gutter={24}>
        <Col span={12}>
          <Card>
            <Statistic
              title="Overall Progress"
              value={filteredArea?.progress || 0}
              suffix="%"
              precision={1}
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card>
            <Statistic
              title="Completed Skills"
              value={filteredArea?.completedSkills || 0}
              suffix={` / ${filteredArea?.totalSkills || 0}`}
            />
          </Card>
        </Col>
      </Row>

      <Table
        columns={columns}
        dataSource={transformDataToTable(progressData.practiceAreas)}
        loading={loading}
        rowKey="key"
        scroll={{ x: 1000 }}
        className="skills-hierarchy-table"
      />
    </div>
  );
};

export default CompetencyTable; 