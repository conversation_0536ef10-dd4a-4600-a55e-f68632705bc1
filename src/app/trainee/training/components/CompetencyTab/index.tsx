'use client';

import React from 'react';
import { <PERSON>, Button } from 'antd';
import CompetencyTable from './CompetencyTable';
import { PlusOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';

const CompetencyTab = () => {
  const router = useRouter();

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-lg font-semibold ml-2">Skills Progress Overview</h2>
        <Button
          type="primary"
          size="middle"
          icon={<PlusOutlined />}
          onClick={(e) => {
            e.stopPropagation();
            router.push("/trainee/training?tab=portfolio");
          }}
        >
          Add Entry
        </Button>
      </div>
      <CompetencyTable />
    </div>
  );
};

export default CompetencyTab; 