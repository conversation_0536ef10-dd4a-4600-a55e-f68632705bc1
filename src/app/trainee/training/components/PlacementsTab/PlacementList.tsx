'use client';

import React from 'react';
import { <PERSON>, Card, Button, App } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import type { Placement } from '@/types';
import dayjs from 'dayjs';

interface PlacementListProps {
  selectedPlacement: string | null;
  onPlacementSelect: (id: string) => void;
  onAddPlacement: () => void;
  shouldRefresh?: boolean;
  onRefreshComplete?: () => void;
}

const PlacementList: React.FC<PlacementListProps> = ({
  selectedPlacement,
  onPlacementSelect,
  onAddPlacement,
  shouldRefresh,
  onRefreshComplete
}) => {
  const [placements, setPlacements] = React.useState<Placement[]>([]);
  const [loading, setLoading] = React.useState(false);
  const { message } = App.useApp();

  const fetchPlacements = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/placements/trainee');
      if (!response.ok) throw new Error('Failed to fetch placements');
      const data = await response.json();
      setPlacements(data);
    } catch (error) {
      console.error('Error fetching placements:', error);
      message.error('Failed to load placements');
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    fetchPlacements();
  }, []);

  React.useEffect(() => {
    if (shouldRefresh) {
      fetchPlacements().then(() => {
        onRefreshComplete?.();
      });
    }
  }, [shouldRefresh]);

  return (
    <Card title="Placements" className="h-full">
      <List
        loading={loading}
        dataSource={placements}
        renderItem={(placement) => (
          <List.Item
            className={`cursor-pointer transition-colors ${
              selectedPlacement === placement.id
                ? 'bg-orange-50'
                : 'hover:bg-gray-50'
            }`}
            onClick={() => onPlacementSelect(placement.id)}
          >
            <div className="w-full">
              <div className="flex flex-col justify-between items-start mb-2">
                <h3 className="font-medium whitespace-nowrap">{placement.client?.name || ''}</h3>
                <p className="text-sm text-gray-500 whitespace-nowrap">{placement.client?.email || ''}</p>
              </div>
              <div className="text-sm text-gray-500">
                <div>
                  {dayjs(placement.startDate).format('DD/MM/YYYY')} - 
                  {placement.endDate ? dayjs(placement.endDate).format('DD/MM/YYYY') : 'Present'}
                </div>
              </div>
            </div>
          </List.Item>
        )}
        footer={
          <Button
            type="dashed"
            icon={<PlusOutlined />}
            className="w-full"
            onClick={onAddPlacement}
          >
            Add Placement
          </Button>
        }
      />
    </Card>
  );
};

export default PlacementList; 