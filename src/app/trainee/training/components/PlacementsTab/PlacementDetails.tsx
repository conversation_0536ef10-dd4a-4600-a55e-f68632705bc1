'use client';

import React from 'react';
import { Card, Avatar, Button, List, Tag, Empty, App, Modal, Form, DatePicker, Table, Space, Typography, Row, Col } from 'antd';
import { UserOutlined, SendOutlined, EditOutlined, EyeOutlined, LockOutlined } from '@ant-design/icons';
import type { Placement, Entry, Submission } from '@/types';
import dayjs from 'dayjs';


const { Text } = Typography;
const { MonthPicker } = DatePicker;

interface FormValues {
  monthYear: dayjs.Dayjs;
}

interface SendEntriesModalProps {
  open: boolean;
  onClose: () => void;
  placement: Placement;
  reviewer: { id: string; name: string; email: string; } | undefined;
  reviewerRole: string | undefined;
  onSuccess: () => void;
}

const SendEntriesModal: React.FC<SendEntriesModalProps> = ({ open, onClose, placement, reviewer, onSuccess, reviewerRole }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = React.useState(false);
  const [entries, setEntries] = React.useState<Entry[]>([]);
  const [selectedEntries, setSelectedEntries] = React.useState<string[]>([]);
  const { message } = App.useApp();

  const fetchEntries = async (monthYear: dayjs.Dayjs) => {
    try {
      setLoading(true);
      const startOfMonth = monthYear.startOf('month').toISOString();
      const endOfMonth = monthYear.endOf('month').toISOString();
      const response = await fetch(`/api/entries?placementId=${placement.id}&startDate=${startOfMonth}&endDate=${endOfMonth}`);
      if (!response.ok) throw new Error('Failed to fetch entries');
      const data = await response.json();
      
      // Filter entries that END in the selected month (including both draft and submitted)
      const filteredEntries = data.filter((entry: Entry) => {
        const entryEndDate = dayjs(entry.endDate);
        return entryEndDate.month() === monthYear.month() && entryEndDate.year() === monthYear.year();
      });
      
      setEntries(filteredEntries);
    } catch (error) {
      console.error('Error fetching entries:', error);
      message.error('Failed to load entries');
    } finally {
      setLoading(false);
    }
  };

  const handleMonthYearChange = (date: dayjs.Dayjs | null) => {
    if (date) {
      fetchEntries(date);
    } else {
      setEntries([]);
    }
  };

  React.useEffect(() => {
    if (open) {
      const currentMonth = dayjs();
      form.setFieldsValue({ monthYear: currentMonth });
      fetchEntries(currentMonth);
    } else {
      form.resetFields();
      setSelectedEntries([]);
      setEntries([]);
    }
  }, [open]);

  const handleSubmit = async (values: FormValues) => {
    const availableEntries = selectedEntries.filter(entryId => {
      const entry = entries.find(e => e.id === entryId);
      if (!entry) return false;

      // Check if entry has already been submitted to this specific reviewer
      const hasSubmissionToReviewer = entry.entrySubmissions?.some((entrySubmission: any) =>
        entrySubmission.submission.reviewerId === reviewer?.id
      );

      return !hasSubmissionToReviewer;
    });

    if (availableEntries.length === 0) {
      message.error('Please select at least one available entry');
      return;
    }

    try {
      setLoading(true);
      
      // Calculate actual start and end dates from selected entries
      const selectedEntryObjects = availableEntries
        .map(entryId => entries.find(e => e.id === entryId))
        .filter((entry): entry is Entry => entry !== undefined);
      
      const allDates = selectedEntryObjects.flatMap(entry => [
        new Date(entry.startDate),
        new Date(entry.endDate)
      ]);
      
      const actualStartDate = dayjs(new Date(Math.min(...allDates.map(d => d.getTime()))));
      const actualEndDate = dayjs(new Date(Math.max(...allDates.map(d => d.getTime()))));
      
      const response = await fetch('/api/submissions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: `${values.monthYear.month() + 1}/${values.monthYear.year()}`,
          startDate: actualStartDate.toISOString(),
          endDate: actualEndDate.toISOString(),
          entryIds: availableEntries,
          placementId: placement.id,
          reviewerId: reviewer?.id,
          reviewerRole: reviewerRole || ''
        }),
      });

      if (!response.ok) throw new Error('Failed to send entries');
      
      message.success('Entries sent successfully');
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error sending entries:', error);
      message.error('Failed to send entries');
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      render: (text: string, record: Entry) => {
        const hasSubmissionToReviewer = record.entrySubmissions?.some((entrySubmission: any) =>
          entrySubmission.submission.reviewerId === reviewer?.id
        );

        return (
          <div className="flex items-center space-x-2">
            {hasSubmissionToReviewer && <LockOutlined className="text-gray-400" />}
            <Text strong={!hasSubmissionToReviewer} type={hasSubmissionToReviewer ? 'secondary' : undefined}>
              {text}
            </Text>
          </div>
        );
      },
    },
    {
      title: 'Date Range',
      key: 'dateRange',
      render: (_: unknown, record: Entry) => {
        const hasSubmissionToReviewer = record.entrySubmissions?.some((entrySubmission: any) =>
          entrySubmission.submission.reviewerId === reviewer?.id
        );

        return (
          <Space direction="vertical" size={0}>
            <Text type={hasSubmissionToReviewer ? 'secondary' : undefined}>
              {dayjs(record.startDate).format('DD/MM/YYYY')}
            </Text>
            <Text type={hasSubmissionToReviewer ? 'secondary' : undefined}>
              {dayjs(record.endDate).format('DD/MM/YYYY')}
            </Text>
          </Space>
        );
      },
    },
    {
      title: 'Status',
      key: 'status',
      render: (_: unknown, record: Entry) => {
        // Check if entry has already been submitted to this specific reviewer
        const hasSubmissionToReviewer = record.entrySubmissions?.some((entrySubmission: any) =>
          entrySubmission.submission.reviewerId === reviewer?.id
        );

        if (hasSubmissionToReviewer) {
          return (
            <div className="flex items-center space-x-1">
              <LockOutlined className="text-gray-400" />
              <Text type="secondary" className="text-sm">
                Already sent to {reviewer?.name}
              </Text>
            </div>
          );
        }
        return <Tag color="blue">Available</Tag>;
      },
    },
  ];

  const rowSelection = {
    selectedRowKeys: selectedEntries,
    onChange: (selectedRowKeys: React.Key[]) => {
      setSelectedEntries(selectedRowKeys as string[]);
    },
    getCheckboxProps: (record: Entry) => {
      const hasSubmissionToReviewer = record.entrySubmissions?.some((entrySubmission: any) =>
        entrySubmission.submission.reviewerId === reviewer?.id
      );
      return {
        disabled: hasSubmissionToReviewer,
      };
    },
  };

  const availableEntriesCount = entries.filter(entry => {
    const hasSubmissionToReviewer = entry.entrySubmissions?.some((entrySubmission: any) =>
      entrySubmission.submission.reviewerId === reviewer?.id
    );
    return !hasSubmissionToReviewer;
  }).length;

  return (
    <Modal
      title="Send Entries"
      open={open}
      onCancel={onClose}
      footer={null}
      width={800}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          monthYear: dayjs(),
        }}
      >
        <Form.Item
          name="monthYear"
          label="Month + Year"
          rules={[{ required: true, message: 'Please select a month and year' }]}
        >
          <MonthPicker 
            className="w-full" 
            placeholder="Select month and year (e.g. 6/2025)"
            format="M/YYYY"
            onChange={handleMonthYearChange}
          />
        </Form.Item>

        <div className="mb-4">
          <Text type="secondary">
            Showing entries ending in the selected month. Send to {reviewer?.name}
          </Text>
          {entries.length > 0 && (
            <div className="mt-2">
              <Text className="text-sm">
                {availableEntriesCount} available entries, {entries.length - availableEntriesCount} already sent to {reviewer?.name}
              </Text>
            </div>
          )}
        </div>

        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={entries}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 5 }}
          scroll={{ x: 'max-content' }}
          rowClassName={(record) => {
            const hasSubmissionToReviewer = record.entrySubmissions?.some((entrySubmission: any) =>
              entrySubmission.submission.reviewerId === reviewer?.id
            );
            return hasSubmissionToReviewer ? 'opacity-60' : '';
          }}
        />

        <div className="flex justify-end space-x-4 mt-4">
          <Button onClick={onClose}>
            Cancel
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            icon={<SendOutlined />}
            loading={loading}
            disabled={availableEntriesCount === 0 || selectedEntries.filter(id => {
              const entry = entries.find(e => e.id === id);
              if (!entry) return false;
              const hasSubmissionToReviewer = entry.entrySubmissions?.some((entrySubmission: any) =>
                entrySubmission.submission.reviewerId === reviewer?.id
              );
              return !hasSubmissionToReviewer;
            }).length === 0}
          >
            Send Entries ({selectedEntries.filter(id => {
              const entry = entries.find(e => e.id === id);
              if (!entry) return false;
              const hasSubmissionToReviewer = entry.entrySubmissions?.some((entrySubmission: any) =>
                entrySubmission.submission.reviewerId === reviewer?.id
              );
              return !hasSubmissionToReviewer;
            }).length})
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

interface PlacementDetailsProps {
  placementId: string | null;
  onEdit: (placement: Placement) => void;
  shouldRefresh?: boolean;
  onRefreshComplete?: () => void;
}

const SignoffRequestCard: React.FC<{
  submission: Submission;
}> = ({ submission }) => {

  const getStatusTag = (status: string) => {
    const normalizedStatus = status.toLowerCase();
    console.log('Status:', normalizedStatus);
    
    switch (normalizedStatus) {
      case 'pending':
        return <Tag color="processing">PENDING</Tag>;
      case 'approved':
        return <Tag color="success">APPROVED</Tag>;
      case 'rejected':
        return <Tag color="error">REJECTED</Tag>;
      case 'withdrawn':
        return <Tag>WITHDRAWN</Tag>;
      default:
        return <Tag>{status.toUpperCase()}</Tag>;
    }
  };

  return (
    <Card className="h-full shadow-sm hover:shadow-md transition-shadow">
      <div className="flex flex-col h-full min-h-[180px]">
        <div className="mb-4">
          <div className="flex flex-col items-start gap-2">
            <Text strong className="text-base">{submission.title}</Text>
            {submission.status && getStatusTag(submission.status)}
          </div>
          <div className="text-gray-500 mt-2">
            <div className="text-sm">{submission.entries.length} {submission.entries.length === 1 ? 'Entry' : 'Entries'}</div>
            <div className="text-sm">Sent {dayjs(submission.submittedAt).format('DD/MM/YYYY')}</div>
          </div>
        </div>
        <div className="mt-auto">
          <Button
            type="default"
            block
            icon={<EyeOutlined />}
            onClick={() => window.open(`/signoff-generator/${submission.id}`, '_blank')}
            // onClick={() => router.push(`/signoff-generator/${submission.id}`)}
          >
            View Entries
          </Button>
        </div>
      </div>
    </Card>
  );
};

const PlacementDetails: React.FC<PlacementDetailsProps> = ({ placementId, onEdit, shouldRefresh, onRefreshComplete }) => {
  const [placement, setPlacement] = React.useState<Placement | null>(null);
  const [loading, setLoading] = React.useState(false);
  const [submissionsLoading, setSubmissionsLoading] = React.useState(false);
  const [submissions, setSubmissions] = React.useState<Submission[]>([]);
  const [sendEntriesModalOpen, setSendEntriesModalOpen] = React.useState(false);
  const [selectedReviewer, setSelectedReviewer] = React.useState<{ id: string; name: string; email: string; } | undefined>();
  const [selectedReviewerRole, setSelectedReviewerRole] = React.useState<string | undefined>();
  const { message } = App.useApp();

  const fetchPlacementDetails = async () => {
    if (!placementId) return Promise.resolve();
    try {
      setLoading(true);
      const [placementResponse, traineeResponse] = await Promise.all([
        fetch(`/api/placements/${placementId}`),
        fetch('/api/users/me')
      ]);
      
      if (!placementResponse.ok || !traineeResponse.ok) {
        throw new Error('Failed to fetch placement details or trainee info');
      }
      
      const [placementData, traineeData] = await Promise.all([
        placementResponse.json(),
        traineeResponse.json()
      ]);
      
      // Combine placement data with trainee's mentor
      const combinedData = {
        ...placementData,
        mentor: traineeData.mentor
      };
      
      setPlacement(combinedData);
    } catch (error) {
      console.error('Error fetching placement details:', error);
      message.error('Failed to load placement details');
    } finally {
      setLoading(false);
    }
  };

  const fetchSubmissions = async () => {
    if (!placementId) return;
    try {
      setSubmissionsLoading(true);
      setSubmissions([]); // Clear previous submissions while loading
      const response = await fetch(`/api/submissions?placementId=${placementId}`);
      if (!response.ok) throw new Error('Failed to fetch submissions');
      const data = await response.json();
      setSubmissions(data.submissions.edges);
    } catch (error) {
      console.error('Error fetching submissions:', error);
      message.error('Failed to load submissions');
    } finally {
      setSubmissionsLoading(false);
    }
  };

  React.useEffect(() => {
    if (placementId) {
      fetchPlacementDetails();
      fetchSubmissions();
    } else {
      setSubmissions([]);
      setPlacement(null);
    }
  }, [placementId]);

  React.useEffect(() => {
    if (shouldRefresh && placementId) {
      fetchPlacementDetails().then(() => {
        onRefreshComplete?.();
      });
    }
  }, [shouldRefresh, placementId, onRefreshComplete]);

  if (!placementId) {
    return (
      <div className="h-full flex items-center justify-center">
        <Empty description="Select a placement to view details" />
      </div>
    );
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <Card loading={true}>
          <div style={{ height: '200px' }} />
        </Card>
      </div>
    );
  }

  if (!placement) {
    return (
      <div className="h-full flex items-center justify-center">
        <Empty description="Placement not found" />
      </div>
    );
  }

  const handleSendEntries = (reviewer: { id: string; name: string; email: string; } | undefined, reviewerRole: string) => {
    if (!reviewer?.id || !reviewer?.name || !reviewer?.email) return;
    setSelectedReviewer(reviewer);
    setSelectedReviewerRole(reviewerRole);
    setSendEntriesModalOpen(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start mb-6">
        <Button
          type="primary"
          icon={<EditOutlined />}
          onClick={() => onEdit(placement)}
          className="bg-orange-500 hover:bg-orange-600"
        >
          Edit Placement
        </Button>
      </div>

      <Row gutter={24}>
        <Col span={12}>
          <Card title="Basic Information">
            <List>
              <List.Item>
                <List.Item.Meta
                  title="Organisation Name"
                  description={placement.client?.name || 'No Client'}
                />
              </List.Item>
              <List.Item>
                <List.Item.Meta
                  title="Duration"
                  description={`${dayjs(placement.startDate).format('DD/MM/YYYY')} - ${
                    placement.endDate ? dayjs(placement.endDate).format('DD/MM/YYYY') : 'Present'
                  }`}
                />
              </List.Item>
              <List.Item>
                <List.Item.Meta
                  title="Organisation SRA Number (if applicable)"
                  description={placement.orgSraNumber || ''}
                />
              </List.Item>
            </List>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="Additional Information">
            <List>
              <List.Item>
                <List.Item.Meta
                  title="Schedule"
                  description={placement.isFullTime ? 'Full Time' : 'Part Time'}
                />
              </List.Item>
            </List>
          </Card>
        </Col>
      </Row>

      <Card title="Supervisors & Mentors" className="mb-6!">
        <List>
          {placement.supervisor && (
            <List.Item
              actions={[
                <Button
                  key="send"
                  type="primary"
                  icon={<SendOutlined />}
                  onClick={() => handleSendEntries({
                    id: placement.supervisor!.id,
                    name: placement.supervisor!.name,
                    email: placement.supervisor!.email
                  }, 'supervisor')}
                  className="bg-orange-500 hover:bg-orange-600"
                >
                  Send Entries
                </Button>,
              ]}
            >
              <List.Item.Meta
                avatar={<Avatar icon={<UserOutlined />} src={null} size="large" />}
                title={placement.supervisor.name}
                description={
                  <div>
                    <div>{placement.supervisor.email}</div>
                    <Tag color="blue">Supervisor</Tag>
                  </div>
                }
              />
            </List.Item>
          )}
          {placement.mentor && (
            <List.Item
              actions={[
                <Button
                  key="send"
                  type="primary"
                  icon={<SendOutlined />}
                  onClick={() => handleSendEntries({
                    id: placement.mentor!.id,
                    name: placement.mentor!.name,
                    email: placement.mentor!.email
                  }, 'mentor')}
                  className="bg-orange-500 hover:bg-orange-600"
                >
                  Send Entries
                </Button>,
              ]}
            >
              <List.Item.Meta
                avatar={<Avatar icon={<UserOutlined />} src={null} size="large" />}
                title={placement.mentor.name}
                description={
                  <div>
                    <div>{placement.mentor.email}</div>
                    <Tag color="green">Mentor</Tag>
                  </div>
                }
              />
            </List.Item>
          )}
        </List>
      </Card>

      <Card 
        title={
          <div className="flex items-center justify-between">
            <span>Signoff Requests</span>
            <Text type="secondary" className="text-sm">
              {submissions.length} {submissions.length === 1 ? 'request' : 'requests'}
            </Text>
          </div>
        }
        loading={submissionsLoading}
      >
        {submissions.length === 0 ? (
          <Empty description="No signoff requests yet" />
        ) : (
          <Row gutter={[16, 16]}>
            {submissions.map((submission) => (
              <Col key={submission.id} xs={24} sm={12} md={12} lg={8}>
                <SignoffRequestCard submission={submission} />
              </Col>
            ))}
          </Row>
        )}
      </Card>

      <SendEntriesModal
        open={sendEntriesModalOpen}
        onClose={() => {
          setSendEntriesModalOpen(false);
          setSelectedReviewer(undefined);
        }}
        placement={placement}
        reviewer={selectedReviewer}
        onSuccess={fetchSubmissions}
        reviewerRole={selectedReviewerRole}
      />
    </div>
  );
};

export default PlacementDetails; 