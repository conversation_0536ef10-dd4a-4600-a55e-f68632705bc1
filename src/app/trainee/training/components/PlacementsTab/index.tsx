'use client';

import React from 'react';
import { Row, Col, App, Typography } from 'antd';
import PlacementList from './PlacementList';
import PlacementDetails from './PlacementDetails';
import PlacementFormModal from '@/components/placement/PlacementFormModal';
import type { Placement } from '@/types';
import { useSession } from 'next-auth/react';

const { Paragraph } = Typography;

const PlacementsTab = ({ qualificationRoute: propQualificationRoute }: { qualificationRoute?: string }) => {
  const { data: session } = useSession();
  const { message } = App.useApp();
  const [selectedPlacement, setSelectedPlacement] = React.useState<string | null>(null);
  const [isFormVisible, setIsFormVisible] = React.useState(false);
  const [editingPlacement, setEditingPlacement] = React.useState<Placement | undefined>(undefined);
  const [shouldRefreshList, setShouldRefreshList] = React.useState(false);
  const [loading, setLoading] = React.useState(false);

  const qualificationRoute = (propQualificationRoute || session?.user?.qualificationRoute || '').toUpperCase();

  const getSubTitle = () => {
    if (qualificationRoute === 'TC') {
      return 'Please add details of each of your seats during your period of recognised training.';
    }
    return (
      <span>
        Please add details of each of the placements you would like to use towards your two years&apos; QWE. <strong style={{ color: 'red' }}>
          Remember, you can&apos;t have more than four different organisations.
        </strong>
      </span>
    );
  };

  const handleSubmit = async (values: Partial<Placement>) => {
    if (!session?.user?.id) return;
    setLoading(true);
    try {
      const url = `/api/placements/${editingPlacement?.id ? editingPlacement.id : ''}`;
      const method = editingPlacement?.id ? 'PATCH' : 'POST';
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...values,
          userId: session.user.id,
          ...(editingPlacement?.id && { id: editingPlacement.id })
        }),
      });
      const data = await response.json();
      if (!response.ok) {
        if (data.code === 'SUPERVISOR_CLIENT_MISMATCH') {
          message.error({
            content: (
              <div>
                <div className="font-medium">Your supervisor already exists in the platform, please contact admin</div>
              </div>
            ),
            duration: 10,
            className: 'custom-error-message'
          });
          throw new Error(data.error);
        }
        throw new Error(data.error || 'Failed to save placement');
      }
      message.success(`Placement ${editingPlacement?.id ? 'updated' : 'created'} successfully`);
      setShouldRefreshList(true);
      setIsFormVisible(false);
      setEditingPlacement(undefined);
    } catch (error) {
      console.error('Error saving placement:', error);
      if (!(error instanceof Error && error.message.includes('already registered'))) {
        message.error({
          content: error instanceof Error ? error.message : 'Failed to save placement',
          duration: 5
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleAddPlacement = () => {
    setEditingPlacement(undefined);
    setIsFormVisible(true);
  };

  const handleEditPlacement = (placement: Placement) => {
    setEditingPlacement(placement);
    setIsFormVisible(true);
  };

  return (
    <>
      <div className="mb-4">
        <Paragraph type="secondary">{getSubTitle()}</Paragraph>
      </div>
      <Row gutter={24}>
        <Col xs={24} lg={8}>
          <PlacementList 
            selectedPlacement={selectedPlacement}
            onPlacementSelect={setSelectedPlacement}
            onAddPlacement={handleAddPlacement}
            shouldRefresh={shouldRefreshList}
            onRefreshComplete={() => setShouldRefreshList(false)}
          />
        </Col>
        <Col xs={24} lg={16}>
          <PlacementDetails 
            placementId={selectedPlacement} 
            onEdit={handleEditPlacement}
            shouldRefresh={shouldRefreshList}
            onRefreshComplete={() => setShouldRefreshList(false)}
          />
        </Col>
      </Row>
      <PlacementFormModal
        open={isFormVisible}
        onCancel={() => {
          setIsFormVisible(false);
          setEditingPlacement(undefined);
        }}
        onSubmit={handleSubmit}
        placement={editingPlacement}
        loading={loading}
      />
    </>
  );
};

export default PlacementsTab; 