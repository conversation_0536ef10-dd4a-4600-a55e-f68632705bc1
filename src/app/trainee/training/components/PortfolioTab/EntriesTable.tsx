'use client';

import React from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Typography,
  App,
  Tooltip,
} from 'antd';
import {
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import dayjs from 'dayjs';
import { Entry, EntryStatus } from '@/types';
import { ViewDocument } from '@/components/ViewDocument';

const { Text } = Typography;

const EntriesTable: React.FC = () => {
  const [entries, setEntries] = React.useState<Entry[]>([]);
  const [loading, setLoading] = React.useState(true);
  const router = useRouter();
  const { message, modal } = App.useApp();

  React.useEffect(() => {
    fetchEntries();
  }, []);

  const fetchEntries = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/entries');
      if (!response.ok) throw new Error('Failed to fetch entries');
      const data = await response.json();
      setEntries(data);
    } catch (error) {
      message.error('Failed to load entries');
      console.error('Error fetching entries:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    modal.confirm({
      title: 'Delete Entry',
      content: 'Are you sure you want to delete this entry?',
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      async onOk() {
        try {
          const response = await fetch(`/api/entries/${id}`, {
            method: 'DELETE',
          });
          if (!response.ok) throw new Error('Failed to delete entry');
          message.success('Entry deleted successfully');
          fetchEntries();
        } catch (error) {
          message.error('Failed to delete entry');
          console.error('Error deleting entry:', error);
        }
      },
    });
  };

  const getStatusTag = (status: EntryStatus) => {
    switch (status) {
      case 'draft':
        return <Tag color="default">Draft</Tag>;
      case 'submitted':
        return <Tag color="processing">Submitted</Tag>;
      case 'signedoff':
        return <Tag color="success">Signed Off</Tag>;
      case 'rejected':
        return <Tag color="error">Rejected</Tag>;
      default:
        return <Tag color="default">{status}</Tag>;
    }
  };

  const columns = [
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      minWidth: 180,
      render: (text: string, record: Entry) => (
        <Space direction="vertical" size={0}>
          <Text strong>{text}</Text>
          <Text type="secondary">{record.placement?.name}</Text>
        </Space>
      ),
    },
    {
      title: 'Date Range',
      key: 'dateRange',
      minWidth: 120,
      render: (_: unknown, record: Entry) => (
        <Space direction="vertical" size={0}>
          <Text>{dayjs(record.startDate).format('DD/MM/YYYY')}</Text>
          <Text>{dayjs(record.endDate).format('DD/MM/YYYY')}</Text>
        </Space>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: EntryStatus) => getStatusTag(status),
    },
    {
      title: 'Feedback',
      dataIndex: 'feedback',
      key: 'feedback',
      render: (feedback: string) => (
        <Tooltip title={feedback}>
          <Text className="truncate max-w-[200px]">{feedback}</Text>
        </Tooltip>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: unknown, record: Entry) => (
        <Space>
          <Tooltip title="View">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => router.push(`/trainee/entries/${record.id}?returnTo=/trainee/training?tab=portfolio`)}
            />
          </Tooltip>
          {(record.status === 'draft' || record.status === 'submitted') && (
            <Tooltip title="Edit">
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => router.push(`/trainee/entries/${record.id}/edit?returnTo=/trainee/training?tab=portfolio`)}
              />
            </Tooltip>
          )}
          {record.documentKey && (
            <ViewDocument documentKey={record.documentKey} />
          )}
          {record.status === 'draft' && (
            <Tooltip title="Delete">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDelete(record.id)}
              />
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  return (
    <Table
      columns={columns}
      dataSource={entries}
      loading={loading}
      rowKey="id"
      scroll={{ x: '1000' }}
      pagination={{
        pageSize: 10,
        showSizeChanger: true,
        showTotal: (total) => `Total ${total} entries`,
      }}
    />
  );
};

export default EntriesTable; 