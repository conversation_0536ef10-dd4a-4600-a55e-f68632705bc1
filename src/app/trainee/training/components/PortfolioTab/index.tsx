'use client';

import React from 'react';
import { Card, Input, Button, Space } from 'antd';
import { SearchOutlined, PlusOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import EntriesTable from './EntriesTable';

const { Search } = Input;

const PortfolioTab = () => {
  const router = useRouter();

  const handleAddEntry = () => {
    router.push('/trainee/entries/new?returnTo=/trainee/training?tab=portfolio');
  };

  return (
    <div>
      <Card className="mb-6">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <Space size="middle" className="w-full md:w-auto">
            <Search
              placeholder="Search entries..."
              allowClear
              enterButton={<SearchOutlined />}
              className="max-w-md"
            />
          </Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            className="bg-orange-500 hover:bg-orange-600"
            onClick={handleAddEntry}
          >
            Add Entry
          </Button>
        </div>
      </Card>

      <EntriesTable />
    </div>
  );
};

export default PortfolioTab; 