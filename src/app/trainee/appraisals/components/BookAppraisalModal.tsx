'use client';

import { useState } from 'react';
import { Modal, Form, DatePicker, Typography, App } from 'antd';

import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';
import { User } from '@/types';

const { Text } = Typography;

interface BookAppraisalModalProps {
  open: boolean;
  mentor: User;
  onCancel: () => void;
  onSuccess: () => void;
}

export default function BookAppraisalModal({
  open,
  mentor,
  onCancel,
  onSuccess,
}: BookAppraisalModalProps) {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const { message } = App.useApp();
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const proposedTime = values.datetime.toDate();
      
      // Check if the proposed time is in the future
      if (proposedTime <= new Date()) {
        message.error('Please select a future date and time');
        return;
      }

      setLoading(true);

      const response = await fetch('/api/meetings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mentorId: mentor.id,
          proposedTime: proposedTime.toISOString(),
          type: 'APPRAISAL',
          reviewerRole: mentor.role,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to book appraisal');
      }

      message.success('Appraisal request sent successfully');
      onSuccess();
      form.resetFields();
    } catch (error) {
      console.error('Error booking appraisal:', error);
      message.error(error instanceof Error ? error.message : 'Failed to book appraisal');
    } finally {
      setLoading(false);
    }
  };

  const disabledDate = (current: Dayjs) => {
    // Disable dates before today
    return current && current < dayjs().startOf('day');
  };

  const disabledTime = (current: Dayjs) => {
    if (current && current.isSame(dayjs(), 'day')) {
      // For today, disable past times
      const currentHour = dayjs().hour();
      const currentMinute = dayjs().minute();
      
      return {
        disabledHours: () => Array.from({ length: currentHour }, (_, i) => i),
        disabledMinutes: (selectedHour: number) => {
          if (selectedHour === currentHour) {
            return Array.from({ length: currentMinute }, (_, i) => i);
          }
          return [];
        },
      };
    }
    return {};
  };

  const isTrainingPrincipal = mentor.role === 'MENTOR' && mentor.isTrainingPrincipal;

  return (
    <Modal
      title={`Book an Appraisal with ${isTrainingPrincipal ? 'Training Principal' : 'Supervisor'}`}
      open={open}
      onCancel={onCancel}
      onOk={handleSubmit}
      okText="Send Appraisal Request"
      confirmLoading={loading}
    >
      <div className="mb-4">
        <Text>Booking appraisal with {mentor.name}</Text>
        <Text className="block text-gray-500 mt-2">
          {isTrainingPrincipal 
            ? 'This appraisal will be with your Training Principal to discuss your overall progress and development.'
            : 'This appraisal will be with your Supervisor to discuss your work and progress during your placement.'}
        </Text>
      </div>
      <Form form={form} layout="vertical">
        <Form.Item
          name="datetime"
          label="Please propose a date & time (Europe/London)"
          rules={[
            { required: true, message: 'Please select a date and time' },
            {
              validator: async (_, value) => {
                if (value && value.toDate() <= new Date()) {
                  throw new Error('Please select a future date and time');
                }
              },
            },
          ]}
        >
          <DatePicker
            showTime
            format="DD/MM/YYYY HH:mm"
            disabledDate={disabledDate}
            disabledTime={disabledTime}
            className="w-full"
            minuteStep={5}
          />
        </Form.Item>
        <div className="mt-4">
          <p>*Download appraisal form <a href="https://pathways-dev-uploads.s3.eu-west-1.amazonaws.com/manualUploads/appraisal_form.docx" target="_blank">here</a></p>
          <p>*Download end of training contract form <a href="https://pathways-dev-uploads.s3.eu-west-1.amazonaws.com/manualUploads/tc_checklist.docx" target="_blank">here</a></p>
        </div>
      </Form>
    </Modal>
  );
}
