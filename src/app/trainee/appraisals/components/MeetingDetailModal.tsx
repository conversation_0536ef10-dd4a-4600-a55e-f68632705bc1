import { Modal, Descriptions, Tag, Button } from 'antd';
import { Meeting, MeetingFormat, MeetingType } from '@/types';
import dayjs from 'dayjs';
import { VideoCameraOutlined } from '@ant-design/icons';
import { forwardRef, useImperativeHandle, useState, useMemo } from 'react';

const MeetingTypeLabel = {
  [MeetingType.MONTHLY]: "Monthly Review",
  [MeetingType.TPM]: "TPM",
  [MeetingType.APPRAISAL]: "Appraisal",
}

export interface MeetingDetailModalRef {
  open: (meeting: Meeting) => void;
}

interface MeetingDetailModalProps {
  onClose?: () => void;
}

const MeetingDetailModal = forwardRef<MeetingDetailModalRef, MeetingDetailModalProps>(({ onClose }, ref) => {
  const [selectedMeeting, setSelectedMeeting] = useState<Meeting | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return "gold";
      case 'ACCEPTED':
        return "green";
      case 'DECLINED':
        return "red";
      case 'COMPLETED':
        return "blue";
      default:
        return "default";
    }
  };

  const selectedMeetingUrl = useMemo(() => {
    if (!selectedMeeting) return undefined;
    if (selectedMeeting?.meetingUrl) return selectedMeeting?.meetingUrl;
    if (selectedMeeting?.availability?.meetingMethod !== MeetingFormat.VIRTUAL) return undefined;
    const meetingId = selectedMeeting?.id;
    if (!meetingId) return undefined;
    return `${window.location.origin}/meeting/${meetingId}`;
  }, [selectedMeeting]);

  const openMeetingUrl = (meetingUrl: string) => {
    window.open(meetingUrl, '_blank');
  };

  useImperativeHandle(ref, () => ({
    open: (meeting: Meeting) => {
      setSelectedMeeting(meeting);
      setIsOpen(true);
    }
  }));

  const handleClose = () => {
    setIsOpen(false);
    setSelectedMeeting(null);
    onClose?.();
  };

  return (
    <Modal
      title="Meeting Information"
      open={isOpen}
      onCancel={handleClose}
      footer={[
        <Button
          key="close"
          onClick={handleClose}
        >
          Close
        </Button>,
        !!selectedMeetingUrl && selectedMeeting?.availability?.meetingMethod === MeetingFormat.VIRTUAL && (
          <Button
            key="join"
            type="primary"
            icon={<VideoCameraOutlined />}
            onClick={() => openMeetingUrl(selectedMeetingUrl)}
          >
            Join Meeting
          </Button>
        ),
      ].filter(Boolean)}
    >
      {selectedMeeting && (
        <Descriptions column={1} style={{ marginTop: 30 }}>
          <Descriptions.Item label="Type">
            {MeetingTypeLabel[selectedMeeting.type]}
          </Descriptions.Item>
          <Descriptions.Item label="Date & Time">
            {dayjs(selectedMeeting.proposedTime).format("DD/MM/YYYY h:mm A")}
          </Descriptions.Item>
          <Descriptions.Item label="Status">
            <Tag color={getStatusColor(selectedMeeting.status)}>{selectedMeeting.status}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label={selectedMeeting.type === MeetingType.APPRAISAL ? "Training Principal" : "Mentor"}>
            {selectedMeeting.mentor.name}
          </Descriptions.Item>
          <Descriptions.Item label="Meeting Method">
            {selectedMeeting.availability?.meetingMethod === MeetingFormat.VIRTUAL ? "Virtual" : "In-Person"}
          </Descriptions.Item>
          {!!selectedMeeting.meetingLocation && (
            <Descriptions.Item label="Location">
              {selectedMeeting.meetingLocation}
            </Descriptions.Item>
          )}
          {!!selectedMeetingUrl && selectedMeeting?.availability?.meetingMethod === MeetingFormat.VIRTUAL && (
            <Descriptions.Item label="Meeting URL">
              <a href={selectedMeetingUrl} target="_blank" rel="noopener noreferrer">
                {selectedMeetingUrl}
              </a>
            </Descriptions.Item>
          )}
        </Descriptions>
      )}
    </Modal>
  );
});

MeetingDetailModal.displayName = 'MeetingDetailModal';

export default MeetingDetailModal; 