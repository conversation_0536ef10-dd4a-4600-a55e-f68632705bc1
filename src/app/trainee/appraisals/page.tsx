"use client";

import { useEffect, useState, useCallback, useRef } from "react";
import { <PERSON>, Typo<PERSON>, Button, Table, Tag, Tabs, App } from "antd";
import { useSession } from "next-auth/react";
import { Meeting, User } from "@/types";
import dayjs from "dayjs";
import BookAppraisalModal from "./components/BookAppraisalModal";
import MeetingDetailModal, {
  MeetingDetailModalRef,
} from "./components/MeetingDetailModal";

const { Title } = Typography;
const { TabPane } = Tabs;

export default function AppraisalsPage() {
  const { message } = App.useApp();
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedMentor, setSelectedMentor] = useState<User | null>(null);
  const [bookModalOpen, setBookModalOpen] = useState(false);
  const { data: session } = useSession();
  const [mentors, setMentors] = useState<User[]>([]);
  const meetingDetailModalRef = useRef<MeetingDetailModalRef>(null);

  const fetchMentorsAndSupervisors = useCallback(async () => {
    try {
      const response = await fetch("/api/users?forAppraisals=true");
      if (!response.ok)
        throw new Error("Failed to fetch mentors and supervisors");
      const data = await response.json();
      setMentors(data);
    } catch (error) {
      console.error("Error fetching mentors and supervisors:", error);
      message.error("Failed to load mentors and supervisors");
    }
  }, [message]);

  const fetchMeetings = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/meetings?type=APPRAISAL");
      if (!response.ok) throw new Error("Failed to fetch meetings");
      const data = await response.json();
      setMeetings(data);
    } catch (error) {
      console.error("Error fetching meetings:", error);
      message.error("Failed to load meetings");
    } finally {
      setLoading(false);
    }
  }, [message]);

  useEffect(() => {
    if (session?.user) {
      fetchMentorsAndSupervisors();
      fetchMeetings();
    }
  }, [session, fetchMentorsAndSupervisors, fetchMeetings]);

  const handleBook = (mentor: User) => {
    setSelectedMentor(mentor);
    setBookModalOpen(true);
  };

  const handleBookingSuccess = () => {
    setBookModalOpen(false);
    setSelectedMentor(null);
    fetchMeetings();
    message.success("Appraisal booked successfully");
  };

  const handleViewDetails = (meeting: Meeting) => {
    meetingDetailModalRef.current?.open(meeting);
  };

  const trainingPrincipals = mentors.filter(
    (user) => user.role === "MENTOR" && user.isTrainingPrincipal
  );
  const supervisors = mentors.filter((user) => user.role === "SUPERVISOR");

  const columns = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
    },
    {
      title: "Actions",
      key: "actions",
      render: (_: unknown, record: User) => (
        <Button type="primary" onClick={() => handleBook(record)}>
          Book an Appraisal
        </Button>
      ),
    },
  ];

  const meetingColumns = [
    {
      title: "Reviewer",
      dataIndex: ["mentor", "name"],
      key: "mentorName",
      render: (name: string, record: Meeting) =>
        `${name} (${
          record.mentor.role === "MENTOR" ? "Training Principal" : "Supervisor"
        })`,
    },
    {
      title: "Date & Time",
      dataIndex: "proposedTime",
      key: "proposedTime",
      render: (date: string) => dayjs(date).format("DD/MM/YYYY HH:mm"),
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      render: (status: string) => (
        <Tag
          color={
            status === "PENDING"
              ? "gold"
              : status === "ACCEPTED"
              ? "green"
              : status === "DECLINED"
              ? "red"
              : "blue"
          }
        >
          {status}
        </Tag>
      ),
    },
    {
      title: "Actions",
      key: "actions",
      render: (_: unknown, record: Meeting) => (
        <Button type="link" onClick={() => handleViewDetails(record)}>
          View Details
        </Button>
      ),
    },
  ];

  return (
    <div className="space-y-6 p-6">
      <Title level={2}>Appraisals</Title>
      <p>
        Approximately every six months, you need to schedule an appraisal with
        your supervisor and your training principal to discuss your development
        and progress to date. You will need to book separate appraisal meetings
        with both your supervisor and training principal. Once you have a time
        that works for you, click the &apos;Book an appraisal&apos; button to
        schedule the meeting. Remember to circulate your draft appraisal form to
        both of them a couple of days before your appraisal meeting.
      </p>

      <Card className="mb-6!">
        <Table
          dataSource={trainingPrincipals}
          columns={columns}
          rowKey="id"
          pagination={false}
        />
      </Card>
      <Card title="Scheduled Appraisals">
        <Table
          dataSource={meetings}
          columns={meetingColumns}
          rowKey="id"
          loading={loading}
        />
      </Card>

      {selectedMentor && (
        <BookAppraisalModal
          open={bookModalOpen}
          mentor={selectedMentor}
          onCancel={() => {
            setBookModalOpen(false);
            setSelectedMentor(null);
          }}
          onSuccess={handleBookingSuccess}
        />
      )}

      <MeetingDetailModal ref={meetingDetailModalRef} />
    </div>
  );
}
