'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { UserRole } from '@/types';
import SubmissionsList from '@/components/submissions/SubmissionsList';

export default function TraineeSubmissionsPage() {
  const router = useRouter();
  const { data: session, status } = useSession();

  useEffect(() => {
    // Redirect if not authenticated or not a trainee
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
      return;
    }

    if (session?.user?.role !== UserRole.TRAINEE) {
      router.push('/trainee/trainee-dashboard');
      return;
    }
  }, [session, status, router]);

  return (
    <SubmissionsList 
      role={UserRole.TRAINEE}
      title="My Submissions"
      showCreateButton={true}
      showWithdrawButton={true}
    />
  );
}
