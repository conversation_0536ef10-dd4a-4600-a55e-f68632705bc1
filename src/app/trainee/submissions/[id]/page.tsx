'use client';

import { useSession } from 'next-auth/react';
import { useRouter, useParams } from 'next/navigation';
import { useEffect } from 'react';
import { UserRole } from '@/types';
import SubmissionDetail from '@/components/submissions/SubmissionDetail';

export default function TraineeSubmissionDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session, status } = useSession();

  useEffect(() => {
    // Redirect if not authenticated or not a trainee
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
      return;
    }

    if (session?.user?.role !== UserRole.TRAINEE) {
      router.push('/');
      return;
    }
  }, [session, status, router]);

  return (
    <SubmissionDetail 
      role={UserRole.TRAINEE}
      submissionId={params.id as string}
      showWithdrawButton={true}
    />
  );
}
