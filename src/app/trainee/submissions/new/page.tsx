'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Breadcrumb,
  Spin,
  Form,
  Button,
  Select,
  DatePicker,
  Table,
  Tag,
  Space,
  App,
  Badge,
  Tooltip
} from 'antd';
import {
  HomeOutlined,
  FileTextOutlined,
  SendOutlined,
} from '@ant-design/icons';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { UserRole, Entry, Placement } from '@/types';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface Reviewer {
  id: string;
  name: string;
  email: string;
  role: UserRole; // Changed from string to UserRole enum
}

export default function NewSubmissionPage() {
  const [form] = Form.useForm();
  const [entries, setEntries] = useState<Entry[]>([]);
  const [reviewers, setReviewers] = useState<Reviewer[]>([]);
  const [selectedEntries, setSelectedEntries] = useState<string[]>([]);
  const [selectedPlacement, setSelectedPlacement] = useState<string | null>(null);
  const [placements, setPlacements] = useState<{id: string, name: string}[]>([]); // Changed position to name
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const router = useRouter();
  const { data: session, status } = useSession();
  const { message } = App.useApp();
  useEffect(() => {
    // Redirect if not authenticated or not a trainee
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
      return;
    }

    if (session?.user?.role !== UserRole.TRAINEE) {
      router.push('/');
      return;
    }

    fetchPlacements();
    fetchEntries();
  }, [session, status, router]);

  // Fetch all placements for the trainee
  const fetchPlacements = async () => {
    try {
      const response = await fetch('/api/placements/trainee');
      if (!response.ok) throw new Error('Failed to fetch placements');
      const data = await response.json();
      setPlacements(data.map((p: Placement) => ({ id: p.id, name: p.name })));
    } catch (error) {
      message.error('Failed to load placements');
      console.error('Error fetching placements:', error);
    }
  };

  // Fetch all draft entries for the trainee
  const fetchEntries = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/entries?status=draft');
      if (!response.ok) throw new Error('Failed to fetch entries');
      const data = await response.json();
      setEntries(data);
    } catch (error) {
      message.error('Failed to load draft entries');
      console.error('Error fetching entries:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch reviewers based on selected placement
  const fetchReviewers = async (placementId: string) => {
    try {
      const response = await fetch(`/api/placements/${placementId}/reviewers`);
      if (!response.ok) throw new Error('Failed to fetch reviewers');
      const data = await response.json();
      setReviewers(data);

      // Reset the reviewer selection in the form
      form.setFieldsValue({ reviewerId: undefined });
    } catch (error) {
      message.error('Failed to load reviewers');
      console.error('Error fetching reviewers:', error);
    }
  };

  // Handle placement selection change
  const handlePlacementChange = (placementId: string) => {
    setSelectedPlacement(placementId);
    setSelectedEntries([]);
    fetchReviewers(placementId);
  };

  const handleSubmit = async (values: {
    placementId: string;
    dateRange: [dayjs.Dayjs, dayjs.Dayjs];
    reviewerId: string;
  }) => {
    if (!selectedPlacement) {
      message.error('Please select a placement');
      return;
    }

    if (selectedEntries.length === 0) {
      message.error('Please select at least one entry');
      return;
    }

    // Verify all selected entries belong to the selected placement
    const invalidEntries = selectedEntries.filter(entryId => {
      const entry = entries.find(e => e.id === entryId);
      return !entry || entry.placement.id !== selectedPlacement;
    });

    if (invalidEntries.length > 0) {
      message.error('All entries must belong to the selected placement');
      return;
    }

    try {
      setSubmitting(true);

      // Extract date range
      const dateRange = values.dateRange || [dayjs(), dayjs()];
      const [startDate, endDate] = dateRange;

      const response = await fetch('/api/submissions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          entryIds: selectedEntries,
          placementId: values.placementId,
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          reviewerId: values.reviewerId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create submission');
      }

      message.success('Submission created successfully');
      router.push('/trainee/training');
    } catch (error) {
      message.error(error instanceof Error ? error.message : 'Failed to create submission');
    } finally {
      setSubmitting(false);
    }
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'draft':
        return <Tag color="default">Draft</Tag>;
      case 'submitted':
        return <Tag color="processing">Submitted</Tag>;
      case 'signedoff':
        return <Tag color="success">Signed Off</Tag>;
      case 'rejected':
        return <Tag color="error">Rejected</Tag>;
      default:
        return <Tag color="default">{status}</Tag>;
    }
  };

  const rowSelection = {
    selectedRowKeys: selectedEntries,
    onChange: (selectedRowKeys: React.Key[]) => {
      setSelectedEntries(selectedRowKeys as string[]);
    },
    getCheckboxProps: (record: Entry) => ({
      disabled: record.status !== 'draft',
      name: record.title,
    }),
  };

  const columns = [
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      render: (text: string, record: Entry) => (
        <Space direction="vertical" size={0}>
          <Text strong>{text}</Text>
          <Text type="secondary">{record.placement?.name}</Text>
        </Space>
      ),
    },
    {
      title: 'Date Range',
      key: 'dateRange',
      render: (_: unknown, record: Entry) => (
        <Space direction="vertical" size={0}>
          <Text>{dayjs(record.startDate).format('DD/MM/YYYY')}</Text>
          <Text>{dayjs(record.endDate).format('DD/MM/YYYY')}</Text>
        </Space>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: 'Skills',
      key: 'skills',
      width: 120,
      render: (_: unknown, record: Entry) => {
        const skillCount = record.entrySubSkills?.length || 0;
        
        if (skillCount === 0) {
          return <Text type="secondary">None</Text>;
        }

        const tooltipContent = (
          <div style={{ 
            maxWidth: '300px',
            maxHeight: '300px', 
            overflow: 'auto',
            padding: '8px 4px',
            fontSize: '14px',
          }}>
            <div style={{ 
              marginBottom: '8px', 
              fontWeight: 500,
              borderBottom: '1px solid rgba(255,255,255,0.2)',
              paddingBottom: '4px'
            }}>
              Skills ({skillCount})
            </div>
            {record.entrySubSkills?.map((skill) => (
              <div key={skill.subSkill.id} style={{ 
                padding: '6px 8px',
                margin: '4px 0',
                backgroundColor: 'rgba(255,255,255,0.1)',
                borderRadius: '4px',
                display: 'flex',
                alignItems: 'center',
                gap: '6px'
              }}>
                <div style={{ 
                  width: '6px', 
                  height: '6px', 
                  backgroundColor: '#1890ff',
                  borderRadius: '50%' 
                }} />
                {skill.subSkill.name}
              </div>
            ))}
          </div>
        );

        return (
          <Tooltip 
            title={tooltipContent} 
            overlayStyle={{ maxWidth: 'none' }}
            overlayInnerStyle={{ 
              borderRadius: '6px',
              padding: '12px'
            }}
          >
            <Badge 
              count={skillCount} 
              style={{ backgroundColor: '#1890ff' }}
              overflowCount={99}
            >
              <Tag color="blue">Skills</Tag>
            </Badge>
          </Tooltip>
        );
      },
    },
  ];

  return (
    <div className="space-y-6 p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link href="/">
                <HomeOutlined /> Home
              </Link>
            ),
          },
          {
            title: (
              <Link href="/trainee/submissions">
                <FileTextOutlined /> Submissions
              </Link>
            ),
          },
          {
            title: (
              <>
                <SendOutlined /> New Submission
              </>
            ),
          },
        ]}
      />

      <Title level={2}>Create New Submission</Title>

      <Spin spinning={loading}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            dateRange: [dayjs(), dayjs()],
          }}
        >
          <Card title="Submission Details" className="mb-6">
            <div className="mb-4">
              <Text type="secondary">
                Please select a placement first. You can only submit entries from the same placement to either the supervisor of that placement or your mentor.
              </Text>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Form.Item
                name="placementId"
                label="Placement"
                rules={[{ required: true, message: 'Please select a placement' }]}
              >
                <Select
                  placeholder="Select a placement"
                  onChange={handlePlacementChange}
                >
                  {placements.map((placement) => (
                    <Option key={placement.id} value={placement.id}>
                      {placement.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="dateRange"
                label="Date Range"
                rules={[{ required: true, message: 'Please select a date range' }]}
              >
                <RangePicker className="w-full" />
              </Form.Item>

              <Form.Item
                name="reviewerId"
                label="Reviewer"
                rules={[{ required: true, message: 'Please select a reviewer' }]}
                dependencies={['placementId']}
              >
                <Select
                  placeholder={selectedPlacement ? "Select a reviewer" : "Please select a placement first"}
                  disabled={!selectedPlacement}
                >
                  {reviewers.map((reviewer) => (
                    <Option key={reviewer.id} value={reviewer.id}>
                      {reviewer.name} ({reviewer.role})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </div>
          </Card>

          <Card title="Select Entries" className="mb-6">
            <div className="mb-4">
              <Text>Select the entries you want to include in this submission. Only draft entries from the selected placement can be submitted.</Text>
            </div>

            {selectedPlacement ? (
              <Table
                rowSelection={rowSelection}
                columns={columns}
                dataSource={entries.filter(entry =>
                  entry.status === 'draft' &&
                  entry.placement.id === selectedPlacement
                )}
                rowKey="id"
                pagination={{ pageSize: 10 }}
                scroll={{ x: 'max-content' }}
              />
            ) : (
              <div className="text-center p-6">
                <Text type="secondary">Please select a placement first to view available entries.</Text>
              </div>
            )}
          </Card>

          <div className="flex justify-end space-x-4">
            <Button
              onClick={() => router.push('/trainee/submissions')}
            >
              Cancel
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              icon={<SendOutlined />}
              loading={submitting}
              disabled={selectedEntries.length === 0}
            >
              Submit Entries
            </Button>
          </div>
        </Form>
      </Spin>
    </div>
  );
}
