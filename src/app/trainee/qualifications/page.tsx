'use client';

import { Typography, Card } from 'antd';
import QualificationsList from '@/components/qualifications/QualificationsList';
import AddQualificationButton from '@/components/qualifications/AddQualificationButton';
import { useRef } from 'react';

const { Title, Text } = Typography;

export default function QualificationsPage() {
  const qualificationsListRef = useRef<{ fetchQualifications: () => void } | null>(null);

  const handleQualificationAdded = () => {
    qualificationsListRef.current?.fetchQualifications();
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <Title level={2}>Qualifications</Title>
          <Text type="secondary">
            Safely and securely store all your qualification documents in one convenient place.
            No more frantically running around trying to find that missing A Level certificate anymore!
          </Text>
        </div>
        <AddQualificationButton onSuccess={handleQualificationAdded} />
      </div>
      
      <Card>
        <QualificationsList ref={qualificationsListRef} />
      </Card>
    </div>
  );
} 