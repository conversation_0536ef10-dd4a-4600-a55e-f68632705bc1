'use client';

import { Typo<PERSON>, <PERSON><PERSON>, Button, Spin, Alert, Card } from 'antd';
import useCharacterSuitabilityStore from '@/store/characterSuitabilityStore';
import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';

const { Title, Text, Paragraph } = Typography;

export default function CharacterSuitabilityPage() {
  const { data: session } = useSession();
  const {
    characterSuitability,
    loading,
    error,
    fetchCharacterSuitability,
    updateCompletion,
  } = useCharacterSuitabilityStore();

  const [rulesPart1, setRulesPart1] = useState('');
  const [rulesPart2, setRulesPart2] = useState('');
  const [complete, setComplete] = useState(false);

  useEffect(() => {
    fetchCharacterSuitability();
  }, [fetchCharacterSuitability]);

  useEffect(() => {
    if (characterSuitability) {
      const userCompletion = characterSuitability.userCompletions[0];
      setComplete(userCompletion?.completed || false);

      // Split rules into parts based on "What you must do"
      const fullRules = characterSuitability.rules;
      const r = fullRules.split('|^|');

      // Default to traditional route if not specified
      const isTraditionalRoute = session?.user?.qualificationRoute !== "SQE";
      
      if (!isTraditionalRoute) {
        if (r.length > 0) {
          const index = r[0].indexOf('What you must do');
          setRulesPart1(r[0].substring(0, index));
          setRulesPart2(r[0].substring(index + 17));
        }
      } else {
        if (r.length > 1) {
          const index = r[1].indexOf('What you must do');
          setRulesPart1(r[1].substring(0, index));
          setRulesPart2(r[1].substring(index + 17));
        }
      }
    }
  }, [characterSuitability, session]);

  const handleCompletionChange = async (checked: boolean) => {
    setComplete(checked);
  };

  const handleSave = async () => {
    if (!characterSuitability) return;
    await updateCompletion(characterSuitability.id, complete);
    window.location.reload();
  };

  if (loading) return <Spin size="large" />;
  if (error) return <Alert type="error" message={error} />;
  if (!characterSuitability) return <Alert type="warning" message="No character suitability information found" />;

  return (
    <div className="max-w-4xl mx-auto p-4">
      <Title level={2}>Character and Suitability</Title>
      
      <Text className="block mb-8 text-gray-500">
        The final step to qualification is completing a character and
        suitability test.
      </Text>

      <Card className="mb-4!">
        <Title level={3}>Character and Suitability</Title>
        <div dangerouslySetInnerHTML={{ __html: rulesPart1 }} className="mb-4" />
        <Paragraph strong className="mb-4">
          What you must do
        </Paragraph>
        <div dangerouslySetInnerHTML={{ __html: rulesPart2 }} />
      </Card>

      <Card className="mb-4!">
        <Title level={3}>Information for Qualified Solicitors</Title>
        <Paragraph className="whitespace-pre-line">
          {characterSuitability.infoForQualifiedSolicitors}
        </Paragraph>
      </Card>

      <Card className="mb-4!">
        <Title level={3}>External Links and Information</Title>
        <ul className="list-none pl-0">
          {characterSuitability.externalLinks.map((link, index) => (
            <li key={index} className="my-2">
              <Link 
                href={link.url}
                target="_blank"
                rel="noreferrer"
                className="text-[#c7641b]"
              >
                {link.title || link.url}
              </Link>
              {link.description && (
                <Text type="secondary" className="ml-2">
                  {link.description}
                </Text>
              )}
            </li>
          ))}
        </ul>
      </Card>

      <Card>
        <div className="flex items-center justify-between">
          <Checkbox
            checked={complete}
            onChange={(e) => handleCompletionChange(e.target.checked)}
          >
            Tick when Character and Suitability Test successfully completed
          </Checkbox>
          <Button
            type="primary"
            onClick={handleSave}
            disabled={complete === characterSuitability.userCompletions[0]?.completed}
          >
            Save
          </Button>
        </div>
      </Card>
    </div>
  );
} 