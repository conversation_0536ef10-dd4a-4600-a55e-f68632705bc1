'use client';

import { useState, useEffect } from 'react';
import { Card, Typography, Row, Col, Spin, Empty, App } from 'antd';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { UserRole, CompetencyPlus } from '@/types';
import dayjs from 'dayjs';

const { Title, Paragraph, Text } = Typography;

export default function TraineeCompetencyPlusPage() {
  const [items, setItems] = useState<CompetencyPlus[]>([]);
  const [loading, setLoading] = useState(true);
  const [imageUrls, setImageUrls] = useState<Record<string, string>>({});
  const router = useRouter();
  const { data: session, status } = useSession();
  const { message } = App.useApp();
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
      return;
    }

    if (session && session.user?.role !== UserRole.TRAINEE) {
      router.push('/trainee/dashboard');
      return;
    }

    fetchItems();
  }, [session, status, router]);

  const fetchItems = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/competency-plus');
      if (!response.ok) throw new Error('Failed to fetch items');
      const data = await response.json();
      setItems(data);

      // Fetch signed URLs for banner images
      const urls: Record<string, string> = {};
      await Promise.all(
        data.map(async (item: CompetencyPlus) => {
          if (item.bannerImageUrl) {
            if (item.bannerImageUrl.startsWith('http')) {
              urls[item.id] = item.bannerImageUrl;
            } else {
              try {
                const response = await fetch('/api/get-signed-url?key=' + item.bannerImageUrl);
                const data = await response.json();
                urls[item.id] = data.url;
              } catch (error) {
                console.error('Error getting signed URL:', error);
              }
            }
          }
        })
      );
      setImageUrls(urls);
    } catch (error) {
      console.error('Error fetching competency plus items:', error);
      message.error('Failed to fetch competency plus items');
    } finally {
      setLoading(false);
    }
  };

  const handleItemClick = (id: string) => {
    router.push(`/trainee/competency-plus/${id}`);
  };

  if (status === 'loading' || loading) {
    return (
      <div className="p-6">
        <div className="flex justify-center items-center h-64">
          <Spin size="large" />
        </div>
      </div>
    );
  }

  if (!session || session.user?.role !== UserRole.TRAINEE) {
    return null;
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <Title level={2}>Competency Plus</Title>
        <Text type="secondary">
        {session.user?.name}, here are some exercises, journals, courses, articles and other amazing content to help you further develop your skills.
        </Text>
      </div>

      {items.length === 0 ? (
        <Empty
          description="No competency plus items available yet"
          style={{ marginTop: '50px' }}
        />
      ) : (
        <Row gutter={[24, 24]}>
          {items.map((item) => (
            <Col xs={24} sm={12} lg={8} xl={6} key={item.id}>
              <Card
                hoverable
                onClick={() => handleItemClick(item.id)}
                cover={
                  imageUrls[item.id] ? (
                    <div 
                      className="relative h-48 bg-cover bg-center flex items-end"
                      style={{ 
                        backgroundImage: `linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(0,0,0,0.7) 100%), url(${imageUrls[item.id]})`,
                      }}
                    >
                      <div className="p-4 text-white absolute bottom-0 left-0 right-0">
                        <Title level={5} className="!text-white !mb-0">
                          {item.title}
                        </Title>
                      </div>
                    </div>
                  ) : (
                    <div className="h-48 bg-gray-200 flex items-center justify-center">
                      <div className="text-center p-4">
                        <Title level={5} className="!mb-0">
                          {item.title}
                        </Title>
                      </div>
                    </div>
                  )
                }
                className="h-full"
                styles={{
                  body: {
                    padding: '16px',
                  }
                }}
              >
                <div className="h-20 overflow-hidden">
                  <Paragraph 
                    ellipsis={{ rows: 3 }}
                    className="text-gray-600 mb-2"
                  >
                    {item.shortDescription}
                  </Paragraph>
                </div>
                
                <div className="mt-4 text-right">
                  <Text type="secondary" className="text-sm">
                    {dayjs(item.publishedAt || item.createdAt).format('DD/MM/YYYY')}
                  </Text>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      )}
    </div>
  );
} 