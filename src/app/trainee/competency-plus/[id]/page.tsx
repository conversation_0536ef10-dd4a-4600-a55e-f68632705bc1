"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import {
  Card,
  Typography,
  Spin,
  Breadcrumb,
  Button,
  Space,
  Divider,
  List,
  App,
} from "antd";
import {
  ArrowLeftOutlined,
  HomeOutlined,
  PlayCircleOutlined,
  SoundOutlined,
  LinkOutlined,
} from "@ant-design/icons";
import { UserRole, CompetencyPlus } from "@/types";
import dayjs from "dayjs";
import Link from "next/link";
import ReactMarkdown from "react-markdown";

const { Title, Paragraph, Text } = Typography;

export default function CompetencyPlusDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session, status } = useSession();
  const [item, setItem] = useState<CompetencyPlus | null>(null);
  const [loading, setLoading] = useState(true);
  const [bannerImageUrl, setBannerImageUrl] = useState<string>("");
  const [audioUrl, setAudioUrl] = useState<string>("");
  const [videoUrl, setVideoUrl] = useState<string>("");
  const { message } = App.useApp();
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin");
      return;
    }

    if (
      session &&
      ![UserRole.TRAINEE, UserRole.ADMIN].includes(session.user?.role)
    ) {
      router.push("/trainee/dashboard");
      return;
    }

    if (params.id) {
      fetchItem();
    }
  }, [session, status, router, params.id]);

  const fetchItem = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/competency-plus/${params.id}`);
      if (!response.ok) throw new Error("Failed to fetch competency plus item");
      const data = await response.json();
      setItem(data);

      // Fetch signed URLs for media files
      if (data.bannerImageUrl) {
        if (data.bannerImageUrl.startsWith("http")) {
          setBannerImageUrl(data.bannerImageUrl);
        } else {
          try {
            const response = await fetch(
              "/api/get-signed-url?key=" + data.bannerImageUrl
            );
            const urlData = await response.json();
            setBannerImageUrl(urlData.url);
          } catch (error) {
            console.error("Error getting banner image URL:", error);
          }
        }
      }

      if (data.audioUrl) {
        if (data.audioUrl.startsWith("http")) {
          setAudioUrl(data.audioUrl);
        } else {
          try {
            const response = await fetch(
              "/api/get-signed-url?key=" + data.audioUrl
            );
            const urlData = await response.json();
            setAudioUrl(urlData.url);
          } catch (error) {
            console.error("Error getting audio URL:", error);
          }
        }
      }

      if (data.videoUrl) {
        if (data.videoUrl.startsWith("http")) {
          setVideoUrl(data.videoUrl);
        } else {
          try {
            const response = await fetch(
              "/api/get-signed-url?key=" + data.videoUrl
            );
            const urlData = await response.json();
            setVideoUrl(urlData.url);
          } catch (error) {
            console.error("Error getting video URL:", error);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching competency plus item:", error);
      message.error("Failed to fetch competency plus item");
      router.push("/trainee/competency-plus");
    } finally {
      setLoading(false);
    }
  };

  if (status === "loading" || loading) {
    return (
      <div className="p-6">
        <Card>
          <div className="flex justify-center items-center h-32">
            <Spin size="large" />
          </div>
        </Card>
      </div>
    );
  }

  if (
    !session ||
    ![UserRole.TRAINEE, UserRole.ADMIN].includes(session.user?.role)
  ) {
    return null;
  }

  if (!item) {
    return (
      <div className="p-6">
        <Card>
          <div className="text-center">
            <Title level={4}>Competency Plus item not found</Title>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Breadcrumb */}
      <Breadcrumb className="mb-6!">
        <Breadcrumb.Item>
          <Link href="/trainee/dashboard">
            <HomeOutlined /> Home
          </Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <Link href="/trainee/competency-plus">Competency Plus</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{item.title}</Breadcrumb.Item>
      </Breadcrumb>

      {/* Back Button */}
      <div className="mb-6">
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => router.push("/trainee/competency-plus")}
        >
          Back to Competency Plus
        </Button>
      </div>

      {/* Title */}
      <div className="mb-6">
        <Title level={1}>{item.title}</Title>
        <Text type="secondary">
          Published on{" "}
          {dayjs(item.publishedAt || item.createdAt).format("MMMM DD, YYYY")}
        </Text>
      </div>

      {/* Short Description */}
      <Paragraph className="text-lg leading-relaxed">
        {item.shortDescription}
      </Paragraph>

      {/* Banner Image */}
      {bannerImageUrl && (
        <img
          src={bannerImageUrl}
          alt={item.title}
          className="w-full h-auto rounded-lg shadow-md"
          style={{ maxHeight: "400px", objectFit: "cover" }}
        />
      )}

      {/* Content */}
      <div className="mb-6 !mt-6">
        <div className="prose prose-lg max-w-none">
          <ReactMarkdown
            components={{
              ul: ({node, ...props}) => <ul className="list-disc pl-6" {...props} />, 
              ol: ({node, ...props}) => <ol className="list-decimal pl-6" {...props} />, 
              li: ({node, ...props}) => <li className="mb-1" {...props} />
            }}
          >
            {item.content}
          </ReactMarkdown>
        </div>
      </div>

      {/* External Links */}
      {item.externalLinks && item.externalLinks.length > 0 && (
        <Card title="External Links and Information" className="mb-6">
          <List
            dataSource={item.externalLinks}
            renderItem={(link) => (
              <List.Item>
                <List.Item.Meta
                  avatar={<LinkOutlined className="text-blue-600" />}
                  title={
                    <a
                      href={link.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800"
                    >
                      {link.title}
                    </a>
                  }
                  description={
                    <div>
                      <div className="text-gray-500 text-sm mb-1">
                        {link.url}
                      </div>
                      {link.description && (
                        <div className="text-gray-600">{link.description}</div>
                      )}
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        </Card>
      )}

      {/* Audio and Video */}
      {(audioUrl || videoUrl) && (
        <Card title="Media" className="mb-6">
          <Space direction="vertical" size="large" className="w-full">
            {/* Audio Player */}
            {audioUrl && (
              <div>
                <div className="flex items-center mb-3">
                  <SoundOutlined className="mr-2" />
                  <Text strong>Audio Voiceover</Text>
                </div>
                <audio controls className="w-full" preload="metadata">
                  <source src={audioUrl} type="audio/mpeg" />
                  <source src={audioUrl} type="audio/ogg" />
                  <source src={audioUrl} type="audio/wav" />
                  Your browser does not support the audio element.
                </audio>
              </div>
            )}

            {audioUrl && videoUrl && <Divider />}

            {/* Video Player */}
            {videoUrl && (
              <div>
                <div className="flex items-center mb-3">
                  <PlayCircleOutlined className="mr-2" />
                  <Text strong>Video Content</Text>
                </div>
                <video
                  controls
                  className="w-full rounded-lg shadow-md"
                  preload="metadata"
                  style={{ maxHeight: "500px" }}
                >
                  <source src={videoUrl} type="video/mp4" />
                  <source src={videoUrl} type="video/webm" />
                  <source src={videoUrl} type="video/ogg" />
                  Your browser does not support the video element.
                </video>
              </div>
            )}
          </Space>
        </Card>
      )}
    </div>
  );
}
