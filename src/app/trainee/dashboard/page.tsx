'use client';

import { useState, useEffect } from 'react';
import Roadmap from './components/Roadmap';
import UpdatesRibbon from './components/UpdatesRibbon';
// import LinkCard from './components/LinkCard';
// import CourseCard from './components/CourseCard';

// Add type definitions
interface Course {
  id: string;
  name: string;
  excerpt: string;
  bannerImageURL: string;
  category: {
    name: string;
    color: string;
  };
  externalLink: string;
}

interface CourseProgress {
  completed: number;
}

interface MyCourse {
  course: Course;
  progress: CourseProgress;
}


interface Trainee {
  id: string;
  name: string;
  email: string;
  traineeLevel: number;
  qualificationRoute: string;
  myCourses: MyCourse[];
}

export default function TraineeDashboard() {
  const [chatConfirmed] = useState(true);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [trainee, setTrainee] = useState<Trainee>({
    id: '',
    name: '',
    email: '',
    traineeLevel: 1,
    qualificationRoute: 'SQE',
    myCourses: []
  });
  const mockCourses = [
    {
      course: {
        id: '1',
        name: 'Course 1',
        excerpt: 'Excerpt 1',
        bannerImageURL: '',
        category: {
          name: 'Category 1',
          color: '#000000'
        },
        externalLink: ''
      },
      progress: {
        completed: 50
      }
    },
    {
      course: {
        id: '2',
        name: 'Course 2',
        excerpt: 'Excerpt 2',
        bannerImageURL: '',
        category: {
          name: 'Category 2',
          color: '#000000'
        },
        externalLink: ''
      },
      progress: {
        completed: 90
      }
    }
  ];



  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      const userResponse = await fetch('/api/users/me');
      if (!userResponse.ok) {
        throw new Error('Failed to fetch user data');
      }
      const userData = await userResponse.json();
      
      const traineeLevel = userData.traineeLevel || 1;
      
      setTrainee({
        ...userData,
        traineeLevel,
        myCourses: mockCourses
      });
      
    } catch (error) {
      console.error('Error fetching data:', error);
      setError('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const links = []
  if (trainee.qualificationRoute === "TC") {
    links.push({
      title: 'Training Record',
      subTitle:
        'The SRA require you to keep an up-to-date training record throughout your training contract demonstrating the experiences and skills gained. You can access your unique training portfolio here to record all your experiences and monitor which skills might need further attention.',
      linkUrl: '/trainee/training'
    });
  } else {
    links.push({
      title: 'Qualifying Work Experience',
      subTitle:
        'Keep up to date with where you\'re at with qualifying work experience. Access your dedicated training portfolio to capture all your work experience.',
      linkUrl: '/trainee/training'
    });
  }
  links.push({
    title: 'Learning & Development',
    subTitle:
      'Here you can book and complete courses on a variety of useful topics and access our Competency Plus library to further your skills.',
    linkUrl: '/trainee/all-courses'
  });

  if (trainee.traineeLevel === 3) {
    links.push(
      {
        title: 'Resources',
        subTitle:
          'Book and manage your mentor meetings and appraisals with your mentor and principal. You can also view our social and training events.',
        linkUrl: '/trainee/mentoring'
      }
    );
  }

  // const coursesInProgress =
  //   trainee?.myCourses?.filter(
  //     (course) =>
  //       course &&
  //       course.progress &&
  //       course.progress.completed > 0 &&
  //       course.progress.completed < 100
  //   ) ?? [];

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div className="flex flex-col">
      <div className="w-full flex flex-col">
        {chatConfirmed ? (
          <div className="md:w-[65%] w-full flex flex-col">
            <h1 className="text-[24px] font-bold">Dashboard</h1>
            <div className="font-light text-[14px] mt-4 text-color-primary-black">
              Hi {trainee.name}, here is your unique pathway to qualification.
              As you upload documents in the Qualifications area, your pathway
              will change from blue to green so you can see where you&apos;re at
              in your qualification. We hope you have a great journey!
            </div>
          </div>
        ) : (
          <></>
        )}
      </div>
      {trainee.qualificationRoute ? (
        <Roadmap
          chatConfirmed={chatConfirmed}
          roadmapType={
            trainee.qualificationRoute === "TC" ? "traditional" : "sqe"
          }
        />
      ) : (
        <div>
          You have not selected a qualification route. Please select one in the Qualifications area.
        </div>
      )}
      <div className="h-[50px]"></div>
      <UpdatesRibbon />
      {/* 
      <div className="h-[50px]"></div>
      <div className="grid gap-5 my-8 grid-cols-3 xl:grid-cols-3 lg:grid-cols-2 md:grid-cols-1">
        {links.map((link, index) => (
          <LinkCard
            key={index}
            title={link.title}
            subtitle={link.subTitle}
            linkUrl={link.linkUrl}
          />
        ))}
      </div>
      <div className="h-[50px]"></div>
      {coursesInProgress.length > 0 && (
        <>
          <div className="text-[24px] font-bold">Jump back in</div>
          <div className="font-light text-sm text-[#666666] mt-4 max-w-[500px]">
            If you&apos;re halfway through a course, you can jump right back in from
            here!
          </div>
          <div className="grid gap-6 py-4 grid-cols-[repeat(auto-fit,298px)]">
            {coursesInProgress.map(
              (course, index) =>
                course?.course && (
                  <CourseCard
                    key={index}
                    course={{
                      ident: course.course.id,
                      name: course.course.name,
                      excerpt: course.course.excerpt,
                      bannerImageURL: course.course.bannerImageURL,
                      category: course.course.category,
                      externalLink: course.course.externalLink
                    }}
                    progress={course?.progress?.completed}
                    isTaking
                  />
                )
            )}
          </div>
        </>
      )} */}
    </div>
  );
};
