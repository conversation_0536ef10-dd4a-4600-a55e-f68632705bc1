import React from 'react';
import moment from 'moment';

type Props = {
  course: {
    ident: string;
    name: string;
    excerpt?: string;
    bannerImageURL?: string;
    category?: {
      name: string;
      color: string;
    };
    externalLink?: string;
  };
  isTaking?: boolean;
  certificateURL?: string | null;
  progress?: number;
  completedAt?: string | null;
};

export default function CourseCard({ course, isTaking, certificateURL, progress, completedAt }: Props) {

  const descriptionLength = course?.excerpt?.length;

  return (
    <div className="bg-white flex flex-col rounded-lg shadow-[0px_5px_15px_#0000000f] w-[300px] overflow-hidden relative h-max max-h-[120%]">
      <div 
        className="flex items-center cursor-pointer h-[150px] bg-cover bg-center relative"
        style={{
          backgroundImage: course?.bannerImageURL ? `url(${course.bannerImageURL})` : undefined
        }}
      >
        <div className="absolute top-0 left-0 right-0 bottom-0 z-[1] bg-[rgba(0,0,0,0.3)]" />
        <div className="z-[10]">
          <div 
            className="absolute top-0 left-0 bg-[#f37a21] py-1.5 px-5 rounded-br-lg text-white text-xs font-bold"
            style={{ backgroundColor: course.category?.color ?? '#f37a21' }}
          >
            {course?.category?.name}
          </div>
          <div className="mt-[60px] mx-5 text-white font-bold text-xl overflow-hidden h-[150px]">
            {course.name.length < 90
              ? course.name
              : `${course.name.slice(0, course.name[88] == ' ' ? 87 : 88)}...`}
          </div>
        </div>
      </div>
      <div className={`p-5 flex flex-col ${progress !== undefined ? 'pb-0' : 'pb-5'}`}>
        {descriptionLength !== undefined && descriptionLength > 200 ? (
          <div className="relative">
            <input type="checkbox" id={`desc-${course.ident}`} className="hidden" />
            <label 
              htmlFor={`desc-${course.ident}`}
              className="absolute top-full right-0 text-[#f37a21] underline cursor-pointer after:content-['Show_More']"
            />
            <div className="text-sm font-light h-[100px] overflow-hidden relative">
              {course.excerpt}
              <div className="absolute bottom-0 left-0 w-full h-1/2 bg-gradient-to-t from-white via-[#ffffffb5] to-transparent" />
            </div>
          </div>
        ) : (
          <div className="text-sm font-light h-[100px] overflow-hidden">
            {course.excerpt}
          </div>
        )}
        <div className="h-2" />
        <div className="h-4" />
        {progress === undefined && !certificateURL && (
          <button className="bg-[#f37a21] text-white rounded-md py-2 px-3 text-sm font-medium">
            {!isTaking && course.externalLink ? 'More Information' : `${isTaking ? 'Take' : 'Book'} Course`}
          </button>
        )}
        {progress === undefined && certificateURL && (
          <a 
            href={certificateURL}
            className="bg-[#f37a21] text-white no-underline rounded-md py-2 px-3 text-sm font-normal flex justify-center items-center cursor-pointer"
          >
            Download Certificate
          </a>
        )}
      </div>
      {progress !== undefined && (
        <div className="flex justify-between px-4 py-3 bg-[#F5F5F5] border-t border-[#E5E5E5] rounded-b-lg">
          <div className="text-[#333333] text-xs">PROGRESS</div>
          <div className="w-[125px]">
            {/* CourseCompletion component will be added later */}
          </div>
        </div>
      )}
      {!progress && completedAt && (
        <div className="flex justify-between px-4 py-3 bg-[#F5F5F5] border-t border-[#E5E5E5] rounded-b-lg">
          <div className="text-center font-bold text-sm w-full">
            Completed: {moment(completedAt).format('DD/MM/YYYY')}
          </div>
        </div>
      )}
    </div>
  );
}
