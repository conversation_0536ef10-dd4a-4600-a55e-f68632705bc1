import React from 'react';

type Props = {
  title: string;
  subtitle: string;
  linkUrl: string;
};

export default function LinkCard({ title, subtitle, linkUrl }: Props) {
  return (
    <div className="flex flex-col bg-white rounded-lg border border-[#E5E5E5] p-5 shadow-[0px_3px_18px_#0000000d]">
      <div className="flex flex-col justify-start">
        <div className="font-bold text-lg mb-2.5">{title}</div>
        <div className="text-xs text-[#666666] mb-6 line-clamp-3">{subtitle}</div>
      </div>
      <a 
        href={linkUrl} 
        className="bg-brand-orange text-white no-underline rounded-md py-2.5 px-3 text-sm font-semibold flex justify-center items-center cursor-pointer w-1/2 h-8"
      >
        View
      </a>
    </div>
  );
}
