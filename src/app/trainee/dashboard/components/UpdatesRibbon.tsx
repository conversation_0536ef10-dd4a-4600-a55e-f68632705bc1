import React from 'react';
import Link from 'next/link';
import { useSession } from 'next-auth/react';

export default function UpdatesRibbon() {
  const { data: session } = useSession();
  const qualificationRoute = session?.user?.qualificationRoute;
  const traineeLevel = session?.user?.traineeLevel;
  const cards = [
    qualificationRoute === 'TC'
      ? {
          title: 'Training Record',
          description:
            'The SRA require you to keep an up-to-date training record throughout your training contract demonstrating the experiences and skills gained. You can access your unique training portfolio here to record all your experiences and monitor which skills might need further attention.',
          linkText: 'View',
          linkHref: '/trainee/training',
        }
      : {
          title: 'Qualifying Work Experience',
          description:
            "Keep up to date with where you're at with qualifying work experience. Access your dedicated training portfolio to capture all your work...",
          linkText: 'View',
          linkHref: '/trainee/training',
        },
    {
      title: 'Learning & Development',
      description:
        'Here you can book and complete courses on a variety of useful topics and access our Competency Plus library to further your skills.',
      linkText: 'View',
      linkHref: '/trainee/courses',
    },
    traineeLevel === 3 ?
    {
      title: 'Resources',
      description:
        'Book and manage your mentor meetings and appraisals with your mentor and training principal.',
      linkText: 'View',
      linkHref: '/trainee/meetings',
    } : null,
  ];

  return (
    <div className="w-full py-6">
      <h2 className="text-lg font-bold mb-6 px-4">UPDATES</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 px-4">
        {cards.filter(Boolean).map((card, index) => {
          const c = card as { title: string; description: string; linkText: string; linkHref: string };
          return (
            <div key={index} className="bg-white rounded-lg shadow-sm p-6 flex flex-col h-full">
              <h3 className="text-lg font-bold mb-2">{c.title}</h3>
              <p className="text-gray-600 mb-6 text-sm flex-grow">{c.description}</p>
              <div className="mt-auto">
                <Link href={c.linkHref}>
                  <span className="inline-block bg-[#F58220] text-white font-medium py-2 px-8 rounded text-center hover:bg-[#e67b1e] transition-colors w-full md:w-auto">
                    {c.linkText}
                  </span>
                </Link>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
