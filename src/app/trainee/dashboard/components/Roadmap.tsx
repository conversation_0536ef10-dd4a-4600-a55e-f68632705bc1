'use client';

import React, { useState, useEffect } from 'react';
import ProgressBox from './ProgressBox';
import DegreeIcon from '@/assets/roadmap/newDegree.svg';
import TrainingContractIcon from '@/assets/roadmap/QWE.svg';
import CharacterSuitabilityIcon from '@/assets/roadmap/newCS.png';
import QualifiedIcon from '@/assets/roadmap/newQua.png';
import GDLIcon from '@/assets/roadmap/newLearn.png';
import SQE1Icon from '@/assets/roadmap/newSQE1.png';
import SQE2Icon from '@/assets/roadmap/newSQE2LPC.png';
import RunnerIcon from '@/assets/roadmap/runner.svg';
import RightArrowIcon from '@/assets/roadmap/rightarrows.svg';
import LeftArrowIcon from '@/assets/roadmap/leftarrows.svg';
import Image from 'next/image';
import { Qualification, QualificationType } from '@/generated/prisma';
import { useSession } from 'next-auth/react';

const traditionalRoadmapData = {
  type: 'traditional',
  steps: [
    {
      id: 1,
      name: 'Degree',
      icon: DegreeIcon,
      completed: false,
      position: 'pos-1'
    },
    {
      id: 2,
      name: 'LPC',
      icon: SQE2Icon,
      completed: false,
      position: 'pos-2'
    },
    {
      id: 4,
      name: 'Training Contract',
      icon: TrainingContractIcon,
      completed: false,
      position: 'pos-3'
    },
    {
      id: 5,
      name: 'Character & Suitability',
      icon: CharacterSuitabilityIcon,
      completed: false,
      position: 'pos-4'
    },
    {
      id: 6,
      name: 'Qualified',
      icon: QualifiedIcon,
      completed: false,
      position: 'pos-5'
    }
  ],
  progress: {
    maxTime: 522,
    maxSkills: 100,
    curTime: 0,
    curSkills: 0
  }
};

const sqeRoadmapData = {
  type: 'sqe',
  steps: [
    {
      id: 1,
      name: 'Degree',
      icon: DegreeIcon,
      completed: false,
      position: 'pos-1'
    },
    {
      id: 2,
      name: 'SQE1',
      icon: SQE1Icon,
      completed: false,
      position: 'pos-2'
    },
    {
      id: 3,
      name: 'SQE2',
      icon: SQE2Icon,
      completed: false,
      position: 'pos-3'
    },
    {
      id: 4,
      name: 'Qualifying Work Experience',
      icon: TrainingContractIcon,
      completed: false,
      position: 'pos-4'
    },
    {
      id: 5,
      name: 'Character & Suitability',
      icon: CharacterSuitabilityIcon,
      completed: false,
      position: 'pos-5'
    },
    {
      id: 6,
      name: 'Qualified',
      icon: QualifiedIcon,
      completed: false,
      position: 'pos-6'
    }
  ],
  progress: {
    maxTime: 522,
    maxSkills: 100,
    curTime: 0,
    curSkills: 0
  }
};

type ProgressData = {
  maxTime: number;
  maxSkills: number;
  curTime: number;
  curSkills: number;
};

type Props = {
  chatConfirmed: boolean;
  roadmapType?: 'traditional' | 'sqe';
  progressData?: ProgressData;
};

interface DashboardData {
  qualifications: Qualification[];
  characterSuitability: {
    id: string;
    rules: string;
    userCompletions: Array<{
      id: string;
      completed: boolean;
      userId: string;
    }>;
  };
  skillsProgress: {
    practiceAreas: Array<{
      id: string;
      name: string;
      practiceSkills: Array<{
        id: string;
        name: string;
        practiceSkillGroups: Array<{
          id: string;
          name: string;
          practiceSubSkills: Array<{
            id: string;
            name: string;
            minSuggestedEntryCount: number;
            doneEntryCount?: number;
          }>;
        }>;
      }>;
    }>;
    totalDays: number;
  };
}

interface DashboardState {
  data: DashboardData | null;
  loading: boolean;
  error: string | null;
}

export default function Roadmap({ chatConfirmed, roadmapType = 'sqe' }: Props) {
  const { data: session } = useSession();
  const [skillGroupTypeConfirmed, setSkillGroupTypeConfirmed] = useState(chatConfirmed);
  const [roadmapData, setRoadmapData] = useState(roadmapType === 'traditional' ? traditionalRoadmapData : sqeRoadmapData);
  const [dashboardData, setDashboardData] = useState<DashboardState>({
    data: null,
    loading: true,
    error: null
  });

  useEffect(() => {
    setSkillGroupTypeConfirmed(chatConfirmed);
  }, [chatConfirmed]);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const response = await fetch('/api/dashboard');
        const data = await response.json();
        
        if (response.ok) {
          setDashboardData({
            data,
            loading: false,
            error: null
          });
        } else {
          setDashboardData(prev => ({
            ...prev,
            loading: false,
            error: data.error || 'Failed to fetch dashboard data'
          }));
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setDashboardData(prev => ({
          ...prev,
          loading: false,
          error: 'Failed to fetch dashboard data'
        }));
      }
    };

    fetchDashboardData();
  }, []);

  // Update roadmap when dashboard data changes
  useEffect(() => {
    if (dashboardData.loading || !dashboardData.data) {
      return;
    }

    const { qualifications, characterSuitability, skillsProgress } = dashboardData.data;

    // Calculate completion statuses
    const isQualificationCompleted = (type: QualificationType) => {
      return qualifications.some(q => q.type === type);
    };

    const isCharacterSuitabilityCompleted = () => {
      return characterSuitability?.userCompletions?.some(
        completion => completion.completed && completion.userId === session?.user?.id
      ) || false;
    };

    const degreeCompleted = isQualificationCompleted(QualificationType.DEGREE) || isQualificationCompleted(QualificationType.GDL);
    const lpcCompleted = isQualificationCompleted(QualificationType.LPC);
    const sqe1Completed = isQualificationCompleted(QualificationType.SQE1);
    const sqe2Completed = isQualificationCompleted(QualificationType.SQE2);
    const characterSuitabilityCompleted = isCharacterSuitabilityCompleted();

    // Calculate QWE completion
    const userQualificationRoute = session?.user?.qualificationRoute;
    const qweArea = skillsProgress.practiceAreas.find(area => 
      area.name.toLowerCase().includes(userQualificationRoute?.toLowerCase() || 'sqe')
    );

    // Calculate total entries and completion percentage
    let totalCompleted = 0;
    let totalMinSuggested = 0;

    if (qweArea) {
      qweArea.practiceSkills.forEach(skill => {
        skill.practiceSkillGroups.forEach(group => {
          group.practiceSubSkills.forEach(subSkill => {
            totalCompleted += subSkill.doneEntryCount || 0;
            totalMinSuggested += subSkill.minSuggestedEntryCount;
          });
        });
      });
    }

    const qweProgress = totalMinSuggested > 0 ? (totalCompleted / totalMinSuggested) * 100 : 0;
    const qweCompleted = qweProgress >= 100;

    // Update steps completion status
    const updatedSteps = roadmapData.steps.map(step => {
      const name = step.name.toLowerCase();
      
      // Initialize completion status based on step type
      let completed = false;
      
      // Use switch-case to ensure only one condition is applied
      switch (true) {
        case name.includes('degree'):
          completed = degreeCompleted;
          break;
          
        case name.includes('sqe1'):
          completed = sqe1Completed;
          break;
          
        case name.includes('sqe2'):
          completed = sqe2Completed;
          break;
          
        case name.includes('lpc'):
          completed = lpcCompleted;
          break;
          
        case name.includes('qualifying work experience') || name.includes('training contract'):
          completed = qweCompleted;
          break;
          
        case name.includes('character'):
          completed = characterSuitabilityCompleted;
          break;
          
        case name.includes('qualified'):
          if (roadmapType === 'sqe') {
            completed = degreeCompleted && sqe1Completed && sqe2Completed && qweCompleted && characterSuitabilityCompleted;
          } else {
            completed = degreeCompleted && lpcCompleted && qweCompleted && characterSuitabilityCompleted;
          }
          break;
      }

      return {
        ...step,
        completed
      };
    });

    // Update roadmap data
    setRoadmapData(prevData => ({
      ...prevData,
      steps: updatedSteps,
      progress: {
        maxTime: 522,
        maxSkills: totalMinSuggested,
        curTime: skillsProgress.totalDays,
        curSkills: totalCompleted
      }
    }));
  }, [dashboardData.data, roadmapType, session?.user?.id]);

  if (dashboardData.loading) {
    return <div>Loading...</div>;
  }

  if (dashboardData.error) {
    return <div>Error: {dashboardData.error}</div>;
  }

  return (
    <div className="flex flex-col items-start lg:items-center min-h-[200px] sm:min-h-[300px] md:min-h-[350px] lg:min-h-[450px] px-8 py-4">
      {skillGroupTypeConfirmed ? (
        <div className="relative w-0 lg:w-auto scale-40 sm:scale-45 md:scale-50 lg:scale-100 flex flex-col items-start lg:items-center">
          {/* Start and Finish circles */}
          <div className="absolute top-[28px] left-[20px] w-[82px] h-[82px] bg-[#212121] text-white z-10 rounded-full flex items-center justify-center border-8 border-[#fd6d21]">
            Start
          </div>
          <div className="absolute top-[315px] left-[630px] w-[82px] h-[82px] bg-[#212121] text-white z-10 rounded-full flex items-center justify-center border-8 border-[#fd6d21]">
            Finish
          </div>

          {/* Path line */}
          <div className="absolute lg:relative w-[620px] h-[320px] m-[64px] bg-no-repeat" 
               style={{ backgroundImage: `url(${RunnerIcon.src})` }} />

          {/* Arrow indicators */}
          <div className="absolute top-[21px] left-[595px] w-[136px] h-[242px] bg-no-repeat"
               style={{ backgroundImage: `url(${RightArrowIcon.src})` }} />
          <div className="absolute top-[165px] left-[62px] w-[136px] h-[242px] bg-no-repeat"
               style={{ backgroundImage: `url(${LeftArrowIcon.src})` }} />

          {/* Milestone icons */}
          {roadmapData.steps.map((step) => (
            <React.Fragment key={step.id}>
              <div className={`absolute ${step.position} w-[80px] h-[80px] bg-white rounded-full flex items-center justify-center ${
                step.completed ? 'shadow-[0_0_13px_13px_#96d4a0]' : 'shadow-[0_0_10px_10px_#b7e8fe]'
              } z-10`}>
                <Image src={step.icon} alt={step.name} width={38} height={38} className="w-[38px] h-[38px] mx-auto z-20" />
              </div>
              <div className={`absolute ${step.position} translate-y-[92px] w-[140px] -translate-x-[30px] text-center text-[16px] text-gray-600`}>
                {step.name}
              </div>
            </React.Fragment>
          ))}

          {/* Progress box */}
          <div className="absolute top-[182px] left-[345px] w-[220px] h-[62px] bg-white border border-[#e5e5e5] rounded-[10px] z-30">
            <ProgressBox 
              maxTime={roadmapData.progress.maxTime} 
              maxSkills={roadmapData.progress.maxSkills} 
              curTime={roadmapData.progress.curTime} 
              curSkills={roadmapData.progress.curSkills} 
            />
          </div>
        </div>
      ) : (
        <div className="text-center my-[100px]">
          <strong>
            Please complete the questions Al asks you on the right. <br />
            Once complete, we will customize your tailored journey and your roadmap will appear.
          </strong>
        </div>
      )}
    </div>
  );
}
