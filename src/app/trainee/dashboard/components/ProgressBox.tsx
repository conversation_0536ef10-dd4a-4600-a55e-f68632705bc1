import React from 'react';

type Props = {
  maxTime: number;
  maxSkills: number;
  curTime: number;
  curSkills: number;
};

function ProgressBox({ maxTime, maxSkills, curTime, curSkills }: Props) {
  const calculatePercentage = (current: number, max: number) => {
    if (current === 0) return 0;
    const percentage = (current * 100) / max;
    return percentage >= 100 ? 100 : percentage;
  };

  const daysPercentage = calculatePercentage(curTime, maxTime);
  const skillsPercentage = calculatePercentage(curSkills, maxSkills);

  return (
    <div className="flex flex-col justify-between p-[10px_8px_10px_16px] h-full box-border">
      <div className="flex w-full items-center">
        <div className="w-[42px] text-[13px] text-[#737987]">Days</div>
        <div className="w-[136px] h-[12px] border border-[#e5e5e5] mx-[6px] bg-white rounded-[6px] overflow-hidden">
          <div 
            className="h-[10px] rounded-[5px] bg-gradient-to-r from-[#f27b31] to-[#f9e1b4]"
            style={{ width: `${daysPercentage}%` }}
          />
        </div>
        <div className="w-[80px] text-[11px] text-center text-[#7c818e]">
          {`${curTime >= maxTime ? maxTime : curTime}/${maxTime}`}
        </div>
      </div>
      <div className="flex w-full items-center">
        <div className="w-[42px] text-[13px] text-[#737987]">Skills</div>
        <div className="w-[136px] h-[12px] border border-[#e5e5e5] mx-[6px] bg-white rounded-[6px] overflow-hidden">
          <div 
            className="h-[10px] rounded-[5px] bg-gradient-to-r from-[#f27b31] to-[#f9e1b4]"
            style={{ width: `${skillsPercentage}%` }}
          />
        </div>
        <div className="w-[80px] text-[11px] text-center text-[#7c818e]">
          {`${curSkills >= maxSkills ? maxSkills : curSkills}/${maxSkills}`}
        </div>
      </div>
    </div>
  );
}

export default ProgressBox;
