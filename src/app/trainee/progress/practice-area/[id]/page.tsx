'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Progress,
  Spin,
  Table,
  Tag,
  Statistic,
  Row,
  Col,
  Breadcrumb,
  Button,
  Empty,
  Input,
  Tabs,
  TablePaginationConfig
} from 'antd';
import {
  HomeOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ArrowLeftOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import Link from 'next/link';
import { useParams } from 'next/navigation';
const { Title, Text } = Typography;
const { TabPane } = Tabs;

interface SubSkill {
  id: string;
  name: string;
  doneEntryCount: number;
  minSuggestedEntryCount: number;
  progress: number;
  isCompleted: boolean;
  documentKey?: string;
}

interface SkillGroup {
  id: string;
  name: string;
  subSkills: SubSkill[];
  progress: number;
  isCompleted: boolean;
}

interface Skill {
  id: string;
  name: string;
  groups: SkillGroup[];
  progress: number;
  isCompleted: boolean;
}

interface TableRow {
  key: string;
  id: string;
  name: string;
  doneEntryCount?: number;
  minSuggestedEntryCount?: number;
  progress: number;
  level: number;
  isCompleted: boolean;
  documentKey?: string;
}

interface PracticeAreaData {
  trainee: {
    name: string;
    email: string;
    qualificationRoute: string;
  };
  practiceArea: {
    id: string;
    name: string;
    description: string | null;
  };
  progress: number;
  totalSkills: number;
  completedSkills: number;
  skills: Skill[];
}

export default function PracticeAreaPage() {
  const params = useParams();
  const [loading, setLoading] = useState(true);
  const [practiceAreaData, setPracticeAreaData] = useState<PracticeAreaData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [searchText, setSearchText] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  useEffect(() => {
    fetchPracticeAreaProgress();
  }, []);

  const fetchPracticeAreaProgress = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/trainee-skills/practice-area/${params.id}`);

      if (!response.ok) {
        throw new Error('Failed to fetch practice area progress data');
      }

      const data = await response.json();
      setPracticeAreaData(data);
    } catch (error) {
      console.error('Error fetching practice area progress:', error);
      setError('Failed to load practice area progress data');
    } finally {
      setLoading(false);
    }
  };

  const getFilteredSkillRows = (skills: Skill[], filterCompleted?: boolean) => {
    const rows: TableRow[] = [];
    
    skills.forEach((skill: Skill) => {
      const skillGroups = filterCompleted === undefined ? skill.groups :
        filterCompleted ? skill.groups.filter((g: SkillGroup) => g.isCompleted) :
        skill.groups.filter((g: SkillGroup) => !g.isCompleted);

      if (skillGroups.length > 0) {
        const skillRow: TableRow = {
          key: skill.id,
          id: skill.id,
          name: skill.name,
          progress: skill.progress,
          level: 0,
          isCompleted: skill.isCompleted
        };

        if (searchText && !skillRow.name.toLowerCase().includes(searchText.toLowerCase())) {
          return;
        }

        rows.push(skillRow);

        skillGroups.forEach((group: SkillGroup) => {
          const groupRow: TableRow = {
            key: group.id,
            id: group.id,
            name: group.name,
            progress: group.progress,
            level: 1,
            isCompleted: group.isCompleted
          };

          if (searchText && !groupRow.name.toLowerCase().includes(searchText.toLowerCase())) {
            return;
          }

          rows.push(groupRow);

          const filteredSubSkills = filterCompleted === undefined ? group.subSkills :
            filterCompleted ? group.subSkills.filter((s: SubSkill) => s.doneEntryCount >= s.minSuggestedEntryCount) :
            group.subSkills.filter((s: SubSkill) => s.doneEntryCount < s.minSuggestedEntryCount);

          filteredSubSkills.forEach((subSkill: SubSkill) => {
            const subSkillRow: TableRow = {
              key: subSkill.id,
              id: subSkill.id,
              name: subSkill.name,
              doneEntryCount: subSkill.doneEntryCount,
              minSuggestedEntryCount: subSkill.minSuggestedEntryCount,
              progress: subSkill.progress,
              level: 2,
              isCompleted: subSkill.isCompleted,
              documentKey: subSkill.documentKey
            };

            if (searchText && !subSkillRow.name.toLowerCase().includes(searchText.toLowerCase())) {
              return;
            }

            rows.push(subSkillRow);
          });
        });
      }
    });

    return rows;
  };

  const columns = [
    {
      title: 'Skill',
      dataIndex: 'name',
      key: 'name',
      render: (_: unknown, record: TableRow) => {
        const level = record.level || 0;
        const indent = level * 24;
        return (
          <div style={{ marginLeft: indent }}>
            <Text>{record.name}</Text>
          </div>
        );
      },
    },
    {
      title: 'Completed',
      key: 'completed',
      render: (_: unknown, record: TableRow) => {
        if (!record.doneEntryCount && !record.minSuggestedEntryCount) {
          return null;
        }
        return (
          <Text>{record.doneEntryCount} / {record.minSuggestedEntryCount}</Text>
        );
      },
    },
    {
      title: 'Progress',
      key: 'progress',
      render: (_: unknown, record: TableRow) => {
        if (!record.progress && record.progress !== 0) {
          return null;
        }
        return <Progress percent={Math.round(record.progress)} size="small" />;
      },
    },
    {
      title: 'Document',
      key: 'document',
      render: (_: unknown, record: TableRow) => {
        if (record.level !== 2 || !record.documentKey) {
          return null;
        }
        return (
          <Button 
            type="link" 
            icon={<FileTextOutlined />}
            onClick={() => window.open(`/api/documents/${record.documentKey}`, '_blank')}
          >
            View
          </Button>
        );
      },
    },
    {
      title: 'Status',
      key: 'status',
      render: (_: unknown, record: TableRow) => {
        if (record.level === 0 || record.level === 1) {
          return record.isCompleted ? 
            <Tag color="success" icon={<CheckCircleOutlined />}>Completed</Tag> :
            <Tag color="processing" icon={<ClockCircleOutlined />}>In Progress</Tag>;
        }
        
        if (!record.doneEntryCount && !record.minSuggestedEntryCount) {
          return null;
        }
        
        return (
          (record.doneEntryCount ?? 0) >= (record.minSuggestedEntryCount ?? 0) ?
            <Tag color="success" icon={<CheckCircleOutlined />}>Completed</Tag> :
            <Tag color="processing" icon={<ClockCircleOutlined />}>In Progress</Tag>
        );
      },
    },
  ];

  const getPaginationConfig = (totalItems: number) => ({
    current: currentPage,
    pageSize: pageSize,
    total: totalItems,
    showSizeChanger: true,
    showTotal: (total: number, range: [number, number]) => `${range[0]}-${range[1]} of ${total} items`,
    pageSizeOptions: [10, 20, 50, 100].map(String),
    defaultPageSize: 20,
    onChange: (page: number, size: number) => {
      setCurrentPage(page);
      setPageSize(size);
    }
  });

  const handleTableChange = (pagination: TablePaginationConfig) => {
    if (pagination.pageSize === undefined) {
      // Handle 'Show All' case
      const totalItems = practiceAreaData?.skills.length || 0;
      setPageSize(totalItems);
      setCurrentPage(1);
    } else {
      setCurrentPage(pagination.current || 1);
      setPageSize(pagination.pageSize);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-64">
        <Text type="danger">{error}</Text>
      </div>
    );
  }

  if (!practiceAreaData) {
    return (
      <div className="flex justify-center items-center h-64">
        <Empty description="No practice area data available" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      <Breadcrumb className="mb-6">
        <Breadcrumb.Item>
          <Link href="/trainee/trainee-dashboard">
            <HomeOutlined /> Dashboard
          </Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <Link href="/trainee/progress">
            Progress Tracking
          </Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{practiceAreaData.practiceArea.name}</Breadcrumb.Item>
      </Breadcrumb>

      <div className="mb-6">
        <Link href="/trainee/progress">
          <Button icon={<ArrowLeftOutlined />}>Back to Progress Overview</Button>
        </Link>
      </div>

      <Title level={2}>{practiceAreaData.practiceArea.name}</Title>
      {practiceAreaData.practiceArea.description && (
        <Text className="text-gray-500 mb-6 block">
          {practiceAreaData.practiceArea.description}
        </Text>
      )}

      {/* Practice Area Progress */}
      <Card className="mb-6">
        <Row gutter={24}>
          <Col span={8}>
            <Statistic
              title="Area Progress"
              value={Math.round(practiceAreaData.progress)}
              suffix="%"
            />
            <Progress
              percent={Math.round(practiceAreaData.progress)}
              status="active"
              className="mt-2"
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="Completed Skills"
              value={practiceAreaData.completedSkills}
              suffix={`/ ${practiceAreaData.totalSkills}`}
            />
          </Col>
        </Row>
      </Card>

      {/* Skills Table */}
      <Card>
        <Title level={4}>Skills</Title>
        <Input.Search
          placeholder="Search skills..."
          style={{ marginBottom: 16, width: 300 }}
          value={searchText}
          onChange={e => setSearchText(e.target.value)}
          allowClear
        />
        <Tabs defaultActiveKey="all">
          <TabPane tab="All Skills" key="all">
            <Table
              dataSource={getFilteredSkillRows(practiceAreaData.skills)}
              columns={columns}
              pagination={getPaginationConfig(getFilteredSkillRows(practiceAreaData.skills).length)}
              onChange={handleTableChange}
              scroll={{ x: 'max-content' }}
            />
          </TabPane>
          <TabPane tab="Completed" key="completed">
            <Table
              dataSource={getFilteredSkillRows(practiceAreaData.skills, true)}
              columns={columns}
              pagination={getPaginationConfig(getFilteredSkillRows(practiceAreaData.skills, true).length)}
              onChange={handleTableChange}
              scroll={{ x: 'max-content' }}
            />
          </TabPane>
          <TabPane tab="In Progress" key="inProgress">
            <Table
              dataSource={getFilteredSkillRows(practiceAreaData.skills, false)}
              columns={columns}
              pagination={getPaginationConfig(getFilteredSkillRows(practiceAreaData.skills, false).length)}
              onChange={handleTableChange}
              scroll={{ x: 'max-content' }}
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
}
