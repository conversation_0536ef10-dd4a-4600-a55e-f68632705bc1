'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Progress,
  Spin,
  Tabs,
  Table,
  Tag,
  Statistic,
  Row,
  Col,
  Breadcrumb,
  Button,
  Radio,
  Form,
  Space,
  App,
  Result,
  Input,
  TablePaginationConfig
} from 'antd';
import {
  HomeOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  FileSearchOutlined
} from '@ant-design/icons';
import Link from 'next/link';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

interface SubSkill {
  id: string;
  name: string;
  doneEntryCount: number;
  minSuggestedEntryCount: number;
  progress: number;
  isCompleted: boolean;
}

interface SkillGroup {
  id: string;
  name: string;
  subSkills: SubSkill[];
  progress: number;
  isCompleted: boolean;
}

interface Skill {
  id: string;
  name: string;
  groups: SkillGroup[];
  progress: number;
  isCompleted: boolean;
}

interface PracticeArea {
  id: string;
  name: string;
  skills: Skill[];
  progress: number;
  totalSkills: number;
  completedSkills: number;
}

interface TableRow {
  key: string;
  id: string;
  name: string;
  doneEntryCount?: number;
  minSuggestedEntryCount?: number;
  progress: number;
  level: number;
  isCompleted: boolean;
}

interface ProgressData {
  trainee: {
    name: string;
    email: string;
    qualificationRoute: string;
  };
  overallProgress: number;
  totalSkills: number;
  completedSkills: number;
  practiceAreas: PracticeArea[];
}

export default function ProgressPage() {
  const { message } = App.useApp();
  const [loading, setLoading] = useState(true);
  const [progressData, setProgressData] = useState<ProgressData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [noQualificationRoute, setNoQualificationRoute] = useState(false);
  const [initializing, setInitializing] = useState(false);
  const [qualificationRoute, setQualificationRoute] = useState<string>("TC");
  const [form] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  useEffect(() => {
    fetchProgress();
  }, []);

  const fetchProgress = async () => {
    try {
      setLoading(true);
      // First check if user has qualification route before even fetching progress
      const userResponse = await fetch('/api/users/me');
      
      if (userResponse.ok) {
        const userData = await userResponse.json();
        if (!userData.qualificationRoute) {
          setNoQualificationRoute(true);
          setLoading(false);
          return;
        }
        setQualificationRoute(userData.qualificationRoute);
      }

      const response = await fetch('/api/trainee-skills/progress');

      if (response.status === 404) {
        const data = await response.json();
        if (data.needsInitialization) {
          setNoQualificationRoute(true);
          setLoading(false);
          return;
        }
      }

      if (!response.ok) {
        throw new Error('Failed to fetch progress data');
      }

      const data = await response.json();
      setProgressData(data);
    } catch (error) {
      console.error('Error fetching progress:', error);
      setError('Failed to load progress data');
    } finally {
      setLoading(false);
    }
  };

  const setUserQualificationRoute = async () => {
    try {
      setInitializing(true);
      
      // First update the user's qualification route
      const updateResponse = await fetch('/api/users/me', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ qualificationRoute }),
      });

      if (!updateResponse.ok) {
        throw new Error('Failed to update qualification route');
      }

      // Then initialize the skills based on the qualification route
      const initResponse = await fetch('/api/trainee-skills/initialize', {
        method: 'POST'
      });

      if (!initResponse.ok) {
        throw new Error('Failed to initialize skill tracking');
      }

      message.success('Qualification route set and skills initialized successfully');
      setNoQualificationRoute(false);
      fetchProgress();
    } catch (error) {
      console.error('Error setting qualification route:', error);
      message.error('Failed to set qualification route');
    } finally {
      setInitializing(false);
    }
  };

  const renderQualificationRouteSelection = () => {
    return (
      <div className="container mx-auto px-4 py-6">
        <Breadcrumb className="mb-6">
          <Breadcrumb.Item>
            <Link href="/trainee/dashboard">
              <HomeOutlined /> Dashboard
            </Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>Progress Tracking</Breadcrumb.Item>
        </Breadcrumb>

        <Result
          icon={<FileSearchOutlined style={{ color: 'rgb(199, 100, 27)' }} />}
          title="Set Your Qualification Route"
          subTitle="You need to set your qualification route before accessing progress tracking"
          extra={
            <Card className="max-w-2xl mx-auto">
              <Form
                form={form}
                layout="vertical"
                className="mx-auto"
                onFinish={setUserQualificationRoute}
                initialValues={{ qualificationRoute: 'TC' }}
              >
                <Form.Item 
                  name="qualificationRoute" 
                  label={<span className="text-lg font-medium">Select Your Qualification Route</span>}
                  rules={[{ required: true, message: 'Please select a qualification route' }]}
                >
                  <Radio.Group 
                    onChange={(e) => setQualificationRoute(e.target.value)}
                    value={qualificationRoute}
                    className="w-full"
                  >
                    <Space direction="vertical" className="w-full">
                      <div className={`p-4 border rounded-lg transition-all hover:border-[rgb(199,100,27)] ${qualificationRoute === 'TC' ? 'border-[rgb(199,100,27)] bg-[rgba(199,100,27,0.05)]' : 'border-gray-200'}`}>
                        <Radio value="TC" className="w-full">
                          <div className="ml-2">
                            <Text strong className="text-[16px] block">Training Contract (TC)</Text>
                            <Text type="secondary">Traditional training path for solicitors</Text>
                          </div>
                        </Radio>
                      </div>

                      <div className={`p-4 border rounded-lg transition-all hover:border-[rgb(199,100,27)] ${qualificationRoute === 'SQE' ? 'border-[rgb(199,100,27)] bg-[rgba(199,100,27,0.05)]' : 'border-gray-200'}`}>
                        <Radio value="SQE" className="w-full">
                          <div className="ml-2">
                            <Text strong className="text-[16px] block">Solicitors Qualifying Examination (SQE)</Text>
                            <Text type="secondary">New path to qualification as a solicitor</Text>
                          </div>
                        </Radio>
                      </div>
                    </Space>
                  </Radio.Group>
                </Form.Item>

                <Form.Item className="mt-6 text-center">
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={initializing}
                    size="large"
                    style={{ backgroundColor: 'rgb(199, 100, 27)' }}
                  >
                    Set Qualification Route & Continue
                  </Button>
                </Form.Item>
              </Form>
            </Card>
          }
        />
      </div>
    );
  }

  // Filter practice areas based on qualification route
  const filteredPracticeAreas = progressData?.practiceAreas.filter(
    area => area.name.includes(progressData.trainee.qualificationRoute === 'TC' ? 'Training Contract' : 'Solicitors Qualifying Examination')
  ) || [];

  const getFilteredSkillRows = (areas: PracticeArea[], filterCompleted?: boolean) => {
    const rows: TableRow[] = [];
    
    areas.forEach(area => {
      area.skills.forEach((skill: Skill) => {
        const skillGroups = filterCompleted === undefined ? skill.groups :
          filterCompleted ? skill.groups.filter((g: SkillGroup) => g.isCompleted) :
          skill.groups.filter((g: SkillGroup) => !g.isCompleted);

        if (skillGroups.length > 0) {
          const skillRow: TableRow = {
            key: skill.id,
            id: skill.id,
            name: skill.name,
            progress: skill.progress,
            level: 0,
            isCompleted: skill.isCompleted
          };

          if (searchText && !skillRow.name.toLowerCase().includes(searchText.toLowerCase())) {
            return;
          }

          rows.push(skillRow);

          skillGroups.forEach((group: SkillGroup) => {
            const groupRow: TableRow = {
              key: group.id,
              id: group.id,
              name: group.name,
              progress: group.progress,
              level: 1,
              isCompleted: group.isCompleted
            };

            if (searchText && !groupRow.name.toLowerCase().includes(searchText.toLowerCase())) {
              return;
            }

            rows.push(groupRow);

            const filteredSubSkills = filterCompleted === undefined ? group.subSkills :
              filterCompleted ? group.subSkills.filter((s: SubSkill) => s.doneEntryCount >= s.minSuggestedEntryCount) :
              group.subSkills.filter((s: SubSkill) => s.doneEntryCount < s.minSuggestedEntryCount);

            filteredSubSkills.forEach((subSkill: SubSkill) => {
              const subSkillRow: TableRow = {
                key: subSkill.id,
                id: subSkill.id,
                name: subSkill.name,
                doneEntryCount: subSkill.doneEntryCount,
                minSuggestedEntryCount: subSkill.minSuggestedEntryCount,
                progress: subSkill.progress,
                level: 2,
                isCompleted: subSkill.isCompleted
              };

              if (searchText && !subSkillRow.name.toLowerCase().includes(searchText.toLowerCase())) {
                return;
              }

              rows.push(subSkillRow);
            });
          });
        }
      });
    });

    return rows;
  };

  const columns = [
    {
      title: 'Skill',
      dataIndex: 'name',
      key: 'name',
      render: (_: unknown, record: TableRow) => {
        const level = record.level || 0;
        const indent = level * 24;
        return (
          <div style={{ marginLeft: indent }}>
            <Text>{record.name}</Text>
          </div>
        );
      },
    },
    {
      title: 'Completed',
      key: 'completed',
      render: (_: unknown, record: TableRow) => {
        if (!record.doneEntryCount && !record.minSuggestedEntryCount) {
          return null;
        }
        return (
          <Text>{record.doneEntryCount} / {record.minSuggestedEntryCount}</Text>
        );
      },
    },
    {
      title: 'Progress',
      key: 'progress',
      render: (_: unknown, record: TableRow) => {
        if (!record.progress && record.progress !== 0) {
          return null;
        }
        return <Progress percent={Math.round(record.progress)} size="small" />;
      },
    },
    {
      title: 'Status',
      key: 'status',
      render: (_: unknown, record: TableRow) => {
        if (record.level === 0 || record.level === 1) {
          return record.isCompleted ? 
            <Tag color="success" icon={<CheckCircleOutlined />}>Completed</Tag> :
            <Tag color="processing" icon={<ClockCircleOutlined />}>In Progress</Tag>;
        }
        
        if (!record.doneEntryCount && !record.minSuggestedEntryCount) {
          return null;
        }
        
        return (
          (record.doneEntryCount ?? 0) >= (record.minSuggestedEntryCount ?? 0) ?
            <Tag color="success" icon={<CheckCircleOutlined />}>Completed</Tag> :
            <Tag color="processing" icon={<ClockCircleOutlined />}>In Progress</Tag>
        );
      }
    },
  ];

  const getPaginationConfig = (totalItems: number) => ({
    current: currentPage,
    pageSize: pageSize,
    total: totalItems,
    showSizeChanger: true,
    showTotal: (total: number, range: [number, number]) => `${range[0]}-${range[1]} of ${total} items`,
    pageSizeOptions: [10, 20, 50, 100].map(String),
    defaultPageSize: 20,
    onChange: (page: number, size: number) => {
      setCurrentPage(page);
      setPageSize(size);
    }
  });

  const handleTableChange = (pagination: TablePaginationConfig) => {
    if (pagination.pageSize === undefined) {
      // Handle 'Show All' case
      const totalItems = progressData?.practiceAreas.length || 0;
      setPageSize(totalItems);
      setCurrentPage(1);
    } else {
      setCurrentPage(pagination.current || 1);
      setPageSize(pagination.pageSize);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (noQualificationRoute) {
    return renderQualificationRouteSelection();
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-64">
        <Text type="danger">{error}</Text>
      </div>
    );
  }

  if (!progressData) {
    return (
      <div className="flex justify-center items-center h-64">
        <Text>No progress data available.</Text>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      <Breadcrumb className="mb-6">
        <Breadcrumb.Item>
          <Link href="/trainee/dashboard">
            <HomeOutlined /> Dashboard
          </Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>Progress Tracking</Breadcrumb.Item>
      </Breadcrumb>

      <Title level={2}>Progress Tracking</Title>
      <Text className="text-gray-500 mb-6 block">
        Track your progress across all practice areas and skills
      </Text>

      {/* Overall Progress */}
      <Card className="mb-6">
        <Row gutter={24}>
          <Col span={8}>
            <Statistic
              title="Overall Progress"
              value={Math.round(progressData.overallProgress)}
              suffix="%"
            />
            <Progress
              percent={Math.round(progressData.overallProgress)}
              status="active"
              className="mt-2"
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="Completed Skills"
              value={progressData.completedSkills}
              suffix={`/ ${progressData.totalSkills}`}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="Qualification Route"
              value={progressData.trainee.qualificationRoute || 'TC'}
            />
          </Col>
        </Row>
      </Card>

      {/* Practice Areas */}
      <Card className="mb-6">
        <Title level={4}>Practice Areas</Title>
        <Row gutter={[16, 16]} className="mt-4">
          {filteredPracticeAreas.map(area => (
            <Col span={8} key={area.id}>
              <Card>
                <Text strong>{area.name}</Text>
                <Progress
                  percent={Math.round(area.progress)}
                  size="small"
                  className="mt-2"
                />
                <div className="mt-2">
                  <Text>{area.completedSkills} / {area.totalSkills} skills completed</Text>
                </div>
                <div className="mt-2">
                  <Link href={`/trainee/progress/practice-area/${area.id}`}>
                    <Button type="link" size="small">View Details</Button>
                  </Link>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* Skills Table */}
      <Card>
        <Title level={4}>All Skills</Title>
        <Input.Search
          placeholder="Search skills..."
          style={{ marginBottom: 16, width: 300 }}
          value={searchText}
          onChange={e => setSearchText(e.target.value)}
          allowClear
        />
        <Tabs defaultActiveKey="all">
          <TabPane tab="All Skills" key="all">
            <Table
              dataSource={getFilteredSkillRows(filteredPracticeAreas)}
              columns={columns}
              pagination={getPaginationConfig(getFilteredSkillRows(filteredPracticeAreas).length)}
              onChange={handleTableChange}
              scroll={{ x: 'max-content' }}
            />
          </TabPane>
          <TabPane tab="Completed" key="completed">
            <Table
              dataSource={getFilteredSkillRows(filteredPracticeAreas, true)}
              columns={columns}
              pagination={getPaginationConfig(getFilteredSkillRows(filteredPracticeAreas, true).length)}
              onChange={handleTableChange}
              scroll={{ x: 'max-content' }}
            />
          </TabPane>
          <TabPane tab="In Progress" key="inProgress">
            <Table
              dataSource={getFilteredSkillRows(filteredPracticeAreas, false)}
              columns={columns}
              pagination={getPaginationConfig(getFilteredSkillRows(filteredPracticeAreas, false).length)}
              onChange={handleTableChange}
              scroll={{ x: 'max-content' }}
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
}
