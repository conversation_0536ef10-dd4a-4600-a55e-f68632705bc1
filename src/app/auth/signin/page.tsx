'use client';

import { useState, useEffect, Suspense } from 'react';
import { Form, Input, Button, Card, App } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { signIn, useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { useAuthStore } from '@/store/authStore';
import { UserRole } from '@/types';

function SignInForm() {
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { message } = App.useApp();
  const { data: session } = useSession();
  const setUser = useAuthStore((state) => state.setUser);
  const logout = useAuthStore((state) => state.logout);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);

  useEffect(() => {
    // Clear any existing auth state on mount
    logout();
  }, [logout]);

  useEffect(() => {
    if (session?.user || isAuthenticated) {
      // Check for callbackUrl first
      const callbackUrl = searchParams.get('callbackUrl');
      if (callbackUrl) {
        router.push(callbackUrl);
        return;
      }
      
      // Otherwise redirect to default role page
      const role = session?.user?.role;
      let redirectUrl = '/auth/signin';

      switch (role) {
        case UserRole.ADMIN:
          redirectUrl = '/admin/dashboard';
          break;
        case UserRole.SUPERVISOR:
          redirectUrl = '/supervisor/trainees';
          break;
        case UserRole.MENTOR:
          redirectUrl = '/mentor/mentees';
          break;
        case UserRole.TRAINEE:
          redirectUrl = '/trainee/dashboard';
          break;
      }

      router.push(redirectUrl);
    }
  }, [session, isAuthenticated, router, searchParams]);

  const onFinish = async (values: { email: string; password: string }) => {
    try {
      setLoading(true);
      const result = await signIn('credentials', {
        email: values.email,
        password: values.password,
        redirect: false,
      });

      if (result?.error) {
        message.error('Invalid credentials');
        return;
      }

      const response = await fetch('/api/auth/session');
      const sessionData = await response.json();
      
      if (sessionData?.user) {
        setUser(sessionData.user);
        message.success('Successfully signed in!');
        
        // Get the callback URL from searchParams or redirect based on role
        const callbackUrl = searchParams.get('callbackUrl');
        if (callbackUrl) {
          router.push(callbackUrl);
        } else {
          const role = sessionData.user.role;
          let redirectUrl = '/auth/signin';

          switch (role) {
            case UserRole.ADMIN:
              redirectUrl = '/admin/dashboard';
              break;
            case UserRole.SUPERVISOR:
              redirectUrl = '/supervisor/trainees';
              break;
            case UserRole.MENTOR:
              redirectUrl = '/mentor/mentees';
              break;
            case UserRole.TRAINEE:
              redirectUrl = '/trainee/dashboard';
              break;
          }

          router.push(redirectUrl);
        }
        router.refresh();
      } else {
        message.error('Failed to get user session');
        logout();
      }
    } catch (err) {
      console.error('Sign in error:', err);
      message.error('An error occurred during sign in');
      logout();
    } finally {
      setLoading(false);
    }
  };

  if (session?.user || isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen flex items-center justify-center relative">
      <div className="absolute inset-0 z-0">
        <Image
          src="/wall-papper.jpg"
          alt="Background"
          layout="fill"
          objectFit="cover"
          quality={100}
          priority
        />
      </div>
      <Card className="w-full max-w-md p-8 z-10 bg-white bg-opacity-90 shadow-xl">
        <div className="text-center mb-8">
          <Image
            src="https://admin.accutraineepw.com/images/logos/logo--dark.svg"
            alt="Logo"
            width={200}
            height={40}
            className="mx-auto"
          />
        </div>
        <Form
          name="signin"
          onFinish={onFinish}
          layout="vertical"
          size="large"
        >
          <Form.Item
            name="email"
            rules={[
              { required: true, message: 'Please input your email!' },
              { type: 'email', message: 'Please enter a valid email!' },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="Email"
              autoComplete="email"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[{ required: true, message: 'Please input your password!' }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="Password"
              autoComplete="current-password"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              className="w-full"
              loading={loading}
              style={{ backgroundColor: 'rgb(199, 100, 27)' }}
            >
              Sign in
            </Button>
          </Form.Item>

          <div className="text-center">
            <Link href="/forgot-password" className="text-blue-600 hover:text-blue-500">
              Forgot password?
            </Link>
          </div>
        </Form>
      </Card>
    </div>
  );
}

export default function SignIn() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SignInForm />
    </Suspense>
  );
} 