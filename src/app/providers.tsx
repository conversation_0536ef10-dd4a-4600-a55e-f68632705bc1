'use client';

import { SessionProvider } from 'next-auth/react';
import { ConfigProvider, theme } from 'antd';
import { useEffect } from 'react';
import { useAuthStore } from '@/store/authStore';

function AuthInitializer() {
  const initAuth = useAuthStore((state) => state.initAuth);

  useEffect(() => {
    initAuth();
  }, [initAuth]);

  return null;
}

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ConfigProvider
      theme={{
        algorithm: theme.defaultAlgorithm,
        token: {
          colorPrimary: 'rgb(199, 100, 27)',
          colorBgContainer: '#ffffff',
          colorBgLayout: '#ffffff',
          colorBgElevated: '#ffffff',
        },
        components: {
          Layout: {
            colorBgHeader: '#ffffff',
            colorBgBody: '#ffffff',
            colorBgSolid: '#ffffff',
          },
          Menu: {
            colorItemBg: '#ffffff',
            colorItemText: 'rgba(0, 0, 0, 0.88)',
            colorItemTextSelected: 'rgb(199, 100, 27)',
            colorItemBgSelected: '#fff1e6',
            colorItemBgHover: '#fff1e6',
          },
        },
      }}
    >
      <SessionProvider>
        <AuthInitializer />
        {children}
      </SessionProvider>
    </ConfigProvider>
  );
} 