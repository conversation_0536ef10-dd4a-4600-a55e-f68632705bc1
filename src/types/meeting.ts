import { FeedbackForm } from './feedback';
import { User } from './index';
import { MentorAvailability } from './index';

export enum MeetingType {
  MONTHLY = 'MONTHLY',
  TPM = 'TPM',
  APPRAISAL = 'APPRAISAL',
}

export enum MeetingStatus {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  DECLINED = 'DECLINED',
  COMPLETED = 'COMPLETED',
}

export enum MeetingFormat {
  IN_PERSON = 'IN_PERSON',
  VIRTUAL = 'VIRTUAL',
}

export interface Meeting {
  id: string;
  type: MeetingType;
  candidateId: string;
  candidate: User;
  mentorId: string;
  mentor: User;
  availabilityId?: string | null;
  availability?: MentorAvailability | null;
  status: MeetingStatus;
  proposedTime: Date;
  notes?: string | null;
  meetingUrl?: string | null;
  createdAt: Date;
  updatedAt: Date;
  monthlyReview?: MonthlyReview | null;
}

export interface MonthlyReview {
  id: string;
  documentName: string;
  completionDate: Date;
  action?: string | null;
  s3Key?: string | null;
  documentType: string;
  referredToTP: boolean;
  candidateId: string;
  mentorId?: string | null;
  FeedbackForm?: FeedbackForm | null;
}

export interface CompleteMeetingInput {
  documentName: string;
  action?: string;
  s3Key?: string;
  feedbackFormId?: string;
}

export interface CompleteMeetingResult {
  monthlyReview: {
    id: string;
    documentName: string;
    completionDate: Date;
    action?: string;
    s3Key?: string;
    documentType: string;
    referredToTP: boolean;
    candidateId: string;
    mentorId?: string;
  };
  meeting: Meeting;
} 