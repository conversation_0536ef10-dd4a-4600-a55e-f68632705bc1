// User role enum
export enum UserRole {
  ADMIN = 'ADMIN',
  SUPERVISOR = 'SUPERVISOR',
  MENTOR = 'MENTOR',
  TRAINEE = 'TRAINEE',
}

export type ExtendedUser = {
  id: string;
  name: string | null;
  email: string | null;
  role: UserRole;
  // Add other user fields as needed
};

export type DashboardStats = {
  totalPlacements: number;
  activePlacements: number;
  completedPlacements: number;
  pendingPlacements: number;
};

export type PlacementWithClient = {
  id: string;
  client: {
    id: string;
    name: string;
    // Add other client fields as needed
  };
  // Add other placement fields as needed
};

export interface User {
  id: string;
  email: string;
  name: string;
  image?: string;
  role: UserRole;
  password: string;
  createdAt: Date;
  updatedAt: Date;
  phoneNumber?: string;
  address?: string;
  qualificationRoute?: string;
  mentorId?: string;
  mentor?: User;
  mentees?: User[];
  traineeLevel?: number;
  isTrainingPrincipal?: boolean;
  placements?: Placement[];
  entries?: Entry[];
  qualificationRouteDismissed?: boolean;
  qualificationRouteUpdatedAt?: Date;
}

export interface Client {
  id: string;
  name: string;
  email: string;
  phone?: string;
  address?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Placement {
  id: string;
  name: string;
  startDate: Date;
  endDate?: Date;
  orgPosition?: string;
  orgSraNumber?: string;
  supervisorName: string;
  supervisorEmail: string;
  isFullTime: boolean;
  partTimeDays: number[];
  daysMissed: number;
  documentKey?: string;
  documentName?: string;
  clientId: string;
  client: Client;
  userId: string;
  user: User;
  supervisorId?: string;
  supervisor?: User;
  mentorId?: string;
  mentor?: User;
  entries: Entry[];
  createdAt: Date;
  updatedAt: Date;
}

export interface PlacementResponse {
  placements: {
    edges: Placement[];
    pageInfo: {
      total: number;
      offset: number;
      limit: number;
    }
  }
}

export interface Skill {
  id: string;
  name: string;
  description?: string;
  skillSetId: string;
  skillSet: SkillSet;
  createdAt: Date;
  updatedAt: Date;
}

export interface SkillSet {
  id: string;
  name: string;
  description?: string;
  type: string;
  skills: Skill[];
  createdAt: Date;
  updatedAt: Date;
}

export enum EntryStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  SIGNEDOFF = 'signedoff',
  REJECTED = 'rejected'
}

export type ContentiousType = 'contentious' | 'non_contentious' | 'both';

export interface PracticeSubSkill {
  id: string;
  name: string;
  practiceSubSkillType: 'sqe' | 'tc' | 'both';
  minSuggestedEntryCount: number;
  order: number;
  practiceSkillGroupId: string;
  practiceSkillGroup: PracticeSkillGroup;
  createdAt: Date;
}

export interface PracticeSkillGroup {
  id: string;
  name: string;
  practiceSkillId: string;
  practiceSkill: PracticeSkill;
  practiceSubSkills: PracticeSubSkill[];
  createdAt: Date;
}

export interface PracticeSkill {
  id: string;
  name: string;
  practiceAreaId: string;
  practiceArea: PracticeArea;
  practiceSkillGroups: PracticeSkillGroup[];
  createdAt: Date;
}

export interface EntrySubSkill {
  entryId: string;
  subSkillId: string;
  entry: Entry;
  subSkill: PracticeSubSkill;
}

export interface Entry {
  id: string;
  title: string;
  startDate: Date;
  endDate: Date;
  experience: string;
  evidence: string;
  learnt: string;
  moreExperience?: string;
  needMoreExperience?: string;
  contentiousType: ContentiousType;
  status: string;
  taskedBy?: string;
  documentKey?: string;
  documentName?: string;
  practiceAreas: string[];
  submittedAt?: Date;
  reviewedAt?: Date;
  feedback?: string;
  placementId: string;
  placement: Placement;
  entrySubSkills: EntrySubSkill[];
  reviewerId?: string;
  reviewer?: User;
  submissionId?: string;
  submission?: Submission;
  createdAt: Date;
  updatedAt: Date;

  // For backward compatibility
  description?: string;
}

export interface EntryFormData {
  title: string;
  startDate: Date;
  endDate: Date;
  experience: string;
  evidence: string;
  learnt: string;
  moreExperience?: string;
  needMoreExperience?: string;
  contentiousType: ContentiousType;
  taskedBy?: string;
  placementId: string;
  practiceAreas: string[];
  practiceSubSkillIds: string[];
  documentKey?: string;
  documentName?: string;
}

export interface EntryFilters {
  status?: EntryStatus[];
  dateRange?: [Date, Date];
  search?: string;
  practiceAreas?: string[];
  practiceSubSkillIds?: string[];
}

export interface PaginationParams {
  current: number;
  pageSize: number;
  total: number;
}

export interface Submission {
  id: string;
  title: string;
  startDate: Date;
  endDate: Date;
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: Date;
  entries: Entry[];
  traineeId: string;
  trainee: User;
  reviewerId?: string;
  reviewer?: User;
  supervisorId?: string;
  supervisor?: User;
  createdAt: Date;
  updatedAt: Date;
}

export interface PracticeArea {
  id: string;
  name: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TableRecord {
  id: string;
  key: string;
  name: string;
  progress: number;
  isCompleted: boolean;
  items?: TableRecord[];
}

export interface PortfolioSubSkill {
  id: string;
  name: string;
  doneEntryCount: number;
  minSuggestedEntryCount: number;
  progress: number;
  isCompleted: boolean;
}

export interface PortfolioSkillGroup {
  id: string;
  name: string;
  subSkills: PortfolioSubSkill[];
  progress: number;
  isCompleted: boolean;
}

export interface PortfolioSkill {
  id: string;
  name: string;
  groups: PortfolioSkillGroup[];
  progress: number;
  isCompleted: boolean;
}

export interface PortfolioPracticeArea {
  id: string;
  name: string;
  skills: PortfolioSkill[];
  progress: number;
  totalSkills: number;
  completedSkills: number;
}

export interface PortfolioProgressData {
  trainee: {
    name: string;
    email: string;
    qualificationRoute: string;
  };
  overallProgress: number;
  totalSkills: number;
  completedSkills: number;
  practiceAreas: PortfolioPracticeArea[];
}

export enum MeetingType {
  MONTHLY = 'MONTHLY',
  TPM = 'TPM',
  APPRAISAL = 'APPRAISAL',
}

export enum MeetingStatus {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  DECLINED = 'DECLINED',
  COMPLETED = 'COMPLETED',
}

export interface Meeting {
  id: string;
  type: MeetingType;
  candidateId: string;
  candidate: User;
  mentorId: string;
  mentor: User;
  availabilityId: string;
  availability: MentorAvailability;
  status: MeetingStatus;
  meetingUrl?: string;
  meetingLocation?: string;
  proposedTime: Date;
  notes: string;
  createdAt: Date;
  updatedAt: Date;
  monthlyReview?: {
    id: string;
    documentName: string;
    completionDate: Date;
    action?: string;
    s3Key?: string;
    documentType: string;
    referredToTP: boolean;
    candidateId: string;
    mentorId?: string;
    feedbackForm?: {
      id: string;
    } | null;
  } | null;
}

export enum MeetingFormat {
  IN_PERSON = 'IN_PERSON',
  VIRTUAL = 'VIRTUAL',
}

export interface MentorAvailability {
  id: string;
  mentorId: string;
  mentor: User;
  startTime: Date;
  endTime: Date;
  isBooked: boolean;
  meetingMethod: MeetingFormat;
  meetingLocation?: string;
  duration?: number;
  bufferTime?: number;
  meetingMessage?: string;
  createdAt: Date;
  updatedAt: Date;
}

export * from './meeting';

export interface Course {
  id: string;
  name: string;
  description: string;
  imageUrl?: string;
  externalLink: string;
  pricing?: string;
  categoryId: string;
  category: CourseCategory;
  secondCategories?: CourseSecondCategory[];
  courseClicks?: CourseClick[];
  _count?: {
    courseClicks?: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface CourseClick {
  id: string;
  userId: string;
  courseId: string;
  clickedAt: Date;
  ipAddress?: string;
  userAgent?: string;
  user?: User;
  course?: Course;
}

export interface CourseCategory {
  id: string;
  name: string;
  description?: string;
  color?: string;
  imageUrl?: string;
  isClassroom: boolean;
  isSecondCategory: boolean;
  faqs?: Array<{
    question: string;
    answer: string;
  }>;
  courses?: Course[];
  courseSecondCategories?: CourseSecondCategory[];
  createdAt: Date;
  updatedAt: Date;
}

export interface CourseSecondCategory {
  id: string;
  courseId: string;
  categoryId: string;
  course?: Course;
  category: CourseCategory;
  createdAt: Date;
}

export interface ExternalLink {
  id: string;
  title: string;
  url: string;
  description?: string;
}

export interface CompetencyPlus {
  id: string;
  title: string;
  shortDescription: string;
  content: string;
  externalLinks?: ExternalLink[];
  audioUrl?: string;
  videoUrl?: string;
  bannerImageUrl?: string;
  published: boolean;
  publishedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}
