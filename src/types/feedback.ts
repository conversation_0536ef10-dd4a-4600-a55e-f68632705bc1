import { User } from './index';

export enum DocumentType {
  MONTHLY_REVIEW = 'MONTHLY_REVIEW',
  FEEDBACK_FORM = 'FEEDBACK_FORM',
  OTHER = 'OTHER',
}

export interface MonthlyReview {
  id: string;
  documentName: string;
  completionDate: Date;
  action?: string;
  s3Key?: string;
  documentType: DocumentType;
  referredToTP: boolean;
  candidateId: string;
  mentorId?: string;
  createdAt: Date;
  updatedAt: Date;
  candidate: User;
  mentor?: User;
  feedbackForm?: FeedbackForm;
}

export interface FeedbackForm {
  id: string;
  monthlyReviewId: string;
  monthlyReview: MonthlyReview;
  coveringMonth: string;
  menteeName: string;
  meetingDate: Date;
  pointsToDiscuss?: string;
  wasGoodWork: boolean;
  goodWorkComments?: string;
  hasFeltResponsible: boolean;
  responsibilityComments?: string;
  workConcerns?: string;
  isWorkingWellSupervisor: boolean;
  workingWellSupervisorComments?: string;
  hadInformalMonthlyFeedback: boolean;
  informalMonthlyFeedbackComments?: string;
  skillStandardsComments?: string;
  anyConcerns?: string;
  furtherTraining?: string;
  appraisalMeeting?: string;
  qualifyingArea?: string;
  mentorNotes?: string;
  furtherAction?: string;
  pointsToNote?: string;
  signatureKey?: string;
  signatureName?: string;
  createdAt: Date;
  updatedAt: Date;
} 