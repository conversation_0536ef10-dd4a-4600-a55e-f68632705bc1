import { UserRole } from '@prisma/client'
import 'next-auth'

declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      email: string
      name: string
      role: UserRole
      qualificationRoute?: string
      qualificationRouteDismissed?: boolean
      qualificationRouteUpdatedAt?: Date | string
      mentorId?: string
      mentor?: {
        id: string
        name: string
        email: string
      }
      isImpersonated?: boolean
      impersonationId?: string
      impersonatedBy?: {
        id: string
        name: string
        email: string
      }
      image?: string
      phoneNumber?: string
      address?: string
      isTrainingPrincipal?: boolean
      traineeLevel?: number
    }
  }

  interface User {
    id: string
    email: string
    name: string
    role: UserRole
    qualificationRoute?: string
    qualificationRouteDismissed?: boolean
    qualificationRouteUpdatedAt?: Date | string
    mentorId?: string
    mentor?: {
      id: string
      name: string
      email: string
    }
    isImpersonated?: boolean
    impersonationId?: string
    impersonatedBy?: {
      id: string
      name: string
      email: string
    }
    image?: string
    phoneNumber?: string
    address?: string
    isTrainingPrincipal?: boolean
    traineeLevel?: number
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    qualificationRoute?: string
    qualificationRouteDismissed?: boolean
    qualificationRouteUpdatedAt?: Date | string
    id: string
    role: UserRole
    mentorId?: string
    mentor?: {
      id: string
      name: string
      email: string
    }
    isImpersonated?: boolean
    impersonationId?: string
    impersonatedBy?: {
      id: string
      name: string
      email: string
    }
    name: string
    email: string
    image?: string
    phoneNumber?: string
    address?: string
    isTrainingPrincipal?: boolean
    traineeLevel?: number
  }
} 