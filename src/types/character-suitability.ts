export interface ExternalLink {
  title: string;
  url: string;
  description?: string;
}

export interface CharacterSuitability {
  id: string;
  rules: string;
  infoForQualifiedSolicitors: string;
  externalLinks: ExternalLink[];
  createdAt: Date;
  updatedAt: Date;
  userCompletions: CharacterSuitabilityCompletion[];
}

export interface CharacterSuitabilityCompletion {
  id: string;
  completed: boolean;
  completedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  characterSuitabilityId: string;
  user?: {
    id: string;
    name: string | null;
    email: string | null;
    skillGroupType: string | null;
  };
}

export interface CompletionStatus {
  characterSuitabilityId: string;
  completed: boolean;
} 