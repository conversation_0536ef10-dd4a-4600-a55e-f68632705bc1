/**
 * Strip HTML tags and markdown syntax from text
 */
export function stripHtmlAndMarkdown(text: string): string {
  if (!text) return '';
  
  return text
    // Remove HTML tags
    .replace(/<[^>]*>/g, '')
    // Remove markdown image syntax ![alt](url)
    .replace(/!\[([^\]]*)\]\([^)]*\)/g, '')
    // Remove markdown link syntax [text](url)
    .replace(/\[([^\]]*)\]\([^)]*\)/g, '$1')
    // Remove markdown bold **text** and __text__
    .replace(/\*\*([^*]+)\*\*/g, '$1')
    .replace(/__([^_]+)__/g, '$1')
    // Remove markdown italic *text* and _text_
    .replace(/\*([^*]+)\*/g, '$1')
    .replace(/_([^_]+)_/g, '$1')
    // Remove markdown code `code`
    .replace(/`([^`]+)`/g, '$1')
    // Remove markdown headers ### ## #
    .replace(/^#{1,6}\s+/gm, '')
    // Remove markdown blockquotes >
    .replace(/^>\s+/gm, '')
    // Remove markdown list markers - * +
    .replace(/^[-*+]\s+/gm, '')
    // Remove numbered list markers 1. 2. etc
    .replace(/^\d+\.\s+/gm, '')
    // Remove extra whitespace and newlines
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Truncate text to specified length with ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
}

/**
 * Strip HTML/markdown and truncate text
 */
export function cleanAndTruncateText(text: string, maxLength: number = 150): string {
  const cleaned = stripHtmlAndMarkdown(text);
  return truncateText(cleaned, maxLength);
} 