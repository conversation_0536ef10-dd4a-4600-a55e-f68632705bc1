/**
 * Creates a debounced function that delays invoking the provided function
 * until after `wait` milliseconds have elapsed since the last time it was invoked.
 * 
 * @param func The function to debounce
 * @param wait The number of milliseconds to delay
 * @returns A debounced version of the provided function
 */
export function debounce<F extends (...params: Parameters<F>) => ReturnType<F>>(
  fn: F,
  delay: number
) {
  let timeoutId: ReturnType<typeof setTimeout> | undefined;

  return function(this: unknown, ...args: Parameters<F>) {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    
    timeoutId = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  } as F;
}

export default debounce;
