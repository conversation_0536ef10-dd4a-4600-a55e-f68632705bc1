import { withAuth } from 'next-auth/middleware';
import { NextResponse } from 'next/server';
import { UserRole } from '@/types';

export default withAuth(
  function middleware(req) {
    const token = req.nextauth.token;
    const isAuth = !!token;
    const isAuthPage = req.nextUrl.pathname.startsWith('/auth');
    const isRootPath = req.nextUrl.pathname === '/';

    // If user is authenticated and tries to access auth pages or root, redirect based on role
    if (isAuth && (isAuthPage || isRootPath)) {
      const role = token.role as UserRole;
      let redirectUrl = '/auth/signin';

      switch (role) {
        case UserRole.ADMIN:
          redirectUrl = '/admin/dashboard';
          break;
        case UserRole.SUPERVISOR:
          redirectUrl = '/supervisor/trainees';
          break;
        case UserRole.MENTOR:
          redirectUrl = '/mentor/mentees';
          break;
        case UserRole.TRAINEE:
          redirectUrl = '/trainee/dashboard';
          break;
      }

      return NextResponse.redirect(new URL(redirectUrl, req.url));
    }

    // If user is not authenticated and tries to access protected pages, redirect to signin
    if (!isAuth && !isAuthPage) {
      let callbackUrl = req.nextUrl.pathname;
      if (req.nextUrl.search) {
        callbackUrl += req.nextUrl.search;
      }

      const signInUrl = new URL('/auth/signin', req.url);
      signInUrl.searchParams.set('callbackUrl', callbackUrl);
      
      const response = NextResponse.redirect(signInUrl);
      response.headers.set(
        'Set-Cookie',
        'next-auth.session-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
      );
      
      return response;
    }

    // Handle role-based access
    if (req.nextUrl.pathname.startsWith('/admin') && token?.role !== UserRole.ADMIN) {
      return NextResponse.redirect(new URL('/supervisor/trainees', req.url));
    }

    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // Allow access to auth pages without authentication
        if (req.nextUrl.pathname.startsWith('/auth')) {
          return true;
        }
        // Require authentication for all other protected routes
        return !!token;
      },
    },
  }
);

export const config = {
  matcher: [
    '/',
    '/admin/:path*',
    '/supervisor/:path*',
    '/mentor/:path*',
    '/trainee/:path*',
    '/auth/:path*'
  ],
}; 