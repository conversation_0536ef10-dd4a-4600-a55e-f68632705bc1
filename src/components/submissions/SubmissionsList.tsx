'use client';

import { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  message, 
  Typography, 
  Tag, 
  Space, 
  Breadcrumb, 
  Tooltip, 
  Input,
  App
} from 'antd';
import { 
  PlusOutlined, 
  HomeOutlined, 
  FileTextOutlined, 
  EyeOutlined, 
  RollbackOutlined,
  CheckOutlined,
  CloseOutlined,
  SearchOutlined 
} from '@ant-design/icons';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { UserRole } from '@/types';
import dayjs from 'dayjs';
import { viewDocument } from '@/lib/utils';

const { Title, Text } = Typography;

interface Entry {
  id: string;
  documentKey?: string;
  documentName?: string;
}

interface Submission {
  id: string;
  status: string;
  startDate: string;
  endDate: string;
  createdAt: string;
  trainee: {
    id: string;
    name: string;
  };
  reviewer?: {
    id: string;
    name: string;
  };
  entries: Entry[];
}

interface SubmissionsListProps {
  role: UserRole;
  title?: string;
  showCreateButton?: boolean;
  showWithdrawButton?: boolean;
  showApproveRejectButtons?: boolean;
}

export default function SubmissionsList({ 
  role, 
  title = 'Submissions',
  showCreateButton = false,
  showWithdrawButton = false,
  showApproveRejectButtons = false
}: SubmissionsListProps) {
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [actionId, setActionId] = useState<string | null>(null);
  const router = useRouter();
  const { modal } = App.useApp();

  useEffect(() => {
    fetchSubmissions();
  }, []);

  const fetchSubmissions = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/submissions');
      if (!response.ok) throw new Error('Failed to fetch submissions');
      const data = await response.json();
      setSubmissions(data.submissions.edges);
    } catch (error) {
      message.error('Failed to load submissions');
      console.error('Error fetching submissions:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'pending':
        return <Tag color="processing">Pending</Tag>;
      case 'approved':
        return <Tag color="success">Approved</Tag>;
      case 'rejected':
        return <Tag color="error">Rejected</Tag>;
      default:
        return <Tag color="default">{status}</Tag>;
    }
  };

  const handleWithdraw = async (id: string) => {
    try {
      setActionId(id);
      const response = await fetch(`/api/submissions/${id}/withdraw`, {
        method: 'POST',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to withdraw submission');
      }
      
      message.success('Submission withdrawn successfully');
      fetchSubmissions();
    } catch (error) {
      message.error(error instanceof Error ? error.message : 'Failed to withdraw submission');
    } finally {
      setActionId(null);
    }
  };

  const handleApprove = async (id: string) => {
    try {
      const response = await fetch(`/api/submissions/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'approved',
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to approve submission');
      }

      message.success('Submission approved successfully');
      fetchSubmissions();
    } catch (error) {
      message.error(error instanceof Error ? error.message : 'Failed to approve submission');
    }
  };

  const handleReject = async (id: string, feedback: string) => {
    try {
      const response = await fetch(`/api/submissions/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'rejected',
          feedback: feedback,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to reject submission');
      }

      message.success('Submission rejected successfully');
      fetchSubmissions();
    } catch (error) {
      message.error(error instanceof Error ? error.message : 'Failed to reject submission');
    }
  };

  const showWithdrawConfirm = (id: string) => {
    modal.confirm({
      title: 'Withdraw Submission',
      content: 'Are you sure you want to withdraw this submission? All entries will be returned to draft status.',
      okText: 'Withdraw',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk() {
        return handleWithdraw(id);
      },
    });
  };

  const showRejectConfirm = (id: string) => {
    let modalFeedback = '';

    modal.confirm({
      title: 'Reject Submission',
      content: (
        <div className="space-y-4">
          <p>Are you sure you want to reject this submission?</p>
          <Input.TextArea
            placeholder="Please provide feedback for the rejection..."
            onChange={(e) => {
              modalFeedback = e.target.value;
            }}
            rows={4}
          />
        </div>
      ),
      okText: 'Reject',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk() {
        // if (!modalFeedback.trim()) {
        //   message.warning('Please provide feedback for the rejection');
        //   return Promise.reject();
        // }
        return handleReject(id, modalFeedback);
      },
    });
  };

  const filteredSubmissions = submissions.filter(submission => 
    submission.id.toLowerCase().includes(searchText.toLowerCase()) ||
    (submission.reviewer?.name || '').toLowerCase().includes(searchText.toLowerCase()) ||
    submission.trainee.name.toLowerCase().includes(searchText.toLowerCase())
  );

  const columns = [
    // {
    //   title: 'ID',
    //   dataIndex: 'id',
    //   key: 'id',
    //   render: (text: string) => <Text copyable>{text}</Text>,
    // },
    {
      title: 'Trainee',
      dataIndex: ['trainee', 'name'],
      key: 'trainee',
    },
    {
      title: 'Date Range',
      key: 'dateRange',
      render: (_: unknown, record: Submission) => (
        <Space direction="vertical" size={0}>
          <Text>{dayjs(record.startDate).format('DD/MM/YYYY')}</Text>
          <Text>{dayjs(record.endDate).format('DD/MM/YYYY')}</Text>
        </Space>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: 'Entries',
      key: 'entries',
      render: (_: unknown, record: Submission) => (
        <Text>{record.entries.length} entries</Text>
      ),
    },
    {
      title: 'Document',
      key: 'document',
      render: (_: unknown, record: Submission) => {
        const entry = record.entries[0];
        if (!entry?.documentKey) return 'No document';
        
        return (
          <a onClick={() => entry.documentKey && viewDocument(entry.documentKey)}>
            {entry.documentName || entry.documentKey}
          </a>
        );
      },
    },
    {
      title: 'Reviewer',
      key: 'reviewer',
      render: (_: unknown, record: Submission) => (
        <Text>{record.reviewer?.name || 'Not assigned'}</Text>
      ),
    },
    {
      title: 'Submitted',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => dayjs(date).format('DD/MM/YYYY HH:mm'),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: unknown, record: Submission) => (
        <Space>
          <Tooltip title="View">
            <Button 
              icon={<EyeOutlined />} 
              size="small" 
              onClick={() => window.open(`/signoff-generator/${record.id}`, '_blank')}
              // onClick={() => router.push(`/signoff-generator/${record.id}`)}
            />
          </Tooltip>
          {showWithdrawButton && record.status === 'pending' && (
            <Tooltip title="Withdraw">
              <Button 
                icon={<RollbackOutlined />} 
                size="small" 
                danger
                loading={actionId === record.id}
                onClick={() => showWithdrawConfirm(record.id)}
              />
            </Tooltip>
          )}
          {showApproveRejectButtons && record.status === 'pending' && (
            <>
              <Tooltip title="Approve">
                <Button 
                  icon={<CheckOutlined />} 
                  size="small" 
                  type="primary"
                  loading={actionId === record.id}
                  onClick={() => handleApprove(record.id)}
                />
              </Tooltip>
              <Tooltip title="Reject">
                <Button 
                  icon={<CloseOutlined />} 
                  size="small" 
                  danger
                  loading={actionId === record.id}
                  onClick={() => showRejectConfirm(record.id)}
                />
              </Tooltip>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-6 p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link href="/">
                <HomeOutlined /> Home
              </Link>
            ),
          },
          {
            title: (
              <>
                <FileTextOutlined /> {title}
              </>
            ),
          },
        ]}
      />

      <div className="flex justify-between items-center">
        <Title level={2}>{title}</Title>
        {showCreateButton && (
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => router.push(`/${role.toLowerCase()}/submissions/new`)}
          >
            Create Submission
          </Button>
        )}
      </div>

      <Card>
        <div className="mb-4">
          <Input
            placeholder="Search submissions..."
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={e => setSearchText(e.target.value)}
            allowClear
          />
        </div>

        <Table 
          scroll={{ x: 'max-content' }}
          columns={columns} 
          dataSource={filteredSubmissions} 
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
        />
      </Card>
    </div>
  );
}
