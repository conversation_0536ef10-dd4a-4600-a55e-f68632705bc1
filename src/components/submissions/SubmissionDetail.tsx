'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Breadcrumb,
  Spin,
  Descriptions,
  Button,
  Table,
  Tag,
  Space,
  Modal,
  Input,
  Tooltip,
  Row
} from 'antd';
import {
  HomeOutlined,
  FileTextOutlined,
  EyeOutlined,
  RollbackOutlined,
  CheckOutlined,
  CloseOutlined
} from '@ant-design/icons';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { EntrySubSkill, UserRole } from '@/types';
import dayjs from 'dayjs';
import { App } from 'antd';
import { Entry } from '@/types';
import { viewDocument } from '@/lib/utils';
const { Title, Text } = Typography;

interface Submission {
  id: string;
  title: string;
  status: string;
  startDate: string;
  endDate: string;
  createdAt: string;
  trainee: {
    id: string;
    name: string;
    email: string;
  };
  reviewer?: {
    id: string;
    name: string;
    email: string;
  };
  entries: Entry[];
}

interface SubmissionDetailProps {
  role: UserRole;
  submissionId: string;
  showWithdrawButton?: boolean;
  showApproveRejectButtons?: boolean;
}

export default function SubmissionDetail({
  role,
  submissionId,
  showWithdrawButton = false,
  showApproveRejectButtons = false
}: SubmissionDetailProps) {
  const [submission, setSubmission] = useState<Submission | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const router = useRouter();
  const { modal, message } = App.useApp();

  useEffect(() => {
    fetchSubmission();
  }, [submissionId]);

  const fetchSubmission = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/submissions/${submissionId}`);
      if (!response.ok) {
        if (response.status === 404) {
          message.error('Submission not found');
          router.push(`/${role.toLowerCase()}/submissions`);
          return;
        }
        throw new Error('Failed to fetch submission');
      }
      const data = await response.json();
      setSubmission(data);
    } catch (error) {
      message.error('Failed to load submission');
      console.error('Error fetching submission:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'draft':
        return <Tag color="default">Draft</Tag>;
      case 'submitted':
        return <Tag color="processing">Submitted</Tag>;
      case 'signedoff':
        return <Tag color="success">Signed Off</Tag>;
      case 'rejected':
        return <Tag color="error">Rejected</Tag>;
      case 'pending':
        return <Tag color="processing">Pending</Tag>;
      case 'approved':
        return <Tag color="success">Approved</Tag>;
      default:
        return <Tag color="default">{status}</Tag>;
    }
  };

  const handleWithdraw = async () => {
    try {
      setActionLoading(true);
      const response = await fetch(`/api/submissions/${submissionId}/withdraw`, {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to withdraw submission');
      }

      message.success('Submission withdrawn successfully');
      router.push(`/${role.toLowerCase()}/submissions`);
    } catch (error) {
      message.error(error instanceof Error ? error.message : 'Failed to withdraw submission');
    } finally {
      setActionLoading(false);
    }
  };

  const handleApprove = async () => {
    try {
      setActionLoading(true);
      const response = await fetch(`/api/submissions/${submissionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'approved',
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to approve submission');
      }

      message.success('Submission approved successfully');
      fetchSubmission();
    } catch (error) {
      message.error(error instanceof Error ? error.message : 'Failed to approve submission');
    } finally {
      setActionLoading(false);
    }
  };

  const handleReject = async (feedback: string) => {
    try {
      setActionLoading(true);
      const response = await fetch(`/api/submissions/${submissionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'rejected',
          feedback: feedback,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to reject submission');
      }

      message.success('Submission rejected successfully');
      fetchSubmission();
    } catch (error) {
      message.error(error instanceof Error ? error.message : 'Failed to reject submission');
    } finally {
      setActionLoading(false);
    }
  };

  const showWithdrawConfirm = () => {
    Modal.confirm({
      title: 'Withdraw Submission',
      content: 'Are you sure you want to withdraw this submission? All entries will be returned to draft status.',
      okText: 'Withdraw',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk() {
        return handleWithdraw();
      },
    });
  };

  const showRejectConfirm = () => {
    let modalFeedback = '';

    modal.confirm({
      title: 'Reject Submission',
      content: (
        <div className="space-y-4">
          <p>Are you sure you want to reject this submission?</p>
          <Input.TextArea
            placeholder="Please provide feedback for the rejection..."
            onChange={(e) => {
              modalFeedback = e.target.value;
            }}
            rows={4}
          />
        </div>
      ),
      okText: 'Reject',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk() {
        // if (!modalFeedback.trim()) {
        //   message.warning('Please provide feedback for the rejection');
        //   return Promise.reject();
        // }
        // setFeedback(modalFeedback);
        return handleReject(modalFeedback);
      },
    });
  };

  const columns = [
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      width: 250,
      render: (text: string, record: Entry) => (
        <Space direction="vertical" size={0}>
          <Text strong>{text}</Text>
          <Text type="secondary" className="text-sm">
            {dayjs(record.startDate).format('DD/MM/YYYY')} - {dayjs(record.endDate).format('DD/MM/YYYY')}
          </Text>
        </Space>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => getStatusTag(status),
    },
    {
      title: 'Skills',
      key: 'skills',
      width: 200,
      render: (_: unknown, record: Entry) => (
        <Space wrap>
          {record.entrySubSkills && record.entrySubSkills.length > 0 ? (
            record.entrySubSkills.map((item: EntrySubSkill) => (
              <Tooltip key={item.subSkill.id} title={item.subSkill.name}>
                <Tag color="blue">{item.subSkill.name}</Tag>
              </Tooltip>
            ))
          ) : (
            <Text type="secondary">None</Text>
          )}
        </Space>
      ),
    },
    {
      title: 'Feedback',
      key: 'feedback',
      width: 250,
      render: (_: unknown, record: Entry) => (
        <div>
          {record.feedback ? (
            <Text>{record.feedback}</Text>
          ) : (
            <Text type="secondary">No feedback yet</Text>
          )}
        </div>
      ),
    },
    {
      title: 'Document',
      key: 'documentKey',
      width: 100,
      render: (_: unknown, record: Entry) => (
        record.documentKey ? (
          <Button type="link" icon={<EyeOutlined />} onClick={() => viewDocument(record.documentKey || '')}>View</Button>
        ) : (
          <Text type="secondary">No document</Text>
        )
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 100,
      fixed: 'right' as const,
      render: (_: unknown, record: Entry) => (
        <Button
          type="link"
          icon={<EyeOutlined />}
          onClick={() => window.open(`/entries/${record.id}`, '_blank')}
        >
          View
        </Button>
      ),
    },
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (!submission) {
    return (
      <div className="p-6">
        <Card>
          <div className="text-center py-8">
            <Title level={4}>Submission not found</Title>
            <Button type="primary" onClick={() => router.push(`/${role.toLowerCase()}/submissions`)}>
              Back to Submissions
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link href="/">
                <HomeOutlined /> Home
              </Link>
            ),
          },
          {
            title: (
              <Link href={`/${role.toLowerCase()}/submissions`}>
                <FileTextOutlined /> Submissions
              </Link>
            ),
          },
          {
            title: (
              <>
                <FileTextOutlined /> Submission Details
              </>
            ),
          },
        ]}
      />

      <div className="flex justify-between items-center">
        <Title level={2}>Submission Details</Title>
        <Space>
          {showWithdrawButton && submission.status === 'pending' && (
            <Button
              type="primary"
              danger
              icon={<RollbackOutlined />}
              loading={actionLoading}
              onClick={showWithdrawConfirm}
            >
              Withdraw Submission
            </Button>
          )}
          {(showApproveRejectButtons || role === UserRole.SUPERVISOR) && submission.status === 'pending' && (
            <>
              <Button
                type="primary"
                icon={<CheckOutlined />}
                loading={actionLoading}
                onClick={handleApprove}
                className="bg-green-500 hover:bg-green-600"
              >
                Approve
              </Button>
              <Button
                danger
                icon={<CloseOutlined />}
                loading={actionLoading}
                onClick={showRejectConfirm}
              >
                Reject
              </Button>
            </>
          )}
        </Space>
      </div>

      <Row>
        <Card className="w-full mb-6!" title="Submission Information">
          <Descriptions column={1} size="small" bordered>
            <Descriptions.Item label="Title">{submission.title}</Descriptions.Item>
            <Descriptions.Item label="Status">{getStatusTag(submission.status)}</Descriptions.Item>
            <Descriptions.Item label="Submitted On">{dayjs(submission.createdAt).format('DD/MM/YYYY HH:mm')}</Descriptions.Item>
            <Descriptions.Item label="Date Range">
              {dayjs(submission.startDate).format('DD/MM/YYYY')} - {dayjs(submission.endDate).format('DD/MM/YYYY')}
            </Descriptions.Item>
            <Descriptions.Item label="Entries">
              {submission.entrySubmissions?.length || submission.entries.length}
            </Descriptions.Item>
            <Descriptions.Item label="Trainee">{submission.trainee.name}</Descriptions.Item>
            <Descriptions.Item label="Reviewer">{submission.reviewer?.name || 'Not assigned'}</Descriptions.Item>
          </Descriptions>
        </Card>
        
        <Card 
          title="Entries"
          className="w-full"
          extra={
            <Text type="secondary">
              Total: {submission.entrySubmissions?.length || submission.entries.length} {(submission.entrySubmissions?.length || submission.entries.length) === 1 ? 'entry' : 'entries'}
            </Text>
          }
        >
          <Table
            columns={columns}
            dataSource={submission.entrySubmissions?.map(es => es.entry) || submission.entries}
            rowKey="id"
            pagination={false}
            scroll={{ x: 'max-content' }}
            size="middle"
          />
        </Card>
      </Row>
    </div>
  );
} 