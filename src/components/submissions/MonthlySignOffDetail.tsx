'use client';

import { UserRole } from '@/types';
import SubmissionDetail from './SubmissionDetail';

interface MonthlySignOffDetailProps {
  submissionId: string;
}

export default function MonthlySignOffDetail({ submissionId }: MonthlySignOffDetailProps) {

  return (
    <div className="">
      <SubmissionDetail 
        role={UserRole.SUPERVISOR} 
        submissionId={submissionId} 
        showApproveRejectButtons={true}
      />
    </div>
  );
} 