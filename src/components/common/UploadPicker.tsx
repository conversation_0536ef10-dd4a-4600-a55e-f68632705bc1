import { Upload, Button } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd/es/upload/interface';

interface UploadPickerProps {
  title: string;
  acceptType?: string;
  onSelected: (file: UploadFile) => void;
}

export default function UploadPicker({ title, acceptType, onSelected }: UploadPickerProps) {
  const handleChange = (info: any) => {
    if (info.file.status === 'done') {
      onSelected(info.file);
    }
  };

  return (
    <Upload
      accept={acceptType}
      onChange={handleChange}
      maxCount={1}
      customRequest={({ file, onSuccess }) => {
        setTimeout(() => {
          onSuccess?.(file);
        }, 0);
      }}
    >
      <Button icon={<UploadOutlined />}>{title}</Button>
    </Upload>
  );
} 