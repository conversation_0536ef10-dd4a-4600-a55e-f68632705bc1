'use client';

import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import { Spin } from 'antd';

const MDEditor = dynamic(() => import('@uiw/react-md-editor'), {
  ssr: false,
  loading: () => <Spin size="large" />
});

interface QuillEditorProps {
  value: string;
  onChange: (value: string) => void;
  style?: React.CSSProperties;
  placeholder?: string;
}

export default function QuillEditor({
  value,
  onChange,
  style,
  placeholder = "Enter content..."
}: QuillEditorProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return (
      <div style={style || { height: '300px' }} className="flex items-center justify-center border border-gray-300 rounded">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div style={style}>
      <style dangerouslySetInnerHTML={{
        __html: `
          .w-md-editor-preview,
          .w-md-editor-preview .wmde-markdown,
          .w-md-editor-preview .wmde-markdown p {
            white-space: pre-wrap !important;
            word-wrap: break-word !important;
            color: #000 !important;
          }
          .w-md-editor-preview .wmde-markdown img {
            background-color: #fff !important;
          }
        `
      }} />
      <MDEditor
        value={value}
        onChange={(val) => onChange(val || '')}
        preview="edit"
        hideToolbar={false}
        visibleDragbar={false}
        data-color-mode="light"
        textareaProps={{
          placeholder: placeholder,
          style: {
            fontSize: 14,
            lineHeight: 1.6,
            whiteSpace: 'pre-wrap',
            wordWrap: 'break-word'
          }
        }}
      />
    </div>
  );
} 