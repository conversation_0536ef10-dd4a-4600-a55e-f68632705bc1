'use client';

import dynamic from 'next/dynamic';
import VideoRenderer from './VideoRenderer';

const MDViewer = dynamic(() => import('@uiw/react-md-editor').then(mod => mod.default.Markdown), {
  ssr: false,
  loading: () => <div className="p-4">Loading content...</div>
});

interface ContentRendererProps {
  content: string;
}

export default function ContentRenderer({ content }: ContentRendererProps) {
  // Extract video elements and replace with placeholders
  const processContent = (text: string) => {
    const videoRegex = /<video[^>]*>[\s\S]*?<\/video>/gi;
    const videos: Array<{ src: string; type?: string; title?: string }> = [];
    
    // Extract video information
    let match;
    while ((match = videoRegex.exec(text)) !== null) {
      const videoHtml = match[0];
      const srcMatch = videoHtml.match(/src="([^"]*)"/);
      const typeMatch = videoHtml.match(/type="([^"]*)"/);
      
      if (srcMatch && srcMatch[1].trim() !== '') {
        videos.push({
          src: srcMatch[1],
          type: typeMatch?.[1],
          title: `Video ${videos.length + 1}`
        });
      }
    }
    
    // Replace video tags with placeholders
    const processedContent = text.replace(videoRegex, (match) => {
      const videoIndex = videos.findIndex((_, i) => match.includes(videos[i].src));
      return `\n\n__VIDEO_PLACEHOLDER_${videoIndex}__\n\n`;
    });
    
    return { processedContent, videos };
  };

  const { processedContent, videos } = processContent(content);

  // Render content with video components
  const renderContentWithVideos = () => {
    const parts = processedContent.split(/__VIDEO_PLACEHOLDER_(\d+)__/);
    const elements: React.ReactNode[] = [];

    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];
      
      if (part && !part.match(/^\d+$/)) {
        // Regular markdown content
        elements.push(
          <div key={`content-${i}`}>
            <MDViewer source={part} />
          </div>
        );
      } else if (part && part.match(/^\d+$/)) {
        // Video placeholder
        const videoIndex = parseInt(part);
        const video = videos[videoIndex];
        if (video) {
          elements.push(
            <VideoRenderer
              key={`video-${i}`}
              src={video.src}
              type={video.type}
              title={video.title}
            />
          );
        }
      }
    }

    return elements;
  };

  return (
    <div className="content-renderer">
      <style dangerouslySetInnerHTML={{
        __html: `
          .content-renderer .wmde-markdown,
          .content-renderer .wmde-markdown p,
          .content-renderer .w-md-editor-preview,
          .content-renderer .w-md-editor-preview .wmde-markdown,
          .content-renderer .w-md-editor-preview .wmde-markdown p {
            white-space: pre-wrap !important;
            word-wrap: break-word !important;
            color: #000 !important;
          }
          .content-renderer .wmde-markdown img {
            background-color: #fff !important;
          }
        `
      }} />
      {videos.length > 0 ? renderContentWithVideos() : <MDViewer source={content} />}
    </div>
  );
} 