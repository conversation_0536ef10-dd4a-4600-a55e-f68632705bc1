'use client';

import { useEffect, useState, useRef } from 'react';
import dynamic from 'next/dynamic';
import { Spin, Upload, Button, App } from 'antd';
import { PictureOutlined, VideoCameraOutlined, LoadingOutlined } from '@ant-design/icons';

const MDEditor = dynamic(() => import('@uiw/react-md-editor'), {
  ssr: false,
  loading: () => <Spin size="large" />
});

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  style?: React.CSSProperties;
}

export default function RichTextEditor({
  value,
  onChange,
  placeholder = "Enter description...",
  style
}: RichTextEditorProps) {
  const [isClient, setIsClient] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [uploadingVideo, setUploadingVideo] = useState(false);
  const [cursorPosition, setCursorPosition] = useState(0);
  const editorRef = useRef<any>(null);
  const { message } = App.useApp();


  useEffect(() => {
    setIsClient(true);
  }, []);

  // Function to insert content at cursor position
  const insertAtCursor = (newContent: string) => {
    console.log('Insert at cursor - Stored position:', cursorPosition);
    console.log('Current value length:', value.length, 'Content to insert:', newContent.length);
    
    // Use the stored cursor position (captured in beforeUpload)
    const currentPosition = cursorPosition;
    
    const before = value.substring(0, currentPosition);
    const after = value.substring(currentPosition);
    const newValue = before + newContent + after;
    
    console.log('Before text (last 30 chars):', JSON.stringify(before.substring(Math.max(0, before.length - 30))));
    console.log('After text (first 30 chars):', JSON.stringify(after.substring(0, 30)));
    
    onChange(newValue);
    
    // Update cursor position to end of inserted content
    const newCursorPos = currentPosition + newContent.length;
    setCursorPosition(newCursorPos);
    
    // Focus back to textarea and set cursor position
    setTimeout(() => {
      // Try multiple selectors to find textarea
      const selectors = [
        '.w-md-editor-text-area',
        '.w-md-editor-text textarea',
        '.w-md-editor textarea',
        'textarea'
      ];
      
      let textarea: HTMLTextAreaElement | null = null;
      for (const selector of selectors) {
        textarea = document.querySelector(selector) as HTMLTextAreaElement;
        if (textarea) break;
      }
      
      if (textarea) {
        textarea.focus();
        textarea.setSelectionRange(newCursorPos, newCursorPos);
        console.log('Focused textarea and set cursor to:', newCursorPos);
      } else {
        console.log('Could not find textarea to set cursor position');
      }
    }, 100);
  };

  // Handle textarea focus/selection change to track cursor position
  const handleTextareaChange = (val: string | undefined) => {
    onChange(val || '');
  };

  // Update cursor position from textarea events
  const updateCursorPosition = () => {
    const textarea = document.querySelector('.w-md-editor-text-area') as HTMLTextAreaElement;
    if (textarea) {
      const position = textarea.selectionStart || 0;
      console.log('Updating cursor position:', position, 'Current value length:', value.length);
      setCursorPosition(position);
    }
  };

  const handleImageUpload = async (file: File) => {
    console.log('Image upload starting - Using stored position:', cursorPosition);
    setUploadingImage(true);
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', 'courses'); // Sử dụng folder courses để có public access
      
      const response = await fetch('/api/image-upload', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) throw new Error('Upload failed');
      
      const data = await response.json();
      if (!data.success) throw new Error('Upload failed');
      
      const imageMarkdown = `\n\n![${file.name}](${data.url})\n\n`;
      insertAtCursor(imageMarkdown);
      
      message.success('Image uploaded successfully');
    } catch (error) {
      console.error('Upload error:', error);
      message.error('Failed to upload image');
    } finally {
      setUploadingImage(false);
    }
  };

  const handleVideoUpload = async (file: File) => {
    console.log('Video upload starting - Using stored position:', cursorPosition);
    setUploadingVideo(true);
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', 'courses'); // Sử dụng folder courses để có public access
      
      const response = await fetch('/api/image-upload', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) throw new Error('Upload failed');
      
      const data = await response.json();
      if (!data.success) throw new Error('Upload failed');
      
      // Use both markdown link and HTML video for better compatibility
      const videoMarkdown = `\n\n<video controls width="100%" style="max-height: 400px;">\n  <source src="${data.url}" type="${file.type}"></video>\n\n`;
      insertAtCursor(videoMarkdown);
      
      message.success('Video uploaded successfully');
    } catch (error) {
      console.error('Upload error:', error);
      message.error('Failed to upload video');
    } finally {
      setUploadingVideo(false);
    }
  };

  if (!isClient) {
    return (
      <div style={style || { height: '300px' }} className="flex items-center justify-center border border-gray-300 rounded">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div style={style}>
      <style dangerouslySetInnerHTML={{
        __html: `
          .w-md-editor-preview,
          .w-md-editor-preview .wmde-markdown,
          .w-md-editor-preview .wmde-markdown p {
            white-space: pre-wrap !important;
            word-wrap: break-word !important;
            color: #000 !important;
          }
          .w-md-editor-preview .wmde-markdown img {
            background-color: #fff !important;
          }
        `
      }} />
      <div className="mb-2 flex gap-2">
        <Upload
          showUploadList={false}
          beforeUpload={(file) => {
            // Try multiple selectors to find the textarea
            const selectors = [
              '.w-md-editor-text-area',
              '.w-md-editor-text textarea',
              '.w-md-editor textarea',
              'textarea'
            ];
            
            let textarea: HTMLTextAreaElement | null = null;
            let position = 0;
            
            for (const selector of selectors) {
              textarea = document.querySelector(selector) as HTMLTextAreaElement;
              if (textarea) {
                position = textarea.selectionStart || 0;
                console.log(`Found textarea with selector: ${selector}, position: ${position}`);
                break;
              }
            }
            
            if (!textarea) {
              console.log('No textarea found, trying all textareas');
              const allTextareas = document.querySelectorAll('textarea');
              console.log('Total textareas found:', allTextareas.length);
              if (allTextareas.length > 0) {
                textarea = allTextareas[0] as HTMLTextAreaElement;
                position = textarea.selectionStart || 0;
                console.log('Using first textarea, position:', position);
              }
            }
            
            setCursorPosition(position);
            
            // Validate image file
            if (!file.type.startsWith('image/')) {
              message.error('Please select an image file');
              return false;
            }
            if (file.size > 10 * 1024 * 1024) { // 10MB limit
              message.error('Image file must be smaller than 10MB');
              return false;
            }
            
            // Use setTimeout to ensure position is captured before upload
            setTimeout(() => handleImageUpload(file), 0);
            return false;
          }}
          accept="image/*"
          disabled={uploadingImage || uploadingVideo}
        >
          <Button 
            icon={uploadingImage ? <LoadingOutlined /> : <PictureOutlined />} 
            size="small"
            loading={uploadingImage}
            disabled={uploadingImage || uploadingVideo}
            onMouseDown={(e) => {
              // Prevent button from stealing focus
              e.preventDefault();
              
              // Ensure textarea stays focused and capture position
              const selectors = [
                '.w-md-editor-text-area',
                '.w-md-editor-text textarea',
                '.w-md-editor textarea',
                'textarea'
              ];
              
              let textarea: HTMLTextAreaElement | null = null;
              for (const selector of selectors) {
                textarea = document.querySelector(selector) as HTMLTextAreaElement;
                if (textarea) {
                  textarea.focus();
                  const position = textarea.selectionStart || 0;
                  console.log(`Image Button mousedown - Found textarea with ${selector}, position: ${position}`);
                  setCursorPosition(position);
                  break;
                }
              }
            }}
          >
            {uploadingImage ? 'Uploading...' : 'Add Image'}
          </Button>
        </Upload>
        <Upload
          showUploadList={false}
          beforeUpload={(file) => {
            // Try multiple selectors to find the textarea
            const selectors = [
              '.w-md-editor-text-area',
              '.w-md-editor-text textarea',
              '.w-md-editor textarea',
              'textarea'
            ];
            
            let textarea: HTMLTextAreaElement | null = null;
            let position = 0;
            
            for (const selector of selectors) {
              textarea = document.querySelector(selector) as HTMLTextAreaElement;
              if (textarea) {
                position = textarea.selectionStart || 0;
                console.log(`Video - Found textarea with selector: ${selector}, position: ${position}`);
                break;
              }
            }
            
            if (!textarea) {
              console.log('Video - No textarea found, trying all textareas');
              const allTextareas = document.querySelectorAll('textarea');
              console.log('Video - Total textareas found:', allTextareas.length);
              if (allTextareas.length > 0) {
                textarea = allTextareas[0] as HTMLTextAreaElement;
                position = textarea.selectionStart || 0;
                console.log('Video - Using first textarea, position:', position);
              }
            }
            
            setCursorPosition(position);
            
            // Validate video file
            if (!file.type.startsWith('video/')) {
              message.error('Please select a video file');
              return false;
            }
            if (file.size > 50 * 1024 * 1024) { // 50MB limit
              message.error('Video file must be smaller than 50MB');
              return false;
            }
            
            // Use setTimeout to ensure position is captured before upload
            setTimeout(() => handleVideoUpload(file), 0);
            return false;
          }}
          accept="video/*"
          disabled={uploadingImage || uploadingVideo}
        >
          <Button 
            icon={uploadingVideo ? <LoadingOutlined /> : <VideoCameraOutlined />} 
            size="small"
            loading={uploadingVideo}
            disabled={uploadingImage || uploadingVideo}
            onMouseDown={(e) => {
              // Prevent button from stealing focus
              e.preventDefault();
              
              // Ensure textarea stays focused and capture position
              const selectors = [
                '.w-md-editor-text-area',
                '.w-md-editor-text textarea',
                '.w-md-editor textarea',
                'textarea'
              ];
              
              let textarea: HTMLTextAreaElement | null = null;
              for (const selector of selectors) {
                textarea = document.querySelector(selector) as HTMLTextAreaElement;
                if (textarea) {
                  textarea.focus();
                  const position = textarea.selectionStart || 0;
                  console.log(`Button mousedown - Found textarea with ${selector}, position: ${position}`);
                  setCursorPosition(position);
                  break;
                }
              }
            }}
          >
            {uploadingVideo ? 'Uploading...' : 'Add Video'}
          </Button>
        </Upload>
      </div>
      
      {/* Upload Status Indicator */}
      {(uploadingImage || uploadingVideo) && (
        <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center gap-2 text-blue-600">
            <LoadingOutlined className="text-lg" />
            <span className="font-medium">
              {uploadingImage && 'Uploading image...'}
              {uploadingVideo && 'Uploading video (this may take a while)...'}
            </span>
          </div>
          <div className="text-blue-500 text-sm mt-1">
            Please wait while your file is being uploaded and processed.
          </div>
        </div>
      )}
      
      <MDEditor
        ref={editorRef}
        value={value}
        onChange={handleTextareaChange}
        preview="live"
        hideToolbar={false}
        visibleDragbar={false}
        data-color-mode="light"
        height={Math.max(300, Math.min(600, (value?.split('\n').length || 10) * 24 + 100))}
        textareaProps={{
          placeholder: placeholder,
          style: {
            fontSize: 14,
            lineHeight: 1.6,
            whiteSpace: 'pre-wrap',
            wordWrap: 'break-word'
          },
          onSelect: updateCursorPosition,
          onClick: updateCursorPosition,
          onKeyUp: updateCursorPosition,
          onFocus: updateCursorPosition
        }}
        style={{ opacity: (uploadingImage || uploadingVideo) ? 0.7 : 1 }}

      />
    </div>
  );
}