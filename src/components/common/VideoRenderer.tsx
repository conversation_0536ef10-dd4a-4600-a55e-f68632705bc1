'use client';

import { useState } from 'react';

interface VideoRendererProps {
  src: string;
  type?: string;
  title?: string;
}

export default function VideoRenderer({ src, type, title }: VideoRendererProps) {
  const [error, setError] = useState(false);
  const [loading, setLoading] = useState(true);
  const [videoSrc, setVideoSrc] = useState(src);
  const [retryCount, setRetryCount] = useState(0);

  if (!src || src.trim() === '') {
    return (
      <div className="my-4 p-4 bg-gray-100 border border-gray-300 rounded-lg text-center">
        <div className="text-gray-600 mb-2">No video source provided</div>
        <div className="text-sm text-gray-500">This video cannot be displayed</div>
      </div>
    );
  }

  const handleLoadStart = () => setLoading(true);
  const handleCanPlay = () => setLoading(false);
  const handleError = async () => {
    console.log('Video load error:', { src: videoSrc, retryCount, originalSrc: src });
    
    // First retry: If using public URL and fails, try signed URL
    if (retryCount === 0 && videoSrc.startsWith('http') && !videoSrc.includes('X-Amz-')) {
      try {
        // Extract the S3 key from public URL
        const key = videoSrc.split('.amazonaws.com/')[1];
        if (key) {
          const response = await fetch(`/api/get-signed-url?key=${encodeURIComponent(key)}`);
          const data = await response.json();
          if (data.url) {
            console.log('Retrying with signed URL:', data.url);
            setVideoSrc(data.url);
            setRetryCount(1);
            return;
          }
        }
      } catch (e) {
        console.error('Failed to get signed URL:', e);
      }
    }
    
    // Second retry: If src doesn't start with http, try to get signed URL
    if (retryCount === 0 && !src.startsWith('http')) {
      try {
        const response = await fetch(`/api/get-signed-url?key=${encodeURIComponent(src)}`);
        const data = await response.json();
        if (data.url) {
          console.log('Trying signed URL for key:', src);
          setVideoSrc(data.url);
          setRetryCount(1);
          return;
        }
      } catch (e) {
        console.error('Failed to get signed URL:', e);
      }
    }
    
    setError(true);
    setLoading(false);
  };

  return (
    <div className="my-4 relative">
      {title && (
        <div className="mb-2 text-sm font-medium text-gray-700">
          🎥 {title}
        </div>
      )}
      
      {error ? (
        <div className="p-4 bg-gray-100 border border-gray-300 rounded-lg text-center">
          <div className="text-gray-600 mb-2">
            Video could not be loaded {retryCount > 0 ? '(after retry)' : ''}
          </div>
          <div className="text-sm text-gray-500 mb-2">
            This might be due to CORS or permissions issues
          </div>
          <a 
            href={videoSrc} 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-blue-600 hover:text-blue-800 underline"
          >
            Open video in new tab
          </a>
        </div>
      ) : (
        <>
          {loading && (
            <div className="absolute inset-0 bg-gray-100 rounded-lg flex items-center justify-center">
              <div className="text-gray-500">Loading video...</div>
            </div>
          )}
          <video
            controls
            style={{ 
              width: '100%', 
              maxHeight: '400px',
              borderRadius: '8px'
            }}
            onLoadStart={handleLoadStart}
            onCanPlay={handleCanPlay}
            onError={handleError}
          >
            <source src={videoSrc} type={type || 'video/mp4'} />
            <p>
              Your browser does not support the video tag. 
              <a href={videoSrc} target="_blank" rel="noopener noreferrer">
                Open video in new tab
              </a>
            </p>
          </video>
        </>
      )}
    </div>
  );
} 