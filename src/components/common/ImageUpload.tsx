import React, { useState } from 'react';
import { Upload, App, Spin } from 'antd';
import { UploadOutlined, LoadingOutlined, PlusOutlined, CloseCircleFilled } from '@ant-design/icons';
import type { UploadProps } from 'antd/es/upload/interface';
import { S3_FOLDERS } from '@/lib/s3';

interface ImageUploadProps {
  value?: string;
  onChange?: (value: string) => void;
  folder?: string;
  aspectRatio?: number;
  maxSize?: number; // in MB
  className?: string;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  value,
  onChange,
  folder = S3_FOLDERS.CATEGORIES,
  aspectRatio = 16/9,
  maxSize = 2,
  className = '',
}) => {
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>();
  const { message } = App.useApp();
  React.useEffect(() => {
    if (value) {
      setImageUrl(value);
    }
  }, [value]);

  const handleRemove = () => {
    setImageUrl(undefined);
    onChange?.('');
  };

  const uploadProps: UploadProps = {
    name: 'file',
    accept: 'image/*',
    multiple: false,
    showUploadList: false,
    action: '/api/image-upload',
    data: {
      folder,
    },
    beforeUpload: (file) => {
      const isImage = file.type.startsWith('image/');
      if (!isImage) {
        message.error('You can only upload image files!');
        return false;
      }
      const isLessThanMax = file.size / 1024 / 1024 < maxSize;
      if (!isLessThanMax) {
        message.error(`Image must be smaller than ${maxSize}MB!`);
        return false;
      }
      return true;
    },
    onChange: async (info) => {
      if (info.file.status === 'uploading') {
        setLoading(true);
        return;
      }
      if (info.file.status === 'done') {
        setLoading(false);
        const { url, success } = info.file.response;
        
        if (!success) {
          message.error('Upload failed');
          return;
        }

        setImageUrl(url);
        onChange?.(url);
      }
      if (info.file.status === 'error') {
        setLoading(false);
        message.error('Upload failed. Please try again.');
      }
    }
  };

  return (
    <div className={`flex flex-col items-center ${className} bg-white`}>
      <div 
        className="relative w-full rounded-lg overflow-hidden bg-gray-50 border-2 border-dashed border-gray-300 hover:border-blue-500 transition-colors"
        style={{ paddingTop: `${100 / aspectRatio}%` }}
      >
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-50 z-50">
            <Spin indicator={<LoadingOutlined style={{ fontSize: 24, color: 'black' }} spin />} />
          </div>
        )}
        {imageUrl ? (
          <div className="absolute inset-0">
            <div className="relative w-full h-full">
              <img
                src={imageUrl}
                alt="Preview"
                className="absolute inset-0 w-full h-full object-contain"
              />
              {/* Remove button */}
              <button
                onClick={handleRemove}
                className="absolute top-1 right-1 text-gray-600 hover:text-red-500 transition-colors z-10"
                type="button"
              >
                <CloseCircleFilled className="text-lg" />
              </button>
            </div>
            {/* Change button overlay */}
            <div className="absolute inset-0 bg-white bg-opacity-0 hover:bg-opacity-30 transition-opacity flex items-center justify-center opacity-0 hover:opacity-100">
              <Upload {...uploadProps}>
                <button
                  type="button"
                  className="p-2 rounded-full bg-white bg-opacity-90 hover:bg-opacity-100 border-2 border-gray-300 transition-all transform hover:scale-110 cursor-pointer"
                >
                  <UploadOutlined className="text-xl" />
                </button>
              </Upload>
            </div>
          </div>
        ) : (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-white">
            <Upload {...uploadProps}>
              <div className="cursor-pointer text-center p-8">
                {loading ? <LoadingOutlined className="text-2xl mb-2" /> : <PlusOutlined className="text-2xl mb-2" />}
                <div className="mt-2">Upload Image</div>
                <div className="text-xs text-gray-500 mt-1">
                  Click or drag file here
                </div>
              </div>
            </Upload>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImageUpload; 