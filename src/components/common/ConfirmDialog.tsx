import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Space, Typography, Divider, Alert } from 'antd';
import { ExclamationCircleOutlined, DeleteOutlined } from '@ant-design/icons';

const { Text, Title } = Typography;

interface DeleteDetails {
  [key: string]: number | string;
}

interface ConfirmDialogProps {
  open: boolean;
  onCancel: () => void;
  onConfirm: () => void;
  loading?: boolean;
  title: string;
  warning: string;
  details?: DeleteDetails;
  itemName?: string;
  type: 'practice-skill' | 'practice-skill-group' | 'practice-sub-skill';
}

export default function ConfirmDialog({
  open,
  onCancel,
  onConfirm,
  loading = false,
  title,
  warning,
  details,
  itemName,
  type
}: ConfirmDialogProps) {
  const getImpactSummary = () => {
    if (!details) return null;

    const impactItems = [];
    
    if (type === 'practice-skill') {
      if (details.skillGroups) impactItems.push(`${details.skillGroups} skill groups`);
      if (details.subSkills) impactItems.push(`${details.subSkills} sub-skills`);
      if (details.entries) impactItems.push(`${details.entries} entries`);
      if (details.trainees) impactItems.push(`${details.trainees} trainees`);
    } else if (type === 'practice-skill-group') {
      if (details.subSkills) impactItems.push(`${details.subSkills} sub-skills`);
      if (details.entries) impactItems.push(`${details.entries} entries`);
      if (details.trainees) impactItems.push(`${details.trainees} trainees`);
    } else if (type === 'practice-sub-skill') {
      if (details.entries) impactItems.push(`${details.entries} entries`);
      if (details.trainees) impactItems.push(`${details.trainees} trainees`);
    }

    return impactItems.length > 0 ? impactItems.join(', ') : null;
  };

  const impactSummary = getImpactSummary();

  return (
    <Modal
      open={open}
      onCancel={onCancel}
      footer={null}
      width={600}
      centered
      destroyOnClose
    >
      <div className="p-4">
        <div className="flex items-center gap-3 mb-4">
          <ExclamationCircleOutlined className="text-red-500 text-2xl" />
          <Title level={4} className="mb-0 text-red-600">
            {title}
          </Title>
        </div>

        <Alert
          message="Warning"
          description="This action cannot be undone!"
          type="warning"
          showIcon
          className="mb-4"
        />

        <div className="mb-4">
          <Text strong className="text-base">
            {warning}
          </Text>
        </div>

        {impactSummary && (
          <div className="bg-gray-50 p-4 rounded-lg mb-4">
            <Text strong className="text-gray-700">
              This will affect:
            </Text>
            <div className="mt-2">
              <Text className="text-gray-600">
                {impactSummary}
              </Text>
            </div>
          </div>
        )}

        {itemName && (
          <div className="mb-4">
            <Text strong>Item to be deleted: </Text>
            <Text code className="bg-gray-100 px-2 py-1 rounded">
              {itemName}
            </Text>
          </div>
        )}

        <Divider />

        <div className="flex justify-end gap-3">
          <Button onClick={onCancel} disabled={loading}>
            Cancel
          </Button>
          <Button
            type="primary"
            danger
            icon={<DeleteOutlined />}
            onClick={onConfirm}
            loading={loading}
          >
            Delete Permanently
          </Button>
        </div>
      </div>
    </Modal>
  );
}
