'use client';

import { Table, Input, Al<PERSON>, Button, Tag } from 'antd';
import type { TableProps } from 'antd';
import { SearchOutlined, EyeOutlined } from '@ant-design/icons';
import { useEffect } from 'react';
import dayjs from 'dayjs';
import { useRouter } from 'next/navigation';
import type { Placement } from '@/types';
import { usePlacementStore } from '@/store/placementStore';

export default function PlacementList() {
  const router = useRouter();
  const {
    placements,
    loading,
    error,
    pageInfo,
    fetchPlacements,
    setSearchQuery,
    searchQuery
  } = usePlacementStore();

  useEffect(() => {
    fetchPlacements({});
  }, [fetchPlacements]);

  const columns: TableProps<Placement>['columns'] = [
    {
      title: 'Name',
      dataIndex: 'name',
      sorter: true,
      width: '15%',
    },
    // {
    //   title: 'Client',
    //   dataIndex: 'clientName',
    //   width: '10%',
    //   render: (clientName: string) => clientName || '',
    // },
    {
      title: 'Supervisor',
      dataIndex: ['supervisor', 'name'],
      key: 'supervisor',
      render: (text: string) => text || 'Not assigned',
    },
    {
      title: 'Mentor',
      dataIndex: ['mentor', 'name'],
      key: 'mentor',
      render: (text: string) => text || 'Not assigned',
    },
    {
      title: 'Start Date',
      dataIndex: 'startDate',
      sorter: true,
      width: '10%',
      render: (date: string) => dayjs(date).format('DD/MM/YYYY'),
    },
    {
      title: 'End Date',
      dataIndex: 'endDate',
      width: '10%',
      render: (date?: string) => date ? dayjs(date).format('DD/MM/YYYY') : 'Ongoing',
    },
    {
      title: 'Status',
      width: '10%',
      dataIndex: 'status',
      render: (status: string) => {
        let color = 'blue';
        if (status === 'COMPLETED') color = 'green';
        if (status === 'PENDING') color = 'orange';
        if (status === 'CANCELLED') color = 'red';
        if (status === 'ACTIVE') color = 'blue';
        return <Tag color={color}>{status?.toUpperCase() || ''}</Tag>;
      },
    },
    // {
    //   title: 'Schedule',
    //   width: '10%',
    //   render: (_, record: Placement) => (
    //     <Text>
    //       {record.isFullTime ? 'Full Time' : 'Part Time'}
    //       {!record.isFullTime && record.partTimeDays && (
    //         <Text type="secondary" style={{ display: 'block' }}>
    //           {record.partTimeDays.join(', ')}
    //         </Text>
    //       )}
    //     </Text>
    //   ),
    // },
    // {
    //   title: 'QWE Document',
    //   width: '10%',
    //   render: (_, record: Placement) => (
    //     <Space>
    //       <Badge
    //         status={record.documentKey ? 'success' : 'default'}
    //         text={record.documentKey ? 'Uploaded' : 'Not Uploaded'}
    //       />
    //     </Space>
    //   ),
    // },
    {
      title: 'Actions',
      width: '10%',
      render: (_, record: Placement) => (
        <Button
          type="primary"
          icon={<EyeOutlined />}
          size="small"
          onClick={() => router.push(`/trainee/placements/${record.id}`)}
        >
          View
        </Button>
      ),
    },
  ];

  const handleSearch = (value: string) => {
    setSearchQuery(value);
    fetchPlacements({ search: value });
  };

  if (error) {
    return <Alert message={error} type="error" />;
  }

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Input.Search
          placeholder="Search placements..."
          allowClear
          enterButton
          onSearch={handleSearch}
          style={{ maxWidth: 400 }}
          prefix={<SearchOutlined />}
        />
      </div>

      <Table
        columns={columns}
        dataSource={placements}
        rowKey="id"
        loading={loading}
        pagination={{
          current: Math.floor(pageInfo.offset / pageInfo.limit) + 1,
          total: pageInfo.total,
          pageSize: pageInfo.limit,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} placements`,
          onChange: (page, pageSize) => {
            fetchPlacements({
              offset: (page - 1) * pageSize,
              limit: pageSize,
              search: searchQuery
            });
          }
        }}
        scroll={{ x: 'max-content' }}
      />
    </div>
  );
}
