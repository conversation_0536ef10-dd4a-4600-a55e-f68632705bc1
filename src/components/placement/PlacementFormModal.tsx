'use client';

import { useState, useEffect, useRef } from 'react';
import { Modal, Form, Input, DatePicker, Spin, App, Switch, Button } from 'antd';
import { UploadOutlined, DeleteOutlined } from '@ant-design/icons';
import type { Placement } from '@/types';
import dayjs from 'dayjs';
import OrganisationNameInput from './OrganisationNameInput';
import { useSession } from 'next-auth/react';

interface PlacementFormModalProps {
  open: boolean;
  onCancel: () => void;
  onSubmit: (placement: Partial<Placement>) => Promise<void>;
  placement?: Partial<Placement>;
  loading?: boolean;
}

export default function PlacementFormModal({
  open,
  onCancel,
  onSubmit,
  placement,
  loading: parentLoading = false,
}: PlacementFormModalProps) {
  const { data: session } = useSession();
  const qualificationRoute = session?.user?.qualificationRoute || 'TC';
  console.log('placement', placement);
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [isFullTime, setIsFullTime] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [documentKey, setDocumentKey] = useState<string | null>(null);
  const [documentName, setDocumentName] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const isLoading = parentLoading || isSubmitting;
  const isEditing = !!placement?.id;

  useEffect(() => {
    if (placement) {
      const fullTimeValue = placement.isFullTime !== undefined ? placement.isFullTime : true;
      form.setFieldsValue({
        ...placement,
        orgName: placement?.client?.name || placement?.name,
        startDate: placement.startDate ? dayjs(placement.startDate) : undefined,
        endDate: placement.endDate ? dayjs(placement.endDate) : undefined,
        isFullTime: fullTimeValue,
      });
      
      if (placement.documentKey) {
        setDocumentKey(placement.documentKey);
        setDocumentName(placement.documentName || 'Document');
        setSelectedFile(new File([], placement.documentName || 'Document'));
      }
      setIsFullTime(fullTimeValue);
    } else {
      form.resetFields();
      form.setFieldValue('isFullTime', true);
      setIsFullTime(true);
      setSelectedFile(null);
      setDocumentKey(null);
      setDocumentName(null);
    }
  }, [placement, form, open]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      
      const isValidType = [
        'application/pdf',
        'image/jpeg',
        'image/png',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ].includes(file.type);

      if (!isValidType) {
        message.error('You can only upload PDF, JPG, PNG, DOC, or DOCX files!');
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        return;
      }

      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('File must be smaller than 10MB!');
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        return;
      }

      setSelectedFile(file);
      setDocumentName(file.name);
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    setDocumentName(null);
    setDocumentKey(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const uploadFile = async (file: File): Promise<{key: string, name: string} | null> => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('entityType', 'placement');
      formData.append('entityId', placement?.id || 'new');
      formData.append('isPublic', 'false');

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to upload file');
      }

      const data = await response.json();
      return {
        key: data.file.s3Key,
        name: data.file.name
      };
    } catch (error) {
      console.error('Error uploading file:', error);
      message.error('Failed to upload document');
      return null;
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setIsSubmitting(true);
      
      let placementDocumentKey = documentKey;
      let placementDocumentName = documentName;

      if (selectedFile && !documentKey) {
        message.loading('Uploading document...', 0);
        
        const uploadResult = await uploadFile(selectedFile);
        
        message.destroy();
        
        if (uploadResult) {
          placementDocumentKey = uploadResult.key;
          placementDocumentName = uploadResult.name;
          message.success('Document uploaded successfully');
        } else {
          const continueWithoutFile = window.confirm('Failed to upload document. Do you want to continue without the document?');
          if (!continueWithoutFile) {
            setIsSubmitting(false);
            return;
          }
        }
      }
      
      const formattedValues = {
        ...values,
        startDate: values.startDate?.toISOString(),
        endDate: values.endDate?.toISOString(),
        documentKey: placementDocumentKey,
        documentName: placementDocumentName,
        ...(placement?.id && { id: placement.id })
      };

      await onSubmit(formattedValues);
      onCancel();
    } catch (error) {
      console.error('Error submitting form:', error);
      message.error(error instanceof Error ? error.message : 'Failed to save placement');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal
      title={isEditing ? 'Edit Placement' : 'Add Placement'}
      open={open}
      onOk={handleSubmit}
      onCancel={onCancel}
      confirmLoading={isLoading}
      width={600}
      destroyOnClose
      styles={{
        body: {
          maxHeight: '70vh',
          overflow: 'auto',
          paddingRight: 10
        }
      }}
      style={{ top: 20 }}
    >
      <Spin spinning={isLoading}>
        <Form form={form} layout="vertical" className="pr-2">
          <Form.Item
            name="orgName"
            label="Organisation Name"
            rules={[{ required: true, message: 'Please enter organisation name' }]}
          >
            <OrganisationNameInput placeholder="Enter or select organisation name" />
          </Form.Item>

          <Form.Item
            name="orgSraNumber"
            label="Organisation SRA Number (if applicable)"
          >
            <Input placeholder="Enter SRA Number" />
          </Form.Item>

          <Form.Item
            name="startDate"
            label="Start Date"
            rules={[{ required: true, message: 'Please select a start date!' }]}
          >
            <DatePicker className="w-full" />
          </Form.Item>

          <Form.Item
            name="endDate"
            label="End Date"
          >
            <DatePicker className="w-full" />
          </Form.Item>

          <Form.Item
            name="isFullTime"
            label="Is the placement*"
            rules={[{ required: true, message: 'Please select the placement type' }]}
            valuePropName="checked"
          >
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium">Part Time</span>
              <Switch 
                className="bg-gray-300"
                checkedChildren=""
                unCheckedChildren=""
                checked={isFullTime}
                onChange={(checked) => {
                  setIsFullTime(checked);
                  form.setFieldValue('isFullTime', checked);
                }}
              />
              <span className="text-sm font-medium">Full Time</span>
            </div>
          </Form.Item>

          <Form.Item
            name="supervisorName"
            label="Name of Supervisor"
            rules={[{ required: true, message: 'Please enter supervisor name' }]}
          >
            <Input placeholder="Enter supervisor name" />
          </Form.Item>

          <Form.Item
            name="supervisorEmail"
            label="Supervisor's Email Address"
            rules={[
              { required: true, message: 'Please enter supervisor email' },
              { type: 'email', message: 'Please enter a valid email' }
            ]}
          >
            <Input placeholder="Enter supervisor email" type="email" />
          </Form.Item>

          {qualificationRoute === 'SQE' && (
            <>
              <Form.Item
                name="qweConfirmingPerson"
                label="Individual confirming your QWE? (They must be a qualified solicitor of England and Wales)"
              >
                <Input placeholder="Enter the name of the qualified solicitor" />
              </Form.Item>
              <Form.Item
                name="qweSraNumber"
                label="SRA Number"
              >
                <Input placeholder="Enter the SRA Number" />
              </Form.Item>
              <Form.Item
                name="qweEmail"
                label="Email Address"
                rules={[
                  { type: 'email', message: 'Please enter a valid email' }
                ]}
              >
                <Input placeholder="Enter the email address" type="email" />
              </Form.Item>
            </>
          )}

          <div className="mb-4">
            <div className="font-medium mb-2">
              {qualificationRoute === 'SQE' ? 'Confirmed Qualifying Work Experience' : 'Confirmed Qualifying Work Experience (if applicable)'}
            </div>
            <div>
              <div className="flex items-center space-x-2">
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                  id="document-upload"
                  className="hidden!"
                />
                <Button 
                  icon={<UploadOutlined />}
                  onClick={() => fileInputRef.current?.click()}
                >
                  {selectedFile ? 'Replace Document' : 'Upload Document'}
                </Button>
                <Button
                  type="default"
                  onClick={() => window.open('https://pathways-dev-uploads.s3.eu-west-1.amazonaws.com/import/sqe_qwe_form.pdf', '_blank')}
                >
                  Download Form
                </Button>
                {selectedFile && (
                  <div className="flex items-center">
                    <span className="ml-2">{documentName || selectedFile.name}</span>
                    <Button 
                      type="text" 
                      danger 
                      icon={<DeleteOutlined />} 
                      onClick={handleRemoveFile}
                      className="ml-1"
                      disabled={isSubmitting}
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        </Form>
      </Spin>
    </Modal>
  );
} 