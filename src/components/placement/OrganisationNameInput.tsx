import { useState, useEffect } from 'react';
import { AutoComplete, Input } from 'antd';
import { clientName } from '@/constants/clientName';
import { Client } from '@/types';

interface OrganisationNameInputProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
}

export default function OrganisationNameInput({ value, onChange, placeholder }: OrganisationNameInputProps) {
  const [options, setOptions] = useState<{ value: string }[]>([]);
  const [existingClients, setExistingClients] = useState<string[]>([]);

  useEffect(() => {
    const fetchClients = async () => {
      try {
        const response = await fetch('/api/clients');
        if (response.ok) {
          const clients = await response.json();
          setExistingClients(clients.map((client: Client) => client.name));
        }
      } catch (error) {
        console.error('Error fetching clients:', error);
      }
    };
    fetchClients();
  }, []);

  const onSearch = (searchText: string) => {
    const allSuggestions = [...new Set([...clientName, ...existingClients])];
    
    const filtered = allSuggestions
      .filter(name => 
        name.toLowerCase().includes(searchText.toLowerCase())
      )
      .map(name => ({ value: name }));
      
    setOptions(filtered);
  };

  return (
    <AutoComplete
      value={value}
      options={options}
      onSearch={onSearch}
      onChange={onChange}
      style={{ width: '100%' }}
    >
      <Input placeholder={placeholder} />
    </AutoComplete>
  );
} 