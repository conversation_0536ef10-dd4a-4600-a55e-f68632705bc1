'use client';

import { useState, useEffect } from 'react';
import { 
  Badge, 
  Dropdown, 
  List, 
  Typography, 
  Button, 
  Empty, 
  Spin, 
  message 
} from 'antd';
import { BellOutlined, CheckOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

const { Text } = Typography;

interface Notification {
  id: string;
  title: string;
  message: string;
  type: string;
  url?: string;
  isRead: boolean;
  createdAt: string;
}

export default function NotificationBell() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);

  useEffect(() => {
    fetchUnreadCount();
  }, []);

  useEffect(() => {
    if (dropdownOpen) {
      fetchNotifications();
    }
  }, [dropdownOpen]);

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/notifications?limit=5');
      if (!response.ok) throw new Error('Failed to fetch notifications');
      const data = await response.json();
      setNotifications(data.notifications.edges);
      setUnreadCount(data.notifications.pageInfo.unreadCount);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchUnreadCount = async () => {
    try {
      const response = await fetch('/api/notifications/unread-count');
      if (!response.ok) throw new Error('Failed to fetch unread count');
      const data = await response.json();
      setUnreadCount(data.unreadCount);
    } catch (error) {
      console.error('Error fetching unread count:', error);
    }
  };

  const markAsRead = async (id?: string) => {
    try {
      const body = id 
        ? { ids: [id] } 
        : { markAll: true };
      
      const response = await fetch('/api/notifications', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });
      
      if (!response.ok) throw new Error('Failed to mark notifications as read');
      
      if (id) {
        // Update local state for single notification
        setNotifications(prev => 
          prev.map(notification => 
            notification.id === id 
              ? { ...notification, isRead: true } 
              : notification
          )
        );
        setUnreadCount(prev => Math.max(0, prev - 1));
      } else {
        // Update all notifications as read
        setNotifications(prev => 
          prev.map(notification => ({ ...notification, isRead: true }))
        );
        setUnreadCount(0);
      }
      
      // Refresh unread count to ensure accuracy
      setTimeout(() => {
        fetchUnreadCount();
      }, 100);
    } catch (error) {
      console.error('Error marking notifications as read:', error);
      message.error('Failed to mark notifications as read');
    }
  };

  const handleNotificationClick = (notification: Notification) => {
    // Mark as read if not already read
    if (!notification.isRead) {
      markAsRead(notification.id);
    }
    switch (notification.type) {
      case 'mentor_availability':
        window.location.href = '/trainee/meetings';
        break;
      case 'meeting_request':
        window.location.href = '/mentor/meetings/requests';
        break;
      case 'submission_supervisor_review':
        window.location.href = '/supervisor/submissions';
        break;
      case 'submission_mentor_review':
        window.location.href = '/mentor/entries/review';
        break;
      default:
        window.location.href = notification.url || '/';
    }
    
    // Close the dropdown
    setDropdownOpen(false);
  };

  const notificationContent = (
    <div className="w-80 max-h-96 overflow-auto bg-white rounded-lg shadow-lg border border-gray-200">
      <div className="p-2 border-b flex justify-between items-center">
        <Text strong>Notifications</Text>
        {unreadCount > 0 && (
          <Button 
            type="link" 
            size="small" 
            onClick={() => markAsRead()}
            icon={<CheckOutlined />}
          >
            Mark all as read
          </Button>
        )}
      </div>
      
      <Spin spinning={loading}>
        {notifications.length > 0 ? (
          <List
            dataSource={notifications}
            renderItem={(notification) => (
              <List.Item 
                className={`cursor-pointer ${!notification.isRead ? 'bg-blue-50' : ''} !p-2`}
                onClick={() => handleNotificationClick(notification)}
              >
                <List.Item.Meta
                  title={notification.title}
                  description={
                    <div>
                      <Text type="secondary" className="block mb-1">
                        {notification.message}
                      </Text>
                      <Text type="secondary" className="text-xs">
                        {dayjs(notification.createdAt).fromNow()}
                      </Text>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        ) : (
          <Empty 
            image={Empty.PRESENTED_IMAGE_SIMPLE} 
            description="No notifications" 
            className="py-4"
          />
        )}
      </Spin>
      
      <div className="p-2 border-t text-center">
        <Button type="link" size="small">
          View all notifications
        </Button>
      </div>
    </div>
  );

  return (
    <Dropdown
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
      dropdownRender={() => notificationContent}
      placement="bottomRight"
      trigger={['click']}
    >
      <Badge count={unreadCount} overflowCount={99}>
        <Button 
          type="text" 
          icon={<BellOutlined style={{ fontSize: '20px', color: 'white' }} />} 
          className="flex items-center justify-center h-10 w-10"
        />
      </Badge>
    </Dropdown>
  );
}
