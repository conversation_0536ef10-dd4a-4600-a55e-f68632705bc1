'use client';

import { useState, useEffect, useCallback } from 'react';
import { Modal, Form, Radio, Space, Typography, Button, Divider } from 'antd';
import { UserRole } from '@/types';

import { useSession } from 'next-auth/react';
import { App } from 'antd';

const { Text, Title } = Typography;

interface QualificationRouteModalProps {
  isOpen?: boolean;
  onComplete?: () => void;
}

export default function QualificationRouteModal({ isOpen, onComplete }: QualificationRouteModalProps) {
  const { data: session, update } = useSession();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [qualificationRoute, setQualificationRoute] = useState<string>(session?.user?.qualificationRoute || 'TC');
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const { message } = App.useApp();
  const [userChecked, setUserChecked] = useState(false);

  // Handle externally controlled visibility
  useEffect(() => {
    if (isOpen !== undefined) {
      setIsModalOpen(isOpen);
    }
  }, [isOpen]);

  const checkUserQualificationRoute = useCallback(async () => {
    try {
      // Skip if not a trainee
      if (session?.user?.role !== UserRole.TRAINEE) {
        setUserChecked(true);
        return;
      }

      const response = await fetch('/api/users/me');
      if (response.ok) {
        const userData = await response.json();
        
        // Check if user has any completed entries
        const entriesResponse = await fetch('/api/trainee-skills/check-entries');
        const { hasCompletedEntries } = await entriesResponse.json();
        
        // If user has qualification route and any completed entries, prevent changes
        if (userData.qualificationRoute && hasCompletedEntries) {
          setUserChecked(true);
          return;
        }

        // If user has no qualification route, always show modal
        if (!userData.qualificationRoute) {
          setIsModalOpen(true);
          setUserChecked(true);
          return;
        }

        // If user has qualification route but hasn't dismissed the modal, show it
        if (userData.qualificationRoute && !userData.qualificationRouteDismissed) {
          setIsModalOpen(true);
          setQualificationRoute(userData.qualificationRoute);
        }
      }
      setUserChecked(true);
    } catch (error) {
      console.error('Error checking user qualification route:', error);
      setUserChecked(true);
    }
  }, [session, setUserChecked, setIsModalOpen, setQualificationRoute]);

  useEffect(() => {
    // Only run once when the component mounts if not externally controlled
    if (isOpen === undefined && !userChecked && session?.user) {
      checkUserQualificationRoute();
    }
  }, [session, userChecked, isOpen, checkUserQualificationRoute]);

  const handleSubmit = async () => {
    try {
      setLoading(true);
      
      // Update the user's qualification route and mark as not dismissed
      const updateResponse = await fetch('/api/users/me', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          qualificationRoute,
          qualificationRouteDismissed: true,
          qualificationRouteUpdatedAt: new Date().toISOString()
        }),
      });

      if (!updateResponse.ok) {
        throw new Error('Failed to update qualification route');
      }

      // Initialize skills based on the qualification route
      const initResponse = await fetch('/api/trainee-skills/initialize', {
        method: 'POST'
      });

      if (!initResponse.ok) {
        throw new Error('Failed to initialize skills');
      }

      // Update the session to reflect the new qualification route
      await update({
        ...session,
        user: {
          ...session?.user,
          qualificationRoute,
          qualificationRouteDismissed: true,
          qualificationRouteUpdatedAt: new Date().toISOString()
        }
      });

      message.success('Qualification route set and skills initialized successfully');
      setIsModalOpen(false);
      window.location.reload();
      
      // Call onComplete callback if provided
      if (onComplete) {
        onComplete();
      }
    } catch (error) {
      console.error('Error setting qualification route:', error);
      message.error('Failed to set qualification route');
    } finally {
      setLoading(false);
    }
  };

  const handleDismiss = async () => {
    // Only allow dismissal if user has qualification route
    if (qualificationRoute) {
      try {
        // Update the user's dismissal status in the database
        const response = await fetch('/api/users/me', {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ 
            qualificationRouteDismissed: true,
            qualificationRouteUpdatedAt: new Date().toISOString()
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to update dismissal status');
        }

        // Update the session
        await update({
          ...session,
          user: {
            ...session?.user,
            qualificationRouteDismissed: true,
            qualificationRouteUpdatedAt: new Date().toISOString()
          }
        });

        setIsModalOpen(false);
        
        // Call onComplete callback if provided
        if (onComplete) {
          onComplete();
        }
      } catch (error) {
        console.error('Error updating dismissal status:', error);
        message.error('Failed to update dismissal status');
      }
    } else {
      message.warning('Please select a qualification route to proceed');
    }
  };

  return (
    <Modal
      title={null}
      open={isModalOpen}
      closable={false}
      maskClosable={false}
      footer={null}
      centered
      width={600}
      styles={{
        body: {
          padding: 0,
          borderRadius: '8px',
          overflow: 'hidden'
        }
      }}
    >
      <div className="flex flex-col">
        <div className="bg-[rgb(199,100,27)] p-6 text-white">
          <Title level={3} style={{ color: 'white', margin: 0 }}>
            Welcome to Pathways!
          </Title>
          <Text style={{ color: 'rgba(255, 255, 255, 0.85)' }}>
            Select your qualification route to begin your journey
          </Text>
        </div>
        
        <div className="p-6">
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={{ qualificationRoute: session?.user?.qualificationRoute || 'TC' }}
          >
            <Form.Item
              name="qualificationRoute"
              label={<span className="text-lg font-medium">Select Your Qualification Route</span>}
              rules={[{ required: true, message: 'Please select a qualification route' }]}
            >
              <Radio.Group
                onChange={(e) => setQualificationRoute(e.target.value)}
                value={qualificationRoute}
                className="w-full"
              >
                <Space direction="vertical" className="w-full">
                  <div className={`p-4 border rounded-lg transition-all hover:border-[rgb(199,100,27)] ${qualificationRoute === 'TC' ? 'border-[rgb(199,100,27)] bg-[rgba(199,100,27,0.05)]' : 'border-gray-200'}`}>
                    <Radio value="TC" className="w-full">
                      <div className="ml-2">
                        <Text strong className="text-[16px] block">Training Contract (TC)</Text>
                        <Text type="secondary">Traditional training path for solicitors</Text>
                      </div>
                    </Radio>
                  </div>

                  <div className={`p-4 border rounded-lg transition-all hover:border-[rgb(199,100,27)] ${qualificationRoute === 'SQE' ? 'border-[rgb(199,100,27)] bg-[rgba(199,100,27,0.05)]' : 'border-gray-200'}`}>
                    <Radio value="SQE" className="w-full">
                      <div className="ml-2">
                        <Text strong className="text-[16px] block">Solicitors Qualifying Examination (SQE)</Text>
                        <Text type="secondary">New path to qualification as a solicitor</Text>
                      </div>
                    </Radio>
                  </div>
                </Space>
              </Radio.Group>
            </Form.Item>

            <Divider />

            <div className="flex justify-between items-center">
              <div className="space-x-2">
                <Button 
                  onClick={handleDismiss}
                >
                  Don&apos;t show me again
                </Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  style={{ backgroundColor: 'rgb(199, 100, 27)' }}
                >
                  Set Qualification Route
                </Button>
              </div>
            </div>
          </Form>
        </div>
      </div>
    </Modal>
  );
} 