'use client';

import { Modal, Descriptions, Tag, Button, App } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import type { Qualification } from '@/generated/prisma/index';
import { qualificationOptions } from '@/constants/qualifications';
import dayjs from 'dayjs';
import { viewDocument } from '@/lib/utils';

interface ViewQualificationModalProps {
  open: boolean;
  onClose: () => void;
  qualification: Qualification | null;
}

export default function ViewQualificationModal({ 
  open, 
  onClose, 
  qualification 
}: ViewQualificationModalProps) {
  const { message } = App.useApp();

  if (!qualification) return null;

  const getQualificationTypeText = (type: string) => {
    const option = qualificationOptions.find(opt => opt.value === type);
    return option ? option.text : type.replace(/_/g, ' ');
  };

  const getStatusColor = (status: string) => {
    const statusColors = {
      PENDING: 'orange',
      APPROVED: 'green',
      REJECTED: 'red'
    };
    return statusColors[status as keyof typeof statusColors] || 'default';
  };

  const handleDownloadDocument = async () => {
    if (!qualification.fileUrl) {
      message.error('No document available for this qualification');
      return;
    }
    
    try {
      const response = await fetch('/api/files', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ documentKey: qualification.fileUrl }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to get document URL');
      }
      
      const data = await response.json();
      
      if (!data.url) {
        throw new Error('No URL returned');
      }
      
      const link = document.createElement('a');
      link.href = data.url;
      link.target = '_blank';
      link.download = qualification.fileName || `${qualification.name}.pdf`;
      link.rel = 'noopener noreferrer';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      message.success('Document downloaded successfully');
    } catch (error) {
      message.error('Failed to download document');
      console.error(error);
    }
  };

  const handleViewDocument = async () => {
    if (!qualification.fileUrl) {
      message.error('No document available for this qualification');
      return;
    }
    
    try {
      await viewDocument(qualification.fileUrl);
      message.success('Document opened successfully');
    } catch (error) {
      message.error('Failed to view document');
      console.error(error);
    }
  };

  return (
    <Modal
      title="Qualification Details"
      open={open}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          Close
        </Button>,
        qualification.fileUrl && (
          <Button 
            key="download" 
            type="primary" 
            icon={<DownloadOutlined />}
            onClick={handleDownloadDocument}
          >
            Download Document
          </Button>
        )
      ]}
      width={600}
    >
      <Descriptions column={1} bordered>
        <Descriptions.Item label="Type">
          {getQualificationTypeText(qualification.type)}
        </Descriptions.Item>
        
        <Descriptions.Item label="Name">
          {qualification.name}
        </Descriptions.Item>
        {qualification.startDate && (
          <Descriptions.Item label="Start Date">
            {dayjs(qualification.startDate).format('DD/MM/YYYY')}
          </Descriptions.Item>
        )}
        
        {qualification.endDate && (
          <Descriptions.Item label="End Date">
            {dayjs(qualification.endDate).format('DD/MM/YYYY')}
          </Descriptions.Item>
        )}
        
        {qualification.grade && (
          <Descriptions.Item label="Grade">
            {qualification.grade}
          </Descriptions.Item>
        )}
        
        <Descriptions.Item label="Created At">
          {dayjs(qualification.createdAt).format('DD/MM/YYYY HH:mm')}
        </Descriptions.Item>
        
        <Descriptions.Item label="Updated At">
          {dayjs(qualification.updatedAt).format('DD/MM/YYYY HH:mm')}
        </Descriptions.Item>
        
        {qualification.fileName && (
          <Descriptions.Item label="File Name">
            {qualification.fileName}
          </Descriptions.Item>
        )}
        
        {!qualification.fileUrl && (
          <Descriptions.Item label="Document">
            <span className="text-gray-400">No document attached</span>
          </Descriptions.Item>
        )}
      </Descriptions>
    </Modal>
  );
}
