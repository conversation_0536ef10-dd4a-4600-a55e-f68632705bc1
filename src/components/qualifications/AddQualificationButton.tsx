'use client';

import { useState } from 'react';
import { Button } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import QualificationForm from './QualificationForm';

interface AddQualificationButtonProps {
  onSuccess?: () => void;
}

export default function AddQualificationButton({ onSuccess }: AddQualificationButtonProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleSuccess = () => {
    setIsModalOpen(false);
    if (onSuccess) {
      onSuccess();
    }
  };

  return (
    <>
      <Button
        type="primary"
        icon={<PlusOutlined />}
        onClick={() => setIsModalOpen(true)}
      >
        Add Qualification
      </Button>

      <QualificationForm
        open={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Add New Qualification"
        onSuccess={handleSuccess}
      />
    </>
  );
} 