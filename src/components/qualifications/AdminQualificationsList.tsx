'use client';

import { useEffect, useState } from 'react';
import { Table, Button, Tooltip, Space, Select, DatePicker, App, Card } from 'antd';
import { DownloadOutlined, EyeOutlined, FilterOutlined } from '@ant-design/icons';
import type { Qualification, QualificationType } from '@/generated/prisma/index';
import { qualificationOptions } from '@/constants/qualifications';
import dayjs from 'dayjs';
import { viewDocument } from '@/lib/utils';
import ViewQualificationModal from './ViewQualificationModal';

const { MonthPicker } = DatePicker;

interface FilterParams {
  type?: QualificationType;
  completionDate?: dayjs.Dayjs;
}

interface AdminQualificationsListProps {
  userId: string;
}

export default function AdminQualificationsList({ userId }: AdminQualificationsListProps) {
  const [qualifications, setQualifications] = useState<Qualification[]>([]);
  const [filteredQualifications, setFilteredQualifications] = useState<Qualification[]>([]);
  const [loading, setLoading] = useState(true);
  const [viewingQualification, setViewingQualification] = useState<Qualification | null>(null);
  const [filters, setFilters] = useState<FilterParams>({});
  const { message } = App.useApp();

  const fetchQualifications = async () => {
    try {
      const response = await fetch(`/api/qualifications?userId=${userId}`);
      if (!response.ok) throw new Error('Failed to fetch qualifications');
      const data = await response.json();
      setQualifications(data);
      applyFilters(data, filters);
    } catch (error) {
      message.error('Failed to load qualifications');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchQualifications();
  }, [userId]);

  const applyFilters = (data: Qualification[], currentFilters: FilterParams) => {
    let filtered = [...data];

    if (currentFilters.type) {
      filtered = filtered.filter(q => q.type === currentFilters.type);
    }

    if (currentFilters.completionDate) {
      filtered = filtered.filter(q => {
        const qualificationDate = dayjs(q.startDate);
        return qualificationDate && qualificationDate.isSame(currentFilters.completionDate, 'month');
      });
    }

    setFilteredQualifications(filtered);
  };

  useEffect(() => {
    applyFilters(qualifications, filters);
  }, [filters, qualifications]);

  const handleViewQualification = (qualification: Qualification) => {
    setViewingQualification(qualification);
  };

  const handleDownloadDocument = async (qualification: Qualification) => {
    if (!qualification.fileUrl) {
      message.error('No document available for this qualification');
      return;
    }
    
    try {
      const response = await fetch('/api/files', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ documentKey: qualification.fileUrl }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to get document URL');
      }
      
      const data = await response.json();
      
      if (!data.url) {
        throw new Error('No URL returned');
      }
      
      const link = document.createElement('a');
      link.href = data.url;
      link.download = qualification.fileName || `${qualification.name}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      message.success('Document downloaded successfully');
    } catch (error) {
      message.error('Failed to download document');
      console.error(error);
    }
  };

  const renderFilters = () => (
    <Space className="mb-4" wrap>
      <Select
        allowClear
        placeholder="Filter by Type"
        style={{ width: 200 }}
        onChange={(value) => setFilters(prev => ({ ...prev, type: value }))}
        value={filters.type}
      >
        {qualificationOptions.map(option => (
          <Select.Option key={option.value} value={option.value}>
            {option.text}
          </Select.Option>
        ))}
      </Select>

      <MonthPicker
        allowClear
        placeholder="Filter by Completion Date"
        onChange={(date) => {
          setFilters(prev => ({ ...prev, completionDate: date || undefined }));
        }}
        value={filters.completionDate}
      />

      <Button
        icon={<FilterOutlined />}
        onClick={() => {
          setFilters({});
          applyFilters(qualifications, {});
        }}
      >
        Clear Filters
      </Button>
    </Space>
  );

  const columns = [
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type: QualificationType) => {
        const option = qualificationOptions.find(opt => opt.value === type);
        return option ? option.text : type.replace(/_/g, ' ');
      },
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Completion Date',
      key: 'date',
      dataIndex: 'startDate',
      render: (date: string) => date ? dayjs(date).format('DD/MM/YYYY') : '-',
    },
    {
      title: 'Grade',
      dataIndex: 'grade',
      key: 'grade',
      render: (grade: string | null) => grade || '-',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: Qualification) => (
        <div className="flex gap-2">
          <Tooltip title="View Qualification">
            <Button
              icon={<EyeOutlined />}
              onClick={() => handleViewQualification(record)}
            />
          </Tooltip>
          {record.fileUrl && (
            <Tooltip title="Download Document">
              <Button
                icon={<DownloadOutlined />}
                onClick={() => handleDownloadDocument(record)}
              />
            </Tooltip>
          )}
          {!record.fileUrl && (
            <span className="text-gray-400">No document</span>
          )}
        </div>
      ),
    },
  ];

  if (qualifications.length === 0 && !loading) {
    return (
      <Card>
        <div className="text-center text-gray-500 py-8">
          No qualifications found for this user.
        </div>
      </Card>
    );
  }

  return (
    <div>
      {renderFilters()}
      <Table
        columns={columns}
        dataSource={filteredQualifications}
        rowKey="id"
        loading={loading}
        scroll={{ x: 'max-content' }}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} qualifications`,
        }}
      />
      {viewingQualification && (
        <ViewQualificationModal
          open={!!viewingQualification}
          qualification={viewingQualification}
          onClose={() => setViewingQualification(null)}
        />
      )}
    </div>
  );
}
