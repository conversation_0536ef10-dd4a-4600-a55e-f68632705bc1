'use client';

import { useState } from 'react';
import { Modal, Form, Input, DatePicker, Select, Upload, Button, App } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd/es/upload/interface';
import type { Qualification as QualificationType } from '@/generated/prisma/index';
import { qualificationOptions } from '@/constants/qualifications';
import { useSession } from 'next-auth/react';
import dayjs from 'dayjs';
import { viewDocument } from '@/lib/utils';

interface QualificationFormProps {
  open: boolean;
  onClose: () => void;
  title: string;
  initialValues?: QualificationType;
  isEdit?: boolean;
  onSuccess?: () => void;
}

export default function QualificationForm({
  open,
  onClose,
  title,
  initialValues,
  isEdit,
  onSuccess
}: QualificationFormProps) {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const { data: session } = useSession();
  const { message } = App.useApp();
  const transformedInitialValues = initialValues ? {
    ...initialValues,
    startDate: initialValues.startDate ? dayjs(initialValues.startDate) : undefined,
    endDate: initialValues.endDate ? dayjs(initialValues.endDate) : undefined,
  } : undefined;

  const filteredQualificationOptions = qualificationOptions.filter(option => {
    const skillGroupType = session?.user?.qualificationRoute?.toUpperCase();
    
    // If skillGroupType is SQE, hide PSC
    if (skillGroupType === 'SQE' && option.value === 'PSC') {
      return false;
    }

    // Show all other qualifications
    return true;
  });

  const handleSubmit = async (values: QualificationType) => {
    try {
      setLoading(true);
      
      let fileUrl = initialValues?.fileUrl;
      let fileName = initialValues?.fileName;
      
      if (fileList.length > 0 && fileList[0].originFileObj) {
        const uploadResult = await uploadFile(fileList[0].originFileObj as File);
        if (uploadResult) {
          fileUrl = uploadResult.key;
          fileName = uploadResult.name;
        } else {
          const continueWithoutFile = window.confirm('Failed to upload file. Do you want to continue without the file?');
          if (!continueWithoutFile) {
            setLoading(false);
            return;
          }
        }
      }

      const data = {
        ...values,
        fileUrl,
        fileName,
        startDate: values.startDate?.toISOString(),
        endDate: values.endDate?.toISOString(),
      };

      const url = isEdit 
        ? `/api/qualifications/${initialValues?.id}`
        : '/api/qualifications';
        
      const response = await fetch(url, {
        method: isEdit ? 'PUT' : 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      if (!response.ok) throw new Error('Failed to save qualification');

      message.success(`Qualification ${isEdit ? 'updated' : 'added'} successfully`);
      form.resetFields();
      setFileList([]);
      if (onSuccess) {
        onSuccess();
      }
      onClose();
    } catch (error) {
      console.error('Error saving qualification:', error);
      message.error('Failed to save qualification');
    } finally {
      setLoading(false);
    }
  };

  const uploadFile = async (file: File): Promise<{key: string, name: string} | null> => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('entityType', 'qualification');
      formData.append('isPublic', 'false');

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to upload file');
      }

      const data = await response.json();
      return {
        key: data.file.s3Key,
        name: data.file.name
      };
    } catch (error) {
      console.error('Error uploading file:', error);
      message.error('Failed to upload document');
      return null;
    }
  };

  return (
    <Modal
      open={open}
      title={title}
      onCancel={onClose}
      okText={isEdit ? 'Update' : 'Add'}
      confirmLoading={loading}
      onOk={() => form.submit()}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={transformedInitialValues}
        onFinish={handleSubmit}
      >
        <Form.Item
          name="type"
          label="Qualification Type"
          rules={[{ required: true, message: 'Please select a qualification type' }]}
        >
          <Select>
            {filteredQualificationOptions.map(option => (
              <Select.Option key={option.value} value={option.value}>
                {option.text}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="name"
          label="Qualification Name"
          rules={[{ required: true, message: 'Please enter the qualification name' }]}
        >
          <Input placeholder="e.g., Bachelor of Laws" />
        </Form.Item>

        <Form.Item name="grade" label="Grade/Classification">
          <Input placeholder="e.g., First Class, Merit, Pass" />
        </Form.Item>

        <Form.Item 
          name="startDate" 
          label="Month and Year Completed"
          rules={[{ required: true, message: 'Please select completion date' }]}
        >
          <DatePicker 
            picker="month" 
            className="w-full"
            placeholder="Select completion date"
          />
        </Form.Item>

        <Form.Item label="Document">
          <Upload
            fileList={fileList}
            onChange={({ fileList }) => setFileList(fileList)}
            beforeUpload={() => false}
            maxCount={1}
          >
            <Button icon={<UploadOutlined />}>
              {fileList.length === 0 && initialValues?.fileUrl ? 'Change File' : 'Select File'}
            </Button>
          </Upload>
          {fileList.length === 0 && initialValues?.fileUrl && initialValues?.fileName ? (
            <div className="mb-4">
              <a 
                className="text-blue-500 hover:text-blue-700 cursor-pointer"
                onClick={() => initialValues.fileUrl && viewDocument(initialValues.fileUrl)}
              >
                {initialValues.fileName}
              </a>
            </div>
          ) : null}
        </Form.Item>
      </Form>
    </Modal>
  );
} 