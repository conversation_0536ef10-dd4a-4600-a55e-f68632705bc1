'use client';

import { useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { Table, Button, Popconfirm, Tooltip, Space, Select, DatePicker, App } from 'antd';
import { EditOutlined, DeleteOutlined, DownloadOutlined, EyeOutlined, FilterOutlined } from '@ant-design/icons';
import type { Qualification, QualificationType } from '@/generated/prisma/index';
import { qualificationOptions } from '@/constants/qualifications';
import dayjs from 'dayjs';
import EditQualificationModal from './EditQualificationModal';
import ViewQualificationModal from './ViewQualificationModal';
import { viewDocument } from '@/lib/utils';

const { MonthPicker } = DatePicker;

interface FilterParams {
  type?: QualificationType;
  completionDate?: dayjs.Dayjs;
}

const QualificationsList = forwardRef((_, ref) => {
  const [qualifications, setQualifications] = useState<Qualification[]>([]);
  const [filteredQualifications, setFilteredQualifications] = useState<Qualification[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingQualification, setEditingQualification] = useState<Qualification | null>(null);
  const [viewingQualification, setViewingQualification] = useState<Qualification | null>(null);
  const [filters, setFilters] = useState<FilterParams>({});
  const { message } = App.useApp();
  const fetchQualifications = async () => {
    try {
      const response = await fetch('/api/qualifications');
      if (!response.ok) throw new Error('Failed to fetch qualifications');
      const data = await response.json();
      setQualifications(data);
      applyFilters(data, filters);
    } catch (error) {
      message.error('Failed to load qualifications');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  useImperativeHandle(ref, () => ({
    fetchQualifications
  }));

  useEffect(() => {
    fetchQualifications();
  }, []);

  const applyFilters = (data: Qualification[], currentFilters: FilterParams) => {
    let filtered = [...data];

    if (currentFilters.type) {
      filtered = filtered.filter(q => q.type === currentFilters.type);
    }

    if (currentFilters.completionDate) {
      filtered = filtered.filter(q => {
        const qualificationDate = dayjs(q.startDate);
        return qualificationDate && qualificationDate.isSame(currentFilters.completionDate, 'month');
      });
    }

    setFilteredQualifications(filtered);
  };

  useEffect(() => {
    applyFilters(qualifications, filters);
  }, [filters, qualifications]);

  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/qualifications/${id}`, {
        method: 'DELETE',
      });
      if (!response.ok) throw new Error('Failed to delete qualification');
      
      message.success('Qualification deleted successfully');
      fetchQualifications();
    } catch (error) {
      message.error('Failed to delete qualification');
      console.error(error);
    }
  };

  const handleViewQualification = (qualification: Qualification) => {
    setViewingQualification(qualification);
  };

  const renderFilters = () => (
    <Space className="mb-4" wrap>
      <Select
        allowClear
        placeholder="Filter by Type"
        style={{ width: 200 }}
        onChange={(value) => setFilters(prev => ({ ...prev, type: value }))}
        value={filters.type}
      >
        {qualificationOptions.map(option => (
          <Select.Option key={option.value} value={option.value}>
            {option.text}
          </Select.Option>
        ))}
      </Select>

      <MonthPicker
        allowClear
        placeholder="Filter by Completion Date"
        onChange={(date) => {
          setFilters(prev => ({ ...prev, completionDate: date || undefined }));
        }}
        value={filters.completionDate}
      />

      <Button
        icon={<FilterOutlined />}
        onClick={() => {
          setFilters({});
          applyFilters(qualifications, {});
        }}
      >
        Clear Filters
      </Button>
    </Space>
  );

  const columns = [
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type: QualificationType) => {
        const option = qualificationOptions.find(opt => opt.value === type);
        return option ? option.text : type.replace(/_/g, ' ');
      },
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Completion Date',
      key: 'date',
      dataIndex: 'startDate',
      render: (date: string) => date ? dayjs(date).format('DD/MM/YYYY') : '-',
    },
    {
      title: 'Grade',
      dataIndex: 'grade',
      key: 'grade',
      render: (grade: string | null) => grade || '-',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: Qualification) => (
        <div className="flex gap-2">
          <Tooltip title="View Qualification">
            <Button
              icon={<EyeOutlined />}
              onClick={() => handleViewQualification(record)}
            />
          </Tooltip>
          {record.fileUrl && (
            <Tooltip title="Download Document">
              <Button
                icon={<DownloadOutlined />}
                onClick={() => {
                  if (record.fileUrl) {
                    viewDocument(record.fileUrl);
                  } else {
                    message.error('No file URL available for this qualification');
                  }
                }}
              />
            </Tooltip>
          )}
          <Tooltip title="Edit">
            <Button
              icon={<EditOutlined />}
              onClick={() => setEditingQualification(record)}
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Popconfirm
              title="Delete Qualification"
              description="Are you sure you want to delete this qualification?"
              onConfirm={() => handleDelete(record.id)}
              okText="Yes"
              cancelText="No"
            >
              <Button danger icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
        </div>
      ),
    },
  ];

  return (
    <>
      {renderFilters()}
      <Table
        columns={columns}
        dataSource={filteredQualifications}
        rowKey="id"
        loading={loading}
        scroll={{ x: 'max-content' }}
      />
      {editingQualification && (
        <EditQualificationModal
          qualification={editingQualification}
          onClose={() => setEditingQualification(null)}
          onSuccess={() => {
            setEditingQualification(null);
            fetchQualifications();
          }}
        />
      )}
      {viewingQualification && (
        <ViewQualificationModal
          qualification={viewingQualification}
          onClose={() => setViewingQualification(null)}
          open={!!viewingQualification}
        />
      )}
    </>
  );
});

QualificationsList.displayName = 'QualificationsList';

export default QualificationsList; 