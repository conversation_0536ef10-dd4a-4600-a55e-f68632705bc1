'use client';

import QualificationForm from './QualificationForm';
import type { Qualification } from '@/generated/prisma/index';

interface EditQualificationModalProps {
  qualification: Qualification;
  onClose: () => void;
  onSuccess: () => void;
}

export default function EditQualificationModal({
  qualification,
  onClose,
  onSuccess,
}: EditQualificationModalProps) {
  const handleClose = () => {
    onClose();
    onSuccess();
  };

  return (
    <QualificationForm
      open={true}
      onClose={handleClose}
      title="Edit Qualification"
      initialValues={{
        ...qualification,
        startDate: qualification.startDate ? new Date(qualification.startDate) : null,
        endDate: qualification.endDate ? new Date(qualification.endDate) : null,
      }}
      isEdit
    />
  );
} 