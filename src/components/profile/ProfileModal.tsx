'use client';

import { useState, useEffect } from 'react';
import { Modal, Form, Input, App, Select } from 'antd';
import { User, UserRole } from '@/types';
import { useSession } from 'next-auth/react';

interface ProfileModalProps {
  open: boolean;
  onClose: () => void;
  user: User;
}

export default function ProfileModal({ open, onClose, user }: ProfileModalProps) {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [canChangeQualificationRoute, setCanChangeQualificationRoute] = useState(false);
  const { message } = App.useApp();
  const { update: updateSession } = useSession();

  useEffect(() => {
    const checkQualificationRouteChange = async () => {
      if (user.role !== UserRole.TRAINEE) {
        setCanChangeQualificationRoute(false);
        return;
      }

      try {
        const entriesResponse = await fetch('/api/trainee-skills/check-entries');
        const { hasCompletedEntries } = await entriesResponse.json();
        
        setCanChangeQualificationRoute(!hasCompletedEntries);
      } catch (error) {
        console.error('Error checking qualification route change permission:', error);
        setCanChangeQualificationRoute(false);
      }
    };

    checkQualificationRouteChange();
  }, [user.role]);

  const updateUser = async (data: Partial<User>) => {
    try {
      const response = await fetch('/api/profile', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          ...(data.qualificationRoute && {
            qualificationRouteDismissed: true,
            qualificationRouteUpdatedAt: new Date().toISOString()
          })
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update user');
      }

      const updatedUser = await response.json();
      
      await updateSession({
        ...user,
        ...updatedUser,
      });

      return updatedUser;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to update user');
    }
  };

  const handleSubmit = async (values: Partial<User>) => {
    try {
      setLoading(true);
      await updateUser(values);
      message.success('Profile updated successfully');
      onClose();
    } catch (error) {
      if (error instanceof Error) {
        message.error(error.message);
      } else {
        message.error('Failed to update profile');
      }
      console.error('Update error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="My Profile"
      open={open}
      onCancel={onClose}
      okText="Save"
      cancelText="Cancel"
      confirmLoading={loading}
      onOk={() => {
        form.validateFields()
          .then(handleSubmit)
          .catch(info => {
            console.log('Validate Failed:', info);
          });
      }}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          name: user?.name,
          email: user?.email,
          qualificationRoute: user?.qualificationRoute,
          address: user?.address || '',
        }}
      >
        <Form.Item
          name="name"
          label="Name"
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="email"
          label="Email"
        >
          <Input />
        </Form.Item>

        {user.role === UserRole.TRAINEE && (
          <Form.Item
            name="qualificationRoute"
            label="Qualification Route"
            tooltip={!canChangeQualificationRoute ? 
              "Cannot change qualification route once you have completed entries" : undefined}
            help={canChangeQualificationRoute ? 
              "Warning: Changing qualification route will reset your skills progress" : undefined}
          >
            <Select disabled={!canChangeQualificationRoute}>
              <Select.Option value="SQE">SQE</Select.Option>
              <Select.Option value="TC">TC</Select.Option>
            </Select>
          </Form.Item>
        )}

        <Form.Item
          name="address"
          label="Bio"
        >
          <Input.TextArea rows={3} />
        </Form.Item>
      </Form>
    </Modal>
  );
} 