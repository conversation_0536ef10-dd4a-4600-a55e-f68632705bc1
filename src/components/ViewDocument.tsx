import React from 'react';
import { But<PERSON>, App } from 'antd';
import { FilePdfOutlined } from '@ant-design/icons';

interface ViewDocumentProps {
  documentKey: string;
  name?: string;
  variant?: 'button' | 'link';
  className?: string;
}

export const ViewDocument: React.FC<ViewDocumentProps> = ({
  documentKey,
  name,
  variant = 'button',
  className,
}) => {
  const { message } = App.useApp();

  const handleViewDocument = async () => {
    try {
      message.loading('Getting document URL...');
      const response = await fetch('/api/files', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ documentKey }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to get document URL');
      }
      
      const data = await response.json();
      
      if (!data.url) {
        throw new Error('No URL returned');
      }
      
      window.open(data.url, '_blank');
      message.success('Document opened successfully');
    } catch (error) {
      console.error('Error viewing document:', error);
      message.error('Failed to view document');
    }
  };

  if (variant === 'link') {
    return (
      <a 
        onClick={handleViewDocument}
        className={className}
        style={{ cursor: 'pointer' }}
      >
        {name || 'View Document'}
      </a>
    );
  }

  return (
    <Button
      type="text"
      icon={<FilePdfOutlined />}
      onClick={handleViewDocument}
      className={className}
    >
      {name && <span style={{ marginLeft: 8 }}>{name}</span>}
    </Button>
  );
};

export default ViewDocument; 