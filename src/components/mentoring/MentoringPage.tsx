import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON>ton, App } from "antd";
import { UserRole, Meeting, MentorAvailability } from "@/types";
import MeetingsList from "./MeetingsList";
import AvailabilityCalendar from "./AvailabilityCalendar";
import BookMeetingModal from "./BookMeetingModal";

export default function MentoringPage() {
  const { data: session } = useSession();
  const [activeTab, setActiveTab] = useState("meetings");
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [availabilities, setAvailabilities] = useState<MentorAvailability[]>([]);
  const [isBookingModalOpen, setIsBookingModalOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const { message } = App.useApp();
  const isMentor = session?.user?.role === UserRole.MENTOR;

  useEffect(() => {
    fetchMeetings();
    if (isMentor) {
      fetchAvailabilities();
    }
  }, [isMentor]);

  const fetchMeetings = async () => {
    try {
      const response = await fetch("/api/meetings?type=MONTHLY");
      if (!response.ok) throw new Error("Failed to fetch meetings");
      const data = await response.json();
      setMeetings(data);
    } catch (error) {
      message.error("Failed to load meetings");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAvailabilities = async () => {
    try {
      const response = await fetch("/api/mentor/availability");
      if (!response.ok) throw new Error("Failed to fetch availabilities");
      const data = await response.json();
      setAvailabilities(data);
    } catch (error) {
      message.error("Failed to load availability slots");
      console.error(error);
    }
  };

  const handleMeetingStatusChange = async (meetingId: string, status: string) => {
    try {
      const response = await fetch(`/api/meetings/${meetingId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) throw new Error("Failed to update meeting");
      
      message.success("Meeting status updated successfully");
      fetchMeetings();
    } catch (error) {
      message.error("Failed to update meeting status");
      console.error(error);
    }
  };

  const handleUpdateDateSlots = async (date: Date, slots: {
    startTime: Date;
    endTime: Date;
    meetingMethod: "IN_PERSON" | "VIRTUAL";
    duration?: number;
    bufferTime?: number;
    meetingLocation?: string;
    meetingMessage?: string;
  }[]) => {
    if (session?.user?.role !== UserRole.MENTOR) {
      message.error("You are not authorized to create availability slots");
      return;
    }
    try {
      const response = await fetch("/api/mentor/availability", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ date, slots }),
      });

      if (!response.ok) throw new Error("Failed to create availability");
      
      message.success("Availability slots created successfully");
      fetchAvailabilities();
    } catch (error) {
      message.error("Failed to create availability slots");
      console.error(error);
    }
  };

  const handleDeleteAvailability = async (availabilityId: string) => {
    try {
      const response = await fetch(`/api/mentor/availability?id=${availabilityId}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete availability");
      
      message.success("Availability slot deleted successfully");
      fetchAvailabilities();
    } catch (error) {
      message.error("Failed to delete availability slot");
      console.error(error);
    }
  };

  const items = [
    {
      key: "meetings",
      label: "Meetings",
      children: (
        <MeetingsList
          meetings={meetings}
          loading={loading}
          onStatusChange={handleMeetingStatusChange}
          isMentor={isMentor}
          mode="requests"
          onRefresh={fetchMeetings}
        />
      ),
    },
  ];

  if (isMentor) {
    items.push({
      key: "availability",
      label: "Availability",
      children: (
        <AvailabilityCalendar
          availabilities={availabilities}
          onUpdateDateSlots={handleUpdateDateSlots}
          onDelete={handleDeleteAvailability}
        />
      ),
    });
  }

  return (
    <div className="p-6">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">
            {isMentor ? "Mentor Dashboard" : "My Meetings"}
          </h1>
          <p className="text-gray-600">
            {isMentor
              ? "Manage your availability and meetings with trainees"
              : "Schedule and manage your meetings with your mentor"}
          </p>
        </div>
        {!isMentor && session?.user?.mentorId && (
          <Button 
            type="primary" 
            onClick={() => setIsBookingModalOpen(true)}
            className="ml-4"
          >
            Book Meeting
          </Button>
        )}
      </div>

      {!isMentor && !session?.user?.mentorId ? (
        <div className="text-center py-8">
          <h2 className="text-xl text-gray-600 mb-2">No Mentor Assigned</h2>
          <p className="text-gray-500">
            Please contact your administrator to assign a mentor before booking meetings.
          </p>
        </div>
      ) : (
        <>
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={items}
            className="min-h-[500px]"
          />

          {!isMentor && session?.user?.mentorId && (
            <BookMeetingModal
              open={isBookingModalOpen}
              onClose={() => setIsBookingModalOpen(false)}
              onSuccess={() => {
                setIsBookingModalOpen(false);
                fetchMeetings();
              }}
              mentorId={session.user.mentorId}
              mentor={session.user.mentor || { id: "", name: "", email: "" }}
            />
          )}
        </>
      )}
    </div>
  );
} 