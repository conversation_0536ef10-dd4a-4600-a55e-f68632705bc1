'use client';

import { useState, useEffect } from 'react';
import { Form, Input, DatePicker, Switch, Button, App, Card, Typography, Upload } from 'antd';
import type { FeedbackForm } from '@/types/feedback';
import type { UploadProps } from 'antd/es/upload/interface';
import dayjs from 'dayjs';
import { UploadOutlined, DeleteOutlined } from '@ant-design/icons';
import { viewDocument } from '@/lib/utils';

const { TextArea } = Input;
const { Title } = Typography;

interface FeedbackFormEditProps {
  feedbackForm: FeedbackForm & {
    monthlyReview: {
      candidate: {
        name: string;
      };
      mentor: {
        name: string;
      } | null;
    };
  };
  onSuccess: () => void;
}

export default function FeedbackFormEdit({ feedbackForm, onSuccess }: FeedbackFormEditProps) {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [hasSignature, setHasSignature] = useState(!!feedbackForm?.signatureKey);
  const [signatureName, setSignatureName] = useState(feedbackForm?.signatureName || '');
  const { message } = App.useApp();
  useEffect(() => {
    // Initialize form with initial values including signature details
    if (feedbackForm) {
      form.setFieldsValue({
        ...feedbackForm,
        meetingDate: feedbackForm.meetingDate ? dayjs(feedbackForm.meetingDate) : null,
        signatureKey: feedbackForm.signatureKey || undefined,
        signatureName: feedbackForm.signatureName || undefined,
      });
    }
  }, [feedbackForm, form]);

  const handleSubmit = async () => {
    try {
      setLoading(true);
      
      const formValues = form.getFieldsValue();
      
      // Create a clean object without signature field (which is not in Prisma model)
      const formData = {
        ...formValues,
        meetingDate: formValues.meetingDate ? formValues.meetingDate.toDate() : null,
        signatureKey: hasSignature ? formValues.signatureKey : null,
        signatureName: hasSignature ? formValues.signatureName : null,
      };
      
      // Remove signature if exists in the object
      if ('signature' in formData) {
        delete (formData as any).signature;
      }
      
      console.log('Submitting data:', formData);
      
      const response = await fetch(`/api/feedback-forms/${feedbackForm.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      if (!response.ok) throw new Error('Failed to update feedback form');
      
      message.success('Feedback form saved successfully');
      onSuccess();
    } catch (error) {
      console.error('Error updating feedback form:', error);
      message.error('Failed to save feedback form');
    } finally {
      setLoading(false);
    }
  };

  const handleUpload: UploadProps['customRequest'] = async (options) => {
    const { file, onSuccess, onError } = options;
    try {
      setUploadLoading(true);
      const formData = new FormData();
      formData.append('file', file as Blob);
      formData.append('entityType', 'signature');
      
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) throw new Error('Upload failed');

      const data = await response.json();
      
      // Update form with signature key and name
      const filename = (file as File).name;
      form.setFieldsValue({
        signatureKey: data.file.s3Key,
        signatureName: filename,
      });
      
      // Update state to show signature
      setHasSignature(true);
      setSignatureName(filename);

      onSuccess?.(data, file);
      message.success('Signature uploaded successfully');
    } catch (error) {
      console.error('Error uploading signature:', error);
      onError?.(error as Error);
      message.error('Failed to upload signature');
    } finally {
      setUploadLoading(false);
    }
  };
  
  const handleDeleteSignature = () => {
    // Update form values
    form.setFieldsValue({
      signatureKey: null,
      signatureName: null
    });
    
    // Update state
    setHasSignature(false);
    setSignatureName('');
  };

  return (
    <Form
      form={form}
      layout="vertical"
      initialValues={{
        ...feedbackForm,
        meetingDate: dayjs(feedbackForm.meetingDate),
      }}
      onFinish={handleSubmit}
    >
      <Form.Item
        name="coveringMonth"
        label="Monthly Mentor Meeting covering work from the month of:"
        rules={[{ required: true, message: 'Please enter the covering month' }]}
      >
        <Input placeholder="e.g. July 2024" />
      </Form.Item>

      <Form.Item
        name="menteeName"
        label="Mentee Name"
        rules={[{ required: true, message: 'Please enter the mentee name' }]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        name="meetingDate"
        label="Meeting Date"
        rules={[{ required: true, message: 'Please select the meeting date' }]}
      >
        <DatePicker style={{ width: '100%' }} />
      </Form.Item>

      <Form.Item name="pointsToDiscuss">
        <TextArea rows={4} placeholder="Points to discuss with mentee" />
      </Form.Item>

      <Form.Item name="wasGoodWork" label="1. Have you received good variety/quality work? Are there specific things you would like to gain exposure to?" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item name="goodWorkComments" label="">
        <TextArea rows={4} placeholder="Comments:" />
      </Form.Item>

      <Form.Item name="hasFeltResponsible" label="2. Do you feel that the level of responsiblity you are receiving has increased?" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item name="responsibilityComments" label="Comments on Responsibility">
        <TextArea rows={4} placeholder="Comments:" />
      </Form.Item>

      <Form.Item name="workConcerns" label="3. Do you have concerns/comments about your work you completed last month?">
        <TextArea rows={4} placeholder="Comments:" />
      </Form.Item>

      <Form.Item name="isWorkingWellSupervisor" label="4. Are you working well with your supervisor? Do you have open and clear lines of communication?" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item name="workingWellSupervisorComments">
        <TextArea rows={4} placeholder="Comments:" />
      </Form.Item>

      <Form.Item name="hadInformalMonthlyFeedback" label="5. Have you had an informal monthly feedback session with your supervisor?" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item name="informalMonthlyFeedbackComments">
        <TextArea rows={4} placeholder="Comments:" />
      </Form.Item>

      <h3 className="text-lg font-bold">Skill Standards:</h3>

      <Form.Item name="skillStandardsComments" label="6. What are the skill standards that you still have not met in this seat? (To be considered in January, April, July and October TPM) Discuss any potential gaps forming with your mentee and offer suggestions on how they might gain exposure to these skills.">
        <TextArea rows={4} placeholder="Skills standards not yet met in this seat" />
      </Form.Item>

      <Form.Item name="anyConcerns" label="7. Do you have any concerns/anything you wish to discuss?">
        <TextArea rows={4} placeholder="Comments:" />
      </Form.Item>

      <Form.Item name="furtherTraining" label="8. What further training do you feel you need at this time, if any?">
        <TextArea rows={4} placeholder="Comments:" />
      </Form.Item>

      <h3 className="text-lg font-bold">Towards end of seat:</h3>

      <Form.Item name="appraisalMeeting" label="9. Have you set up an appraisal meeting?">
        <Input placeholder="Comments:" />
      </Form.Item>

      <Form.Item name="qualifyingArea" label="10. Is this a firm/practice area you would consider qualifying into?">
        <Input placeholder="Firm/practice area you would consider qualifying into" />
      </Form.Item>

      <Form.Item name="mentorNotes" label="Mentor Notes">
        <TextArea rows={4} placeholder="Mentor notes (please add any other points raised or discussed with the mentee which you feel should be documented):" />
      </Form.Item>

      <div style={{ marginBottom: 24 }}>
        <Title level={5} style={{ color: '#f5822f' }}>
          Action Box: <span style={{ color: 'rgba(0, 0, 0, 0.85)' }}>Please add comments to any boxes that you would like HR to follow up:</span>
        </Title>
        <Card style={{ border: '2px solid #555' }}>
          <Form.Item name="furtherAction" label="Any further action?" style={{ marginBottom: 16 }}>
            <TextArea rows={2} variant="outlined" />
          </Form.Item>
          <div style={{ borderTop: '2px solid #555', marginBottom: 16 }} />
          <Form.Item name="pointsToNote" label="Points to note:" style={{ marginBottom: 0 }}>
            <TextArea rows={4} variant="outlined" />
          </Form.Item>
        </Card>
      </div>

      <Card style={{ border: '2px solid #555', marginBottom: 24 }}>
        <Form.Item label={<span style={{ color: '#f5822f' }}>Signed off by Mentor/Training Principal</span>}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <div>
              <Upload
                customRequest={handleUpload}
                accept="image/*"
                showUploadList={false}
              >
                <Button icon={<UploadOutlined />} loading={uploadLoading}>
                  {hasSignature ? 'Change Signature' : 'Upload Signature'}
                </Button>
              </Upload>
              {hasSignature && (
                <div className="flex items-center mt-2">
                  <a onClick={() => feedbackForm.signatureKey && viewDocument(feedbackForm.signatureKey)}>
                    {signatureName}
                  </a>
                  <Button 
                    type="text" 
                    danger 
                    icon={<DeleteOutlined />} 
                    onClick={handleDeleteSignature}
                    className="ml-2"
                  />
                </div>
              )}
            </div>
          </div>
        </Form.Item>
        <div style={{ borderTop: '2px solid #555' }} />
        
        {/* Hidden fields to store signature information */}
        <Form.Item name="signatureKey" hidden>
          <Input />
        </Form.Item>
        <Form.Item name="signatureName" hidden>
          <Input />
        </Form.Item>
        
        <Form.Item name="printName" label="Print name">
          <Input variant="outlined" />
        </Form.Item>
      </Card>

      <Form.Item>
        <Button type="primary" htmlType="submit" loading={loading}>
          Submit
        </Button>
      </Form.Item>
    </Form>
  );
} 