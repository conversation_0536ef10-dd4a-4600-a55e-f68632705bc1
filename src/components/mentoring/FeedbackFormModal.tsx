'use client';

import { Modal } from 'antd';
import FeedbackFormEdit from './FeedbackFormEdit';
import { useState, useEffect } from 'react';
import type { FeedbackForm } from '@/types/feedback';

interface FeedbackFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  feedbackFormId: string | null;
}

export default function FeedbackFormModal({ isOpen, onClose, feedbackFormId }: FeedbackFormModalProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [feedbackForm, setFeedbackForm] = useState<FeedbackForm | null>(null);

  useEffect(() => {
    const fetchFeedbackForm = async () => {
      if (!feedbackFormId) return;
      
      try {
        setIsLoading(true);
        const response = await fetch(`/api/feedback-forms/${feedbackFormId}`);
        if (!response.ok) throw new Error('Failed to fetch feedback form');
        setFeedbackForm(await response.json());
      } catch (err) {
        console.error('Error fetching feedback form:', err);
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen && feedbackFormId) {
      fetchFeedbackForm();
    }
  }, [feedbackFormId, isOpen]);

  return (
    <Modal
      title="Edit Feedback Form"
      open={isOpen}
      onCancel={onClose}
      width={800}
      footer={null}
    >
      {isLoading ? (
        <p>Loading...</p>
      ) : feedbackForm ? (
        <FeedbackFormEdit 
          feedbackForm={feedbackForm as FeedbackForm & { monthlyReview: { candidate: { name: string; }; mentor: { name: string; } | null; }; }} 
          onSuccess={() => {
            onClose();
          }}
        />
      ) : (
        <p>Failed to load feedback form</p>
      )}
    </Modal>
  );
}
