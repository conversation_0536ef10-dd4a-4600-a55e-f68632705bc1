import React, { useState, useRef } from 'react';
import { Calendar, Button, Modal, Tooltip, Table, Radio, App } from "antd";
import type { Dayjs } from "dayjs";
import dayjs from "dayjs";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import { MentorAvailability } from "@/types";
import type { CalendarProps } from "antd";
import type { ColumnsType } from "antd/es/table";
import AvailabilityModal, { AvailabilityModalRef } from './AvailabilityModal';

interface AvailabilityCalendarProps {
  availabilities: MentorAvailability[];
  onUpdateDateSlots: (
    date: Date,
    slots: {
      startTime: Date;
      endTime: Date;
      meetingMethod: "IN_PERSON" | "VIRTUAL";
      duration?: number;
      bufferTime?: number;
      meetingLocation?: string;
      meetingMessage?: string;
    }[]
  ) => Promise<void>;
  onDelete: (id: string) => Promise<void>;
}

export default function MenteeAvailabilityCalendar({
  availabilities,
  onUpdateDateSlots,
  onDelete,
}: AvailabilityCalendarProps) {
  const modalRef = useRef<AvailabilityModalRef>(null);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [selectedSlotId, setSelectedSlotId] = useState<string | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'list' | 'calendar'>('list');
  const { message } = App.useApp();
  const handleDelete = async () => {
    if (!selectedSlotId) return;

    try {
      setIsDeleting(selectedSlotId);
      await onDelete(selectedSlotId);
      message.success("Mentee Meeting availability slot deleted successfully");
      setIsDeleteModalOpen(false);
    } catch (error) {
      console.error(error);
      message.error("Failed to delete availability slot");
    } finally {
      setIsDeleting(null);
      setSelectedSlotId(null);
    }
  };

  const showDeleteConfirm = (id: string) => {
    setSelectedSlotId(id);
    setIsDeleteModalOpen(true);
  };

  const dateCellRender = (date: Dayjs) => {
    const dayAvailabilities = availabilities.filter((slot) => {
      const slotDate = dayjs(slot.startTime);
      return (
        date.date() === slotDate.date() &&
        date.month() === slotDate.month() &&
        date.year() === slotDate.year()
      );
    });

    if (dayAvailabilities.length === 0) return null;

    return (
      <ul className="list-none p-0">
        {dayAvailabilities.map((slot) => {
          const method = slot.meetingMethod;

          return (
            <li
              key={slot.id}
              className={`mb-1 p-1 text-xs rounded ${slot.isBooked ? "bg-gray-200" : "bg-blue-100"}`}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-center">
                <div>
                  <span>
                    {dayjs(slot.startTime).format("h:mm A")} -{" "}
                    {dayjs(slot.endTime).format("h:mm A")}
                  </span>
                  <span className="ml-2 text-xs text-gray-600">
                    ({method === "IN_PERSON" ? "In-person" : "Virtual"})
                  </span>
                </div>
                {!slot.isBooked && (
                  <Tooltip title="Delete slot">
                    <Button
                      type="text"
                      size="small"
                      danger
                      icon={<DeleteOutlined />}
                      loading={isDeleting === slot.id}
                      onClick={() => showDeleteConfirm(slot.id)}
                    />
                  </Tooltip>
                )}
              </div>
            </li>
          );
        })}
      </ul>
    );
  };

  // Table columns for the availability list
  const columns: ColumnsType<MentorAvailability> = [
    {
      title: 'Date',
      dataIndex: 'startTime',
      key: 'date',
      render: (text) => dayjs(text).format('DD/MM/YYYY')
    },
    {
      title: 'Time',
      key: 'time',
      render: (_, record) => (
        <span>
          {dayjs(record.startTime).format('HH:mm')} - {dayjs(record.endTime).format('HH:mm')}
        </span>
      ),
    },
    {
      title: 'Method',
      dataIndex: 'meetingMethod',
      key: 'meetingMethod',
      render: (method) => (method === 'IN_PERSON' ? 'In-person' : 'Virtual')
    },
    {
      title: 'Status',
      dataIndex: 'isBooked',
      key: 'status',
      render: (isBooked) => (
        <span className={`px-2 py-1 rounded text-xs ${isBooked ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`}>
          {isBooked ? 'Booked' : 'Available'}
        </span>
      )
    },
    {
      title: 'Action',
      key: 'action',
      render: (_, record) => (
        !record.isBooked && (
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => showDeleteConfirm(record.id)}
            loading={isDeleting === record.id}
          />
        )
      ),
    },
  ];

  return (
    <div>
      <div>
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg mb-6 shadow-sm">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold text-gray-800 mb-2">Mentee Meeting Availability</h2>
              <p className="text-gray-600">Set up times when you&apos;re available to meet with your mentees.</p>
            </div>
            <div className="flex items-center gap-4">
              <Radio.Group
                value={viewMode}
                onChange={(e) => setViewMode(e.target.value)}
                buttonStyle="solid"
              >
                <Radio.Button value="list">List View</Radio.Button>
                <Radio.Button value="calendar">Calendar View</Radio.Button>
              </Radio.Group>
              <Button
                type="primary"
                onClick={() => modalRef.current?.open()}
                icon={<PlusOutlined />}
                size="large"
                className="bg-blue-600 hover:bg-blue-700"
              >
                Add Available Times
              </Button>
            </div>
          </div>
        </div>

        {availabilities.length > 0 ? (
          <div className="bg-white rounded-lg shadow-sm border border-gray-100">
            {viewMode === 'list' ? (
              <Table
                columns={columns}
                dataSource={availabilities}
                rowKey="id"
                pagination={{ pageSize: 10 }}
                className="rounded-lg overflow-hidden"
                summary={() => (
                  <tr>
                    <td colSpan={5} className="px-4 py-3 bg-gray-50">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-500 text-sm">
                          Total: {availabilities.length} {availabilities.length === 1 ? 'slot' : 'slots'}
                        </span>
                        <span className="text-gray-500 text-sm">
                          Booked: {availabilities.filter(a => a.isBooked).length} |
                          Available: {availabilities.filter(a => !a.isBooked).length}
                        </span>
                      </div>
                    </td>
                  </tr>
                )}
              />
            ) : (
              <div className="p-4">
                <Calendar
                  cellRender={dateCellRender}
                  mode="month"
                  className="hide-mode-switch"
                />
                <div className="mt-4 flex items-center gap-4">
                  <div className="flex items-center">
                    <div className="w-4 h-4 bg-blue-100 rounded mr-2"></div>
                    <span className="text-sm">Available</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-4 h-4 bg-gray-200 rounded mr-2"></div>
                    <span className="text-sm">Booked</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-16 text-center">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-4xl">📅</span>
            </div>
            <h3 className="text-xl font-medium mb-2">No availability slots yet</h3>
            <p className="text-gray-500 mb-6">Create your first availability slot to start scheduling mentee meetings</p>
            <Button
              type="primary"
              onClick={() => modalRef.current?.open()}
              icon={<PlusOutlined />}
              size="large"
            >
              Add Your First Slot
            </Button>
          </div>
        )}
      </div>

      <AvailabilityModal
        ref={modalRef}
        availabilities={availabilities}
        onUpdateDateSlots={onUpdateDateSlots}
      />

      <Modal
        title="Delete Availability"
        open={isDeleteModalOpen}
        onCancel={() => {
          setIsDeleteModalOpen(false);
          setSelectedSlotId(null);
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setIsDeleteModalOpen(false);
              setSelectedSlotId(null);
            }}
            disabled={isDeleting !== null}
          >
            Cancel
          </Button>,
          <Button
            key="submit"
            type="primary"
            danger
            onClick={handleDelete}
            loading={isDeleting !== null}
          >
            Delete
          </Button>,
        ]}
        closable={isDeleting === null}
        maskClosable={isDeleting === null}
      >
        <p>Are you sure you want to delete this mentee meeting availability slot?</p>
      </Modal>
      <style jsx global>{`
        .hide-mode-switch .ant-picker-calendar-mode-switch {
          display: none !important;
        }
      `}</style>
    </div>
  );
}
