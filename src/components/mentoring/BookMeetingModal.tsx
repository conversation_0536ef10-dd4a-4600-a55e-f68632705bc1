import { useState, useEffect, useCallback } from "react";
import { Modal, Form, Input, Calendar, Tooltip, App, Spin } from "antd";
import { MentorAvailability, MeetingType, UserRole, Meeting } from "@/types";
import dayjs from "dayjs";
import type { Dayjs } from "dayjs";
import { BookOutlined } from "@ant-design/icons";

interface BookMeetingModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  mentorId: string;
  mentor: {
    id: string;
    name: string;
    email: string;
  };
}

export default function BookMeetingModal({
  open,
  onClose,
  onSuccess,
  mentorId,
  mentor,
}: BookMeetingModalProps) {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(false);
  const [availabilities, setAvailabilities] = useState<MentorAvailability[]>([]);
  const [conflictingMeetings, setConflictingMeetings] = useState<Meeting[]>([]);
  const [slotToBook, setSlotToBook] = useState<MentorAvailability | null>(null);
  const [isSlotModalOpen, setIsSlotModalOpen] = useState(false);
  const { message } = App.useApp();
  console.log('mentorId', mentorId);

  const fetchAvailabilitiesAndConflicts = useCallback(async () => {
    try {
      setDataLoading(true);
      console.log('🔄 Fetching availabilities and conflicts for mentor:', mentorId);
      
      // Fetch availabilities first
      const availResponse = await fetch(`/api/mentor/${mentorId}/availability`);
      if (!availResponse.ok) throw new Error("Failed to fetch availabilities");
      const availData = await availResponse.json();
      
      // Filter out booked slots and past slots
      const now = dayjs();
      const availableSlots = availData.filter((slot: MentorAvailability) => 
        !slot.isBooked && dayjs(slot.startTime).isAfter(now)
      );
      
      console.log('📅 Available slots found:', availableSlots.length);
      
      let conflicts: Meeting[] = [];
      
      if (availableSlots.length > 0) {
        // Sort slots by start time to get correct date range
        const sortedSlots = availableSlots.sort((a: MentorAvailability, b: MentorAvailability ) => 
          dayjs(a.startTime).diff(dayjs(b.startTime))
        );
        
        const startTime = dayjs(sortedSlots[0].startTime).toISOString();
        const endTime = dayjs(sortedSlots[sortedSlots.length - 1].endTime).toISOString();
        
        console.log('🕐 Fetching conflicts for range:', { startTime, endTime });
        
        // Fetch all potential conflicts in one call using the new endpoint
        const conflictsResponse = await fetch(
          `/api/meetings/conflicts?startTime=${encodeURIComponent(startTime)}&endTime=${encodeURIComponent(endTime)}&mentorId=${mentorId}`
        );
        
        if (!conflictsResponse.ok) throw new Error("Failed to fetch conflicts");
        const data = await conflictsResponse.json();
        conflicts = data.conflicts || [];
        
        console.log('⚠️ Conflicts found:', conflicts.length);
      }
      
      // Set both states together to avoid race condition
      setConflictingMeetings(conflicts);
      setAvailabilities(availableSlots);
      
    } catch (error) {
      message.error("Failed to load availability slots");
      console.error('❌ Error fetching data:', error);
    } finally {
      setDataLoading(false);
    }
  }, [mentorId, message]);

  useEffect(() => {
    if (open && mentorId) {
      // Reset states when modal opens
      setAvailabilities([]);
      setConflictingMeetings([]);
      setSlotToBook(null);
      setIsSlotModalOpen(false);
      form.resetFields();
      
      // Fetch data
      fetchAvailabilitiesAndConflicts();
    }
  }, [open, mentorId, fetchAvailabilitiesAndConflicts, form]);

  // Calendar cell render for available slots
  const dateCellRender = (date: Dayjs) => {
    // Don't render anything for past dates
    if (date.isBefore(dayjs(), 'day')) {
      return null;
    }

    const dayAvailabilities = availabilities.filter((slot) => {
      const slotDate = dayjs(slot.startTime);
      return (
        date.date() === slotDate.date() &&
        date.month() === slotDate.month() &&
        date.year() === slotDate.year()
      );
    });

    if (dayAvailabilities.length === 0) return null;
    
    // Debug: Log render info for this date (only in development)
    if (process.env.NODE_ENV === 'development' && dayAvailabilities.length > 0) {
      console.log(`📊 Rendering ${dayAvailabilities.length} slots for ${date.format('YYYY-MM-DD')}:`, {
        conflictingMeetingsCount: conflictingMeetings.length,
        conflictingMeetings: conflictingMeetings.map(m => ({
          proposedTime: dayjs(m.proposedTime).format(),
          type: m.type
        }))
      });
    }

    return (
      <ul className="list-none p-0">
        {dayAvailabilities.map((slot) => {
          const isBooked = slot.isBooked;
          const slotStart = dayjs(slot.startTime);
          const slotEnd = dayjs(slot.endTime);
          
          const slotConflicts = conflictingMeetings.filter(meeting => {
            const meetingTime = dayjs(meeting.proposedTime);
            const meetingEndTime = meetingTime.add(1, 'hour');
            
            return (
              (meetingTime.isBefore(slotEnd) && meetingEndTime.isAfter(slotStart)) ||
              (slotStart.isBefore(meetingEndTime) && slotEnd.isAfter(meetingTime))
            );
          });
          
          if (process.env.NODE_ENV === 'development' && slotConflicts.length > 0) {
            console.log('🚫 Slot conflicts found:', {
              slotStart: slotStart.format(),
              slotEnd: slotEnd.format(),
              conflicts: slotConflicts.map(m => ({
                proposedTime: dayjs(m.proposedTime).format(),
                type: m.type
              }))
            });
          }
          
          const hasConflict = slotConflicts.length > 0;
          const isPast = dayjs(slot.startTime).isBefore(dayjs());
          const isDisabled = isBooked || hasConflict || isPast;

          const tooltipTitle = isBooked 
            ? "Already booked"
            : isPast
              ? "This slot is in the past"
              : hasConflict 
                ? `Conflicting ${slotConflicts[0]?.type} meeting at ${dayjs(slotConflicts[0]?.proposedTime).format("h:mm A")}`
                : "Book this slot";
          
          const slotClassName = `mb-1 p-1 text-xs rounded ${
            isDisabled 
              ? 'bg-gray-100 text-gray-500 cursor-not-allowed' 
              : 'cursor-pointer bg-blue-100 hover:bg-blue-200'
          }`;
          
          return (
            <Tooltip key={slot.id} title={tooltipTitle}>
              <li
                className={slotClassName}
                onClick={(e) => {
                  e.stopPropagation();
                  if (!isDisabled) {
                    setSlotToBook(slot);
                    setIsSlotModalOpen(true);
                  }
                }}
              >
                <div className="flex justify-between items-center">
                  <div>
                    <span>
                      {dayjs(slot.startTime).format("h:mm A")} - {dayjs(slot.endTime).format("h:mm A")}
                    </span>
                    <span className="ml-2 text-xs text-gray-600">
                      ({slot.meetingMethod === "IN_PERSON" ? "In-person" : "Virtual"})
                    </span>
                  </div>
                  <BookOutlined className={isDisabled ? "text-gray-400" : "text-blue-600"} />
                </div>
              </li>
            </Tooltip>
          );
        })}
      </ul>
    );
  };

  const handleBookSlot = async () => {
    try {
      if (!mentorId || !slotToBook) {
        message.error("No slot selected");
        return;
      }

      // Double check conflicts right before booking using the new endpoint
      const conflictsResponse = await fetch(
        `/api/meetings/conflicts?startTime=${encodeURIComponent(dayjs(slotToBook.startTime).toISOString())}&endTime=${encodeURIComponent(dayjs(slotToBook.endTime).toISOString())}&mentorId=${mentorId}`
      );
      
      if (!conflictsResponse.ok) throw new Error("Failed to check conflicts");
      const { conflicts } = await conflictsResponse.json();
      
      if (conflicts && conflicts.length > 0) {
        message.error("This slot is no longer available due to a scheduling conflict");
        setIsSlotModalOpen(false);
        setSlotToBook(null);
        await fetchAvailabilitiesAndConflicts();
        return;
      }

      const values = await form.validateFields();
      setLoading(true);
      
      const payload = {
        type: MeetingType.MONTHLY,
        mentorId: mentorId,
        notes: values.notes,
        availabilityId: slotToBook.id,
        proposedTime: dayjs(slotToBook.startTime).toISOString(),
        reviewerRole: UserRole.MENTOR,
      };

      const response = await fetch("/api/meetings", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || "Failed to create meeting");
      }

      message.success("Meeting request sent successfully");
      form.resetFields();
      setSlotToBook(null);
      setIsSlotModalOpen(false);
      await fetchAvailabilitiesAndConflicts();
      onSuccess();
    } catch (error) {
      if (error instanceof Error) {
        message.error(error.message);
      } else {
        message.error("Failed to create meeting");
      }
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Modal
        title="Book a Meeting"
        open={open}
        onCancel={onClose}
        footer={null}
        width={900}
      >
        <div className="mb-4">
          <p>Mentor: {mentor?.name}</p>
          <p>Email: {mentor?.email}</p>
        </div>
        
        {dataLoading ? (
          <div className="flex flex-col items-center justify-center py-16">
            <Spin size="large" />
            <div className="mt-4 text-gray-600">Loading available time slots...</div>
          </div>
        ) : (
          <div className="mb-4">
            <Calendar
              cellRender={dateCellRender}
              mode="month"
              className="hide-mode-switch"
            />
            <div className="mt-2 text-sm text-gray-500">
              {availabilities.length > 0 
                ? "Click a slot in the calendar to book it."
                : "No available time slots found."}
            </div>
          </div>
        )}
        <style jsx global>{`
          .hide-mode-switch .ant-picker-calendar-mode-switch {
            display: none !important;
          }
        `}</style>
      </Modal>

      <Modal
        title="Confirm Booking"
        open={isSlotModalOpen}
        onCancel={() => {
          setIsSlotModalOpen(false);
          setSlotToBook(null);
        }}
        onOk={handleBookSlot}
        okText="Book Meeting"
        confirmLoading={loading}
        destroyOnClose
      >
        {slotToBook && (
          <>
            <div className="mb-4 p-3 rounded bg-blue-50 border border-blue-100">
              <div className="font-medium">Selected Slot</div>
              <div>{dayjs(slotToBook.startTime).format("dddd, MMMM D, YYYY")}</div>
              <div>
                {dayjs(slotToBook.startTime).format("h:mm A")} - {dayjs(slotToBook.endTime).format("h:mm A")}
              </div>
              <div className="text-xs text-gray-600">
                {slotToBook.meetingMethod === "IN_PERSON" ? "In-person Meeting" : "Virtual Meeting"}
              </div>
            </div>
            <Form form={form} layout="vertical">
              <Form.Item name="notes" label="Notes">
                <Input.TextArea
                  rows={4}
                  placeholder="Add any notes or agenda items for the meeting"
                />
              </Form.Item>
            </Form>
          </>
        )}
      </Modal>
    </>
  );
}
