import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { Modal, Steps, Form, Input, Select, Button, Calendar, App, TimePicker } from "antd";
import type { Dayjs } from "dayjs";
import dayjs from "dayjs";
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { DeleteOutlined, PlusOutlined, CloseOutlined } from "@ant-design/icons";
import type { CalendarProps } from "antd";
import { MentorAvailability } from "@/types";

// Configure dayjs to use timezone
dayjs.extend(utc);
dayjs.extend(timezone);

interface TimeSlot {
  startTime: string;
  endTime: string;
  key: string;
  date?: string;
  isExisting: boolean;
}

interface AvailabilityModalProps {
  availabilities: MentorAvailability[];
  onUpdateDateSlots: (
    date: Date,
    slots: {
      startTime: Date;
      endTime: Date;
      meetingMethod: "IN_PERSON" | "VIRTUAL";
      duration?: number;
      bufferTime?: number;
      meetingLocation?: string;
      meetingMessage?: string;
    }[]
  ) => Promise<void>;
}

export interface AvailabilityModalRef {
  open: () => void;
  close: () => void;
}

const AvailabilityModal = forwardRef<AvailabilityModalRef, AvailabilityModalProps>(({
  availabilities,
  onUpdateDateSlots,
}, ref) => {
  // State management
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [isCreating, setIsCreating] = useState(false);
  const [meetingDuration, setMeetingDuration] = useState<number>(60);
  const [bufferTime, setBufferTime] = useState<number>(10);
  const [meetingMethod, setMeetingMethod] = useState<"IN_PERSON" | "VIRTUAL">("VIRTUAL");
  const [meetingMessage, setMeetingMessage] = useState<string>("");
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [form] = Form.useForm();
  const [isTimeSlotModalOpen, setIsTimeSlotModalOpen] = useState(false);
  const [timeSlotModalDate, setTimeSlotModalDate] = useState<Dayjs | null>(null);
  const { message } = App.useApp();
  const dateCellRender = (date: Dayjs) => {
    const dayAvailabilities = availabilities.filter((slot) => {
      const slotDate = dayjs(slot.startTime);
      return (
        date.date() === slotDate.date() &&
        date.month() === slotDate.month() &&
        date.year() === slotDate.year()
      );
    });

    if (dayAvailabilities.length === 0) return null;

    return (
      <ul className="list-none p-0">
        {dayAvailabilities.map((slot) => {
          const method = slot.meetingMethod;
          
          return (
            <li
              key={slot.id}
              className={`mb-1 p-1 text-xs rounded ${slot.isBooked ? "bg-gray-200" : "bg-blue-100"}`}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-center">
                <div>
                  <span>
                    {dayjs(slot.startTime).format("h:mm A")} -{" "}
                    {dayjs(slot.endTime).format("h:mm A")}
                  </span>
                  <span className="ml-2 text-xs text-gray-600">
                    ({method === "IN_PERSON" ? "In-person" : "Virtual"})
                  </span>
                </div>
              </div>
            </li>
          );
        })}
      </ul>
    );
  };

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    open: () => {
      setIsModalOpen(true);
      setCurrentStep(1);
    },
    close: () => {
      resetWorkflow();
    }
  }));

  const resetWorkflow = () => {
    setCurrentStep(0);
    setIsModalOpen(false);
    setMeetingDuration(60);
    setBufferTime(10);
    setMeetingMethod("VIRTUAL");
    setMeetingMessage("");
    setTimeSlots([]);
    form.resetFields();
  };

  const handleCloseModal = () => {
    if (!isCreating) {
      resetWorkflow();
    }
  };

  const handleNext = () => {
    if (currentStep === 0) {
      setIsModalOpen(true);
      setCurrentStep(1);
    } else if (currentStep === 1) {
      form.validateFields().then(() => {
        setCurrentStep(2);
      });
    }
  };

  const handleBack = () => {
    if (currentStep === 1) {
      setCurrentStep(0);
      setIsModalOpen(false);
    } else if (currentStep === 2) {
      setCurrentStep(1);
    }
  };

  const handleDateSelect: CalendarProps<Dayjs>['onSelect'] = (date, { source }) => {
    if (source === 'date') {
      setTimeSlotModalDate(date);
      // Load existing time slots for the selected date
      const dateStr = date.format('DD/MM/YYYY');
      const existingSlots = availabilities
        .filter(slot => {
          const slotDate = dayjs(slot.startTime);
          return (
            date.date() === slotDate.date() &&
            date.month() === slotDate.month() &&
            date.year() === slotDate.year()
          );
        })
        .map(slot => ({
          startTime: dayjs(slot.startTime).format('HH:mm'),
          endTime: dayjs(slot.endTime).format('HH:mm'),
          key: slot.id,
          date: dateStr,
          isExisting: true // Mark existing slots
        }));
      setTimeSlots(existingSlots);
      setIsTimeSlotModalOpen(true);
    }
  };

  const addTimeSlot = () => {
    let defaultStartTime = "08:00";

    if (timeSlots.length > 0) {
      const lastSlot = timeSlots[timeSlots.length - 1];
      const [lastEndHour, lastEndMinute] = lastSlot.endTime.split(":").map(Number);
      const totalMinutes = lastEndHour * 60 + lastEndMinute + bufferTime;
      const newStartHour = Math.floor(totalMinutes / 60);
      const newStartMinute = totalMinutes % 60;
      defaultStartTime = `${newStartHour.toString().padStart(2, '0')}:${newStartMinute.toString().padStart(2, '0')}`;
    }

    const [startHour, startMinute] = defaultStartTime.split(":").map(Number);
    const totalMinutes = startHour * 60 + startMinute + meetingDuration;
    const endHour = Math.floor(totalMinutes / 60);
    const endMinute = totalMinutes % 60;

    const endTime = `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`;

    const newSlot = {
      startTime: defaultStartTime,
      endTime: endTime,
      key: Math.random().toString(36).substring(2, 9),
      date: timeSlotModalDate?.format('DD/MM/YYYY'),
      isExisting: false // Mark new slots
    };

    setTimeSlots([...timeSlots, newSlot]);
  };

  const removeTimeSlot = (key: string) => {
    setTimeSlots(timeSlots.filter(slot => slot.key !== key));
  };

  const updateTimeSlot = (key: string, field: 'startTime' | 'endTime', value: string) => {
    // Only allow updating non-existing slots
    const slot = timeSlots.find(s => s.key === key);
    if (slot?.isExisting) return;

    if (field === 'startTime') {
      const [startHour, startMinute] = value.split(":").map(Number);
      const totalMinutes = startHour * 60 + startMinute + meetingDuration;
      const endHour = Math.floor(totalMinutes / 60);
      const endMinute = totalMinutes % 60;

      const endTime = `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`;

      const updatedTimeSlots = timeSlots.map(slot =>
        slot.key === key ? { ...slot, startTime: value, endTime: endTime } : slot
      );

      const currentIndex = updatedTimeSlots.findIndex(slot => slot.key === key);

      if (currentIndex < updatedTimeSlots.length - 1) {
        for (let i = currentIndex + 1; i < updatedTimeSlots.length; i++) {
          const prevSlot = updatedTimeSlots[i - 1];
          if (prevSlot.isExisting) continue; // Skip updating existing slots

          const [prevEndHour, prevEndMinute] = prevSlot.endTime.split(":").map(Number);

          const newStartMinutes = prevEndHour * 60 + prevEndMinute + bufferTime;
          const newStartHour = Math.floor(newStartMinutes / 60);
          const newStartMinute = newStartMinutes % 60;
          const newStartTime = `${newStartHour.toString().padStart(2, '0')}:${newStartMinute.toString().padStart(2, '0')}`;

          const newEndMinutes = newStartHour * 60 + newStartMinute + meetingDuration;
          const newEndHour = Math.floor(newEndMinutes / 60);
          const newEndMinute = newEndMinutes % 60;
          const newEndTime = `${newEndHour.toString().padStart(2, '0')}:${newEndMinute.toString().padStart(2, '0')}`;

          updatedTimeSlots[i] = { ...updatedTimeSlots[i], startTime: newStartTime, endTime: newEndTime };
        }
      }

      setTimeSlots(updatedTimeSlots);
    } else {
      setTimeSlots(timeSlots.map(slot =>
        slot.key === key ? { ...slot, [field]: value } : slot
      ));
    }
  };

  const handleSaveTimeSlots = async () => {
    if (!timeSlotModalDate) return;

    try {
      setIsCreating(true);
      
      // Only prepare new time slots (not existing ones)
      const newSlots = timeSlots
        .filter(slot => !slot.isExisting)
        .map(slot => {
          // Create the date in Europe/London timezone to match email timezone
          const startDateTime = dayjs(timeSlotModalDate)
            .hour(parseInt(slot.startTime.split(':')[0]))
            .minute(parseInt(slot.startTime.split(':')[1]))
            .second(0)
            .millisecond(0)
            .tz('Europe/London')
            .toDate();
          
          const endDateTime = dayjs(timeSlotModalDate)
            .hour(parseInt(slot.endTime.split(':')[0]))
            .minute(parseInt(slot.endTime.split(':')[1]))
            .second(0)
            .millisecond(0)
            .tz('Europe/London')
            .toDate();

          return {
            startTime: startDateTime,
            endTime: endDateTime,
            meetingMethod,
            duration: meetingDuration,
            bufferTime,
            meetingMessage
          };
        });

      // Only call onUpdateDateSlots if there are new slots to add
      if (newSlots.length > 0) {
        await onUpdateDateSlots(timeSlotModalDate.toDate(), newSlots as any);
        message.success("New time slots added successfully");
      } else {
        message.info("No new time slots to add");
      }
      
      setIsTimeSlotModalOpen(false);
      // Clear the time slots after successful creation
      setTimeSlots([]);
    } catch (error) {
      console.error('Failed to update time slots:', error);
      message.error('Failed to update time slots');
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Modal
      title={(
        <div className="flex items-center justify-between w-full">
          <div className="flex-grow max-w-[400px]">
            <Steps
              current={currentStep - 1}
              size="small"
              className="flex-grow"
              items={[
                { title: 'Meeting Details' },
                { title: 'Availability' },
              ]}
            />
          </div>
          <Button
            type="text"
            icon={<CloseOutlined style={{ fontSize: 20 }} />}
            onClick={handleCloseModal}
            disabled={isCreating}
            className="ml-4"
            aria-label="Close"
          />
        </div>
      )}
      open={isModalOpen}
      onCancel={handleCloseModal}
      width={900}
      footer={null}
      closable={false}
      maskClosable={!isCreating}
      className="availability-modal"
    >
      {currentStep === 1 && (
        <div className="py-4 flex flex-col items-center">
          <h2 className="text-xl mb-6 text-center font-semibold">Mentee Meeting Details</h2>
          <div className="bg-blue-50 p-4 rounded-lg mb-6">
            <p className="text-blue-800">Configure your mentee meeting settings below. These settings will apply to all time slots you create in the next step.</p>
          </div>
          <Form form={form} layout="vertical" className="w-full max-w-2xl">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Form.Item
                  label={<span className="font-medium">Meeting Duration</span>}
                  help="Duration of each mentee meeting"
                  required
                  name="duration"
                  initialValue={meetingDuration}
                >
                  <Input
                    type="number"
                    value={meetingDuration}
                    onChange={(e) => setMeetingDuration(Number(e.target.value))}
                    className="w-full"
                    min={5}
                    max={240}
                    suffix="minutes"
                  // addonAfter={<span>{Math.floor(meetingDuration/60) > 0 ? `${Math.floor(meetingDuration/60)}h ${meetingDuration%60}m` : ''}</span>}
                  />
                </Form.Item>
              </div>

              <div>
                <Form.Item
                  label={<span className="font-medium">Buffer Time</span>}
                  help="Time between consecutive meetings"
                  name="bufferTime"
                  initialValue={bufferTime}
                >
                  <Input
                    type="number"
                    value={bufferTime}
                    onChange={(e) => setBufferTime(Number(e.target.value))}
                    className="w-full"
                    min={0}
                    max={60}
                    suffix="minutes"
                  />
                </Form.Item>
              </div>
            </div>

            <div className="mb-6 mt-4">
              <Form.Item
                label={<span className="font-medium">Meeting Method</span>}
                name="meetingMethod"
                required
                initialValue={meetingMethod || "VIRTUAL"}
              >
                <Select
                  value={meetingMethod || "VIRTUAL"}
                  onChange={(value: "IN_PERSON" | "VIRTUAL") => setMeetingMethod(value)}
                  className="w-full"
                  size="large"
                  defaultValue="VIRTUAL"
                >
                  <Select.Option value="VIRTUAL">
                    <div className="flex items-center">
                      <span className="mr-2">🖥️</span>
                      <span>Virtual (Online meeting)</span>
                    </div>
                  </Select.Option>
                  <Select.Option value="IN_PERSON">
                    <div className="flex items-center">
                      <span className="mr-2">🏢</span>
                      <span>In-person (Physical location)</span>
                    </div>
                  </Select.Option>
                </Select>
              </Form.Item>
            </div>
            <div className="mb-6">
              <Form.Item
                label={<span className="font-medium">Message for Mentees</span>}
                help="Add any details or instructions for mentees"
                name="message"
              >
                <Input.TextArea
                  placeholder="E.g., Please prepare questions in advance, bring your portfolio, etc."
                  value={meetingMessage}
                  onChange={(e) => setMeetingMessage(e.target.value)}
                  className="w-full"
                  rows={4}
                />
              </Form.Item>
            </div>
          </Form>

          <div className="flex justify-between mt-8 self-end gap-4">
            <Button onClick={handleBack} size="large">
              Back
            </Button>
            <Button type="primary" onClick={handleNext} size="large">
              Next: Select Availability
            </Button>
          </div>
        </div>
      )}

      {currentStep === 2 && (
        <div className="py-4 flex flex-col items-center">
          <h2 className="text-xl mb-6 text-center font-semibold">Select Your Availability</h2>
          <div className="bg-blue-50 p-4 rounded-lg mb-6">
            <p className="text-blue-800">Select dates on the calendar and add specific time slots when you&apos;re available for mentee meetings.</p>
          </div>
          <Calendar
            onSelect={handleDateSelect}
            cellRender={dateCellRender}
            mode="month"
            className="hide-mode-switch"
          />
          {/* <div className="mt-4">
            <div className="flex items-center mb-2">
              <div className="w-4 h-4 bg-blue-100 rounded mr-2"></div>
              <span className="text-sm">Available</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 bg-gray-200 rounded mr-2"></div>
              <span className="text-sm">Booked</span>
            </div>
          </div> */}
          <div className="flex justify-between mt-8 self-end gap-4">
            <Button onClick={handleBack} size="large">
              Back
            </Button>
            <Button type="primary" onClick={resetWorkflow} size="large">
              Done
            </Button>
          </div>
          <Modal
            open={isTimeSlotModalOpen}
            onCancel={() => setIsTimeSlotModalOpen(false)}
            title={timeSlotModalDate ? `Add Time Slots for ${timeSlotModalDate.format('dddd, MMMM D, YYYY')}` : 'Add Time Slots'}
            footer={null}
            width={600}
          >
            <div className="py-4">
              <div className="mb-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium">Time Slots</h3>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={addTimeSlot}
                    disabled={isCreating}
                  >
                    Add Time Slot
                  </Button>
                </div>
                
                {timeSlots.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <p>No time slots added yet. Click &quot;Add Time Slot&quot; to create one.</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {timeSlots.map((slot) => (
                      <div
                        key={slot.key}
                        className={`flex items-center gap-4 p-3 rounded-lg ${
                          slot.isExisting ? 'bg-gray-100' : 'bg-gray-50'
                        }`}
                      >
                        <div className="flex-1 flex items-center gap-4">
                          <div className="flex items-center gap-2">
                            <span className="text-gray-600">Start:</span>
                            <TimePicker
                              format="HH:mm"
                              minuteStep={5}
                              value={slot.startTime ? dayjs(slot.startTime, 'HH:mm') : null}
                              onChange={(time) => updateTimeSlot(slot.key, 'startTime', time ? time.format('HH:mm') : '')}
                              className="w-32"
                              disabled={isCreating || slot.isExisting}
                            />
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-gray-600">End:</span>
                            <TimePicker
                              format="HH:mm"
                              minuteStep={5}
                              value={slot.endTime ? dayjs(slot.endTime, 'HH:mm') : null}
                              onChange={(time) => updateTimeSlot(slot.key, 'endTime', time ? time.format('HH:mm') : '')}
                              className="w-32"
                              disabled={isCreating || slot.isExisting}
                            />
                          </div>
                        </div>
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => removeTimeSlot(slot.key)}
                          disabled={isCreating}
                        />
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div className="flex justify-end gap-3 mt-6">
                <Button onClick={() => setIsTimeSlotModalOpen(false)} disabled={isCreating}>
                  Cancel
                </Button>
                <Button 
                  type="primary" 
                  onClick={handleSaveTimeSlots}
                  loading={isCreating}
                >
                  Save Time Slots
                </Button>
              </div>
            </div>
          </Modal>
        </div>
      )}
    </Modal>
  );
});
AvailabilityModal.displayName = 'AvailabilityModal';

export default AvailabilityModal; 