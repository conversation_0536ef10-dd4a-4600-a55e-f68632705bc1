import { <PERSON>, But<PERSON>, Tag, Space, Tooltip, App } from "antd";
import type { TableProps } from "antd";
import {
  FileOutlined,
  FormOutlined,
  InfoCircleOutlined,
  EditOutlined,
} from "@ant-design/icons";
import { Meeting, MeetingFormat, MeetingStatus, MeetingType } from "@/types";
import dayjs from "dayjs";
import { useState, useRef } from "react";
import CompleteMeetingModal from "./CompleteMeetingModal";
import FeedbackFormModal from "./FeedbackFormModal";
import { viewDocument } from "@/lib/utils";
import MeetingDetailModal, {
  MeetingDetailModalRef,
} from "@/app/trainee/appraisals/components/MeetingDetailModal";

interface MeetingsListProps {
  meetings: Meeting[];
  loading: boolean;
  onStatusChange: (meetingId: string, status: MeetingStatus) => void;
  onBookMeeting?: () => void;
  isMentor: boolean;
  mode: "requests" | "completed";
  onRefresh?: () => void;
  onEdit?: (meeting: Meeting) => void;
}

const MeetingTypeLabel = {
  [MeetingType.MONTHLY]: "Monthly Review",
  [MeetingType.TPM]: "TPM",
  [MeetingType.APPRAISAL]: "Appraisal",
};

export default function MeetingsList({
  meetings,
  loading,
  onStatusChange,
  onBookMeeting,
  isMentor,
  mode,
  onRefresh,
  onEdit,
}: MeetingsListProps) {
  const [selectedMeeting, setSelectedMeeting] = useState<Meeting | null>(null);
  const [isCompleteModalOpen, setIsCompleteModalOpen] = useState(false);
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  const [selectedFeedbackFormId, setSelectedFeedbackFormId] = useState<
    string | null
  >(null);
  const meetingDetailModalRef = useRef<MeetingDetailModalRef>(null);
  const { message } = App.useApp();
  const handleViewDocument = (s3Key: string) => {
    if (s3Key) {
      viewDocument(s3Key);
    } else {
      message.error("Document not found");
    }
  };

  const handleViewFeedbackForm = (feedbackFormId: string) => {
    if (feedbackFormId) {
      setSelectedFeedbackFormId(feedbackFormId);
      setIsFeedbackModalOpen(true);
    } else {
      message.error("Feedback form not found");
    }
  };

  const handleCompleteMeeting = async (values: {
    documentName: string;
    action?: string;
    s3Key?: string;
    feedbackFormId?: string;
    documentType?: string;
  }) => {
    if (!selectedMeeting) return;

    try {
      const response = await fetch(
        `/api/meetings/${selectedMeeting.id}/complete`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(values),
        }
      );

      if (!response.ok) throw new Error("Failed to complete meeting");

      message.success("Meeting completed successfully");
      setIsCompleteModalOpen(false);
      setSelectedMeeting(null);
      onStatusChange(selectedMeeting.id, MeetingStatus.COMPLETED);
      onRefresh?.();
    } catch (error) {
      console.error("Error completing meeting:", error);
      message.error("Failed to complete meeting");
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case MeetingStatus.PENDING:
        return "gold";
      case MeetingStatus.ACCEPTED:
        return "green";
      case MeetingStatus.DECLINED:
        return "red";
      case MeetingStatus.COMPLETED:
        return "blue";
      default:
        return "default";
    }
  };

  const filteredMeetings = meetings.filter((meeting) => {
    if (mode === "requests") {
      // For trainees, show all meetings. For mentors, exclude completed ones from requests view
      if (isMentor) {
        return meeting.status !== MeetingStatus.COMPLETED;
      } else {
        return true; // Trainees see all their meetings
      }
    } else {
      // completed
      return (
        meeting.status === MeetingStatus.COMPLETED ||
        meeting.status === MeetingStatus.ACCEPTED
      );
    }
  });

  const showMeetingInfo = (meeting: Meeting) => {
    meetingDetailModalRef.current?.open(meeting);
  };

  const columns: TableProps<Meeting>["columns"] = [
    {
      title: "Type",
      dataIndex: "type",
      key: "type",
      render: (type: string) => MeetingTypeLabel[type as MeetingType],
    },
    {
      title: "Method",
      key: "meetingMethod",
      render: (meeting: Meeting) => {
        if (!meeting?.availability?.meetingMethod) return "";
        if (meeting?.availability?.meetingMethod === MeetingFormat.VIRTUAL) {
          return "Virtual";
        } else {
          return "In-Person";
        }
      },
    },
    {
      title: "Date & Time",
      dataIndex: "proposedTime",
      key: "proposedTime",
      sorter: (a: Meeting, b: Meeting) =>
        new Date(a.proposedTime).getTime() - new Date(b.proposedTime).getTime(),
      render: (date: string) => dayjs(date).format("DD/MM/YYYY h:mm A"),
    },
    {
      title: isMentor ? "Trainee" : "Mentor",
      dataIndex: isMentor ? "candidate" : "mentor",
      key: "participant",
      render: (participant: { name: string; email: string }) => (
        <div>
          <div>{participant.name}</div>
          <div className="text-gray-500 text-sm">{participant.email}</div>
        </div>
      ),
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      filters: Object.values(MeetingStatus)
        .filter((status) => {
          if (mode === "requests") {
            return status !== MeetingStatus.COMPLETED;
          } else {
            return (
              status === MeetingStatus.COMPLETED ||
              status === MeetingStatus.ACCEPTED
            );
          }
        })
        .map((status) => ({
          text: status,
          value: status,
        })),
      onFilter: (value, record) => record.status === value,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>{status}</Tag>
      ),
    },
    {
      title: "Actions",
      key: "actions",
      render: (_: unknown, record: Meeting) => (
        <Space>
          {isMentor && (
            <>
              {mode === "requests" &&
                record.status === MeetingStatus.PENDING && (
                  <>
                    <Button
                      type="primary"
                      size="small"
                      onClick={() =>
                        onStatusChange(record.id, MeetingStatus.ACCEPTED)
                      }
                    >
                      Accept
                    </Button>
                    <Button
                      danger
                      size="small"
                      onClick={() =>
                        onStatusChange(record.id, MeetingStatus.DECLINED)
                      }
                    >
                      Decline
                    </Button>
                  </>
                )}
                <Tooltip title="Meeting Info">
                  <Button
                    icon={<InfoCircleOutlined />}
                    size="small"
                    onClick={() => showMeetingInfo(record)}
                  />
                </Tooltip>
              {record.status === MeetingStatus.ACCEPTED && (
                <>
                  <Button
                    type="primary"
                    size="small"
                    onClick={() => {
                      setSelectedMeeting(record);
                      setIsCompleteModalOpen(true);
                    }}
                  >
                    Finish Meeting
                  </Button>
                </>
              )}
              {record.status === MeetingStatus.COMPLETED && (
                <>
                  {record.monthlyReview?.s3Key && (
                    <>
                      <Tooltip title="View Monthly Review Document">
                        <Button
                          icon={<FileOutlined />}
                          size="small"
                          onClick={() =>
                            handleViewDocument(
                              record.monthlyReview?.s3Key || ""
                            )
                          }
                        />
                      </Tooltip>
                    </>
                  )}
                  {record.type === "MONTHLY" &&
                    record.monthlyReview?.feedbackForm && (
                      <Tooltip title="View Feedback Form">
                        <Button
                          icon={<FormOutlined />}
                          size="small"
                          onClick={() =>
                            handleViewFeedbackForm(
                              record.monthlyReview!.feedbackForm!.id
                            )
                          }
                        />
                      </Tooltip>
                    )}
                  <Tooltip title="Edit Meeting">
                    <Button
                      icon={<EditOutlined />}
                      size="small"
                      onClick={() => onEdit?.(record)}
                    />
                  </Tooltip>
                </>
              )}
            </>
          )}
          {!isMentor &&
            (record.status === MeetingStatus.ACCEPTED ||
              record.status === MeetingStatus.COMPLETED) && (
              <Tooltip title="Meeting Info">
                <Button
                  type="primary"
                  icon={<InfoCircleOutlined />}
                  size="small"
                  onClick={() => showMeetingInfo(record)}
                >
                  Info
                </Button>
              </Tooltip>
            )}
        </Space>
      ),
    },
  ];

  return (
    <div className="w-full">
      {mode === "requests" && onBookMeeting && (
        <div className="mb-4">
          <Button type="primary" onClick={onBookMeeting}>
            Book a Meeting
          </Button>
        </div>
      )}

      <Table
        columns={columns}
        dataSource={filteredMeetings}
        rowKey="id"
        loading={loading}
        scroll={{ x: 1000 }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} meetings`,
        }}
      />

      <CompleteMeetingModal
        open={isCompleteModalOpen}
        onClose={() => {
          setIsCompleteModalOpen(false);
          setSelectedMeeting(null);
        }}
        meeting={selectedMeeting || undefined}
        onComplete={handleCompleteMeeting}
        onSuccess={onRefresh}
      />

      <FeedbackFormModal
        isOpen={isFeedbackModalOpen}
        onClose={() => {
          setIsFeedbackModalOpen(false);
          setSelectedFeedbackFormId(null);
        }}
        feedbackFormId={selectedFeedbackFormId}
      />

      <MeetingDetailModal ref={meetingDetailModalRef} />
    </div>
  );
}
