import { useState, useEffect } from 'react';
import { Modal, Input, Select, Button, Typography, Form, Card, Space, Divider, Upload, App, DatePicker } from 'antd';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import { Meeting, MeetingType, MeetingFormat } from '@/types';
import Link from 'next/link';
import { UploadOutlined, DeleteOutlined, DownloadOutlined } from '@ant-design/icons';
import { viewDocument } from '@/lib/utils';
import dayjs from 'dayjs';

const { Text } = Typography;
const { Option } = Select;

const MeetingTypeLabel = {
  [MeetingType.MONTHLY]: "Monthly Review",
  [MeetingType.TPM]: "TPM",
  [MeetingType.APPRAISAL]: "Appraisal",
};

interface Mentee {
  id: string;
  name: string;
  email: string;
}

interface CompleteMeetingModalProps {
  open: boolean;
  onClose: () => void;
  meeting?: Meeting;
  monthlyReviewId?: string;
  mode?: 'create' | 'complete' | 'edit';
  onComplete: (values: any) => void;
  onSuccess?: () => void;
  isTrainingPrincipal?: boolean;
}

interface FormState {
  menteeId?: string;
  meetingMethod?: MeetingFormat;
  meetingType?: MeetingType;
  meetingDateTime?: dayjs.Dayjs;
  documentName: string;
  action: string;
  s3Key: string;
  feedbackFormId: string;
  tempMonthlyReviewId?: string;
  file?: UploadFile;
}

const actionOptions = [
  { value: 'approved', text: 'Approve' },
  { value: 'referred', text: 'Escalate to Training Principal' },
];

const meetingMethodOptions = [
  { value: MeetingFormat.IN_PERSON, text: 'In-Person' },
  { value: MeetingFormat.VIRTUAL, text: 'Virtual' },
];

const meetingTypeOptions = [
  { value: MeetingType.MONTHLY, text: 'Monthly' },
  { value: MeetingType.APPRAISAL, text: 'Appraisal' },
];

export default function CompleteMeetingModal({
  open,
  onClose,
  meeting,
  monthlyReviewId,
  mode = 'complete',
  onComplete,
  onSuccess,
  isTrainingPrincipal,
}: CompleteMeetingModalProps) {
  const [formState, setFormState] = useState<FormState>({
    documentName: '',
    action: '',
    s3Key: '',
    feedbackFormId: '',
  });
  const [loading, setLoading] = useState(false);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [feedbackFormLoading, setFeedbackFormLoading] = useState(false);
  const [hasFile, setHasFile] = useState(false);
  const [fileName, setFileName] = useState('');
  const [mentees, setMentees] = useState<Mentee[]>([]);
  const [templateLoading, setTemplateLoading] = useState(false);
  const { message } = App.useApp();

  useEffect(() => {
    if (mode === 'create' && open) {
      fetchMentees();
    }
    
    if (mode === 'edit' && meeting && open) {
      fetchMentees();
      setFormState({
        menteeId: meeting.candidateId,
        meetingMethod: meeting.availability?.meetingMethod,
        meetingType: meeting.type,
        meetingDateTime: dayjs(meeting.proposedTime),
        documentName: meeting.monthlyReview?.documentName || '',
        action: meeting.monthlyReview?.action || '',
        s3Key: meeting.monthlyReview?.s3Key || '',
        feedbackFormId: meeting.monthlyReview?.feedbackForm?.id || '',
      });
      
      if (meeting.monthlyReview?.s3Key) {
        setHasFile(true);
        setFileName(meeting.monthlyReview?.documentName || 'Document');
      }
    }
  }, [mode, open, meeting]);

  const fetchMentees = async () => {
    try {
      const response = await fetch('/api/mentor/mentees');
      if (!response.ok) throw new Error('Failed to fetch mentees');
      const data = await response.json();
      setMentees(data);
    } catch (error) {
      console.error('Error fetching mentees:', error);
      message.error('Failed to fetch mentees');
    }
  };

  const handleUpload: UploadProps['customRequest'] = async (options) => {
    const { file, onSuccess, onError } = options;
    try {
      setUploadLoading(true);
      const formData = new FormData();
      formData.append('file', file as Blob);
      formData.append('entityType', 'document');
      
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Upload failed');
      }

      const data = await response.json();
      
      if (data.file?.s3Key) {
        setFormState(prev => ({
          ...prev,
          s3Key: data.file.s3Key,
          documentName: (file as File).name,
        }));
        
        setHasFile(true);
        setFileName((file as File).name);

        onSuccess?.(data, file);
        message.success('Document uploaded successfully');
      } else {
        throw new Error('Invalid upload response');
      }
    } catch (error) {
      console.error('Error uploading document:', error);
      onError?.(error as Error);
      message.error('Failed to upload document. Please try again.');
      handleDeleteFile();
    } finally {
      setUploadLoading(false);
    }
  };

  const handleDeleteFile = () => {
    setFormState(prev => ({
      ...prev,
      s3Key: '',
      documentName: '',
      file: undefined,
    }));
    setHasFile(false);
    setFileName('');
  };

  const handleCreateFeedbackForm = async () => {
    try {
      setFeedbackFormLoading(true);
      
      const response = await fetch('/api/feedback-forms', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          menteeName: mode === 'create' 
            ? mentees.find(m => m.id === formState.menteeId)?.name
            : meeting?.candidate.name,
          meetingDate: mode === 'create' 
            ? formState.meetingDateTime?.toDate()
            : meeting?.proposedTime,
          meetingId: mode === 'create' ? undefined : meeting?.id,
          candidateId: mode === 'create' ? formState.menteeId : undefined,
        }),
      });

      if (!response.ok) throw new Error('Failed to create feedback form');
      
      const data = await response.json();
      
      // Store both feedbackFormId and tempMonthlyReviewId for create mode
      setFormState(prev => ({
        ...prev,
        feedbackFormId: data.id,
        tempMonthlyReviewId: data.monthlyReviewId || undefined,
      }));

      window.open(`/feedbackForm/${data.id}/edit`, '_blank');
    } catch (error) {
      console.error('Error creating feedback form:', error);
      message.error('Failed to create feedback form');
    } finally {
      setFeedbackFormLoading(false);
    }
  };

  const handleDownloadTemplate = async () => {
    try {
      setTemplateLoading(true);
      const response = await fetch('/api/meeting-templates/active');
      
      if (!response.ok) {
        if (response.status === 404) {
          message.error('No active meeting template found. Please contact admin.');
          return;
        }
        throw new Error('Failed to fetch template');
      }

      const template = await response.json();
      
      // Create download link
      const link = document.createElement('a');
      link.target = '_blank';
      link.href = template.downloadUrl;
      link.download = template.fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      message.success('Template downloaded successfully');
    } catch (error) {
      console.error('Error downloading template:', error);
      message.error('Failed to download template');
    } finally {
      setTemplateLoading(false);
    }
  };

  const resetModalState = () => {
    setFormState({
      documentName: '',
      action: '',
      s3Key: '',
      feedbackFormId: '',
      tempMonthlyReviewId: undefined,
    });
    setLoading(false);
    setUploadLoading(false);
    setFeedbackFormLoading(false);
    setTemplateLoading(false);
    setHasFile(false);
    setFileName('');
  };

  const handleDeleteFeedbackForm = async () => {
    if (!monthlyReviewId && formState.feedbackFormId) {
      try {
        await fetch(`/api/feedback-forms/${formState.feedbackFormId}`, {
          method: 'DELETE',
        });
      } catch (error) {
        console.error('Error deleting feedback form:', error);
      }
    }
    resetModalState();
    onClose();
  };

  const handleModalClose = () => {
    handleDeleteFeedbackForm();
  };

  const handleSubmit = async () => {
    try {
      if (hasFile && !formState.s3Key) {
        message.error('Please wait for the file to finish uploading before saving');
        return;
      }
      
      setLoading(true);
      
      let documentType;
      const meetingType = mode === 'create' ? formState.meetingType : meeting?.type;
      switch (meetingType) {
        case 'APPRAISAL':
          documentType = 'APPRAISAL_FORM';
          break;
        case 'TPM':
          documentType = 'TPM_MEETING_FORM';
          break;
        case 'MONTHLY':
        default:
          documentType = 'MONTHLY_REVIEW';
          break;
      }
      
      if (mode === 'create') {
        if (!formState.menteeId || !formState.meetingType || !formState.meetingDateTime || !formState.meetingMethod || !formState.documentName || !formState.action) {
          message.error('Please fill in all required fields');
          return;
        }
        
        await onComplete({
          menteeId: formState.menteeId,
          meetingMethod: formState.meetingMethod,
          meetingType: formState.meetingType,
          meetingDateTime: formState.meetingDateTime.toDate(),
          documentName: formState.documentName,
          action: formState.action || undefined,
          s3Key: formState.s3Key || undefined,
          feedbackFormId: formState.feedbackFormId || undefined,
          documentType: documentType,
          tempMonthlyReviewId: formState.tempMonthlyReviewId || undefined,
        });
      } else if (mode === 'edit') {
        if (!formState.menteeId || !formState.meetingType || !formState.meetingDateTime || !formState.meetingMethod) {
          message.error('Please fill in all required fields');
          return;
        }

        await onComplete({
          meetingId: meeting?.id,
          menteeId: formState.menteeId,
          meetingMethod: formState.meetingMethod,
          meetingType: formState.meetingType,
          meetingDateTime: formState.meetingDateTime.toDate(),
          documentName: formState.documentName,
          action: formState.action || undefined,
          s3Key: formState.s3Key || undefined,
          feedbackFormId: formState.feedbackFormId || undefined,
          documentType: documentType,
        });
      } else {
        await onComplete({
          meetingType: formState.meetingType,
          documentName: formState.documentName,
          action: formState.action || undefined,
          s3Key: formState.s3Key || undefined,
          feedbackFormId: formState.feedbackFormId || undefined,
          documentType: documentType,
          tempMonthlyReviewId: formState.tempMonthlyReviewId || undefined,
        });
      }
      
      onSuccess?.();
      resetModalState();
      onClose();
    } catch (error) {
      console.error('Error completing meeting:', error);
    } finally {
      setLoading(false);
    }
  };

  const isCreateMode = mode === 'create';
  const isEditMode = mode === 'edit';

  const meetingTypeOptionsFiltered = isTrainingPrincipal ? meetingTypeOptions : meetingTypeOptions.filter(option => option.value !== MeetingType.APPRAISAL);
  return (
    <Modal 
      open={open} 
      onCancel={handleModalClose}
      footer={null}
      width={700}
      destroyOnClose
    >
      <Card className="w-full">
        <div className="mb-6">
          <Text className="text-xl font-medium">
            {isCreateMode ? 'Add Meeting' : isEditMode ? 'Edit Meeting' : (monthlyReviewId ? 'Edit' : 'Add')} Document
          </Text>
          <Text className="block mt-2 text-gray-500">
            {isCreateMode 
              ? 'Create a new meeting with document'
              : isEditMode
              ? 'Edit meeting details and documents'
              : 'Please insert Trainee Name followed by month and year of work covered. E.g. John Doe - July 2024'
            }
          </Text>
        </div>

        <Form layout="vertical">
          {(isCreateMode || isEditMode) && (
            <>
              <Form.Item label="Mentee" required>
                <Select
                  value={formState.menteeId}
                  onChange={(value) => setFormState(prev => ({ ...prev, menteeId: value }))}
                  placeholder="Select mentee"
                >
                  {mentees.map((mentee) => (
                    <Option key={mentee.id} value={mentee.id}>
                      {mentee.name} ({mentee.email})
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item label="In-Person / Virtual" required>
                <Select
                  value={formState.meetingMethod}
                  onChange={(value) => setFormState(prev => ({ ...prev, meetingMethod: value }))}
                  placeholder="Select meeting format"
                >
                  {meetingMethodOptions.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.text}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item label={isTrainingPrincipal ? 'Monthly / Appraisal' : 'Monthly'} required>
                <Select
                  value={formState.meetingType}
                  onChange={(value) => setFormState(prev => ({ ...prev, meetingType: value }))}
                  placeholder="Select meeting format"
                >
                  {meetingTypeOptionsFiltered.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.text}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item label="Meeting Date & Time" required>
                <DatePicker
                  showTime={{ format: 'HH:mm', minuteStep: 5 }}
                  format="DD/MM/YYYY HH:mm"
                  value={formState.meetingDateTime}
                  onChange={(date) => setFormState(prev => ({ ...prev, meetingDateTime: date }))}
                  placeholder="Select date and time"
                  className="w-full"
                />
              </Form.Item>
            </>
          )}

          <Form.Item label="Enter Document Name" required>
            <Input
              value={formState.documentName}
              onChange={(e) => setFormState(prev => ({ ...prev, documentName: e.target.value }))}
              placeholder="Enter document name"
            />
          </Form.Item>

          <Form.Item label="Actions" required>
            <Select
              value={formState.action}
              onChange={(value) => setFormState(prev => ({ ...prev, action: value }))}
              placeholder="Select action"
            >
              {actionOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.text}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Space className="w-full" direction="vertical" size="middle">
            <div>
              <div className="flex items-center space-x-4 mb-4">
                <Upload
                  customRequest={handleUpload}
                  accept="application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,image/png,image/jpeg"
                  showUploadList={false}
                >
                  <Button icon={<UploadOutlined />} loading={uploadLoading}>
                    {hasFile ? 'Change Document' : 'Upload Document'}
                  </Button>
                </Upload>

                <Button 
                  icon={<DownloadOutlined />} 
                  onClick={handleDownloadTemplate}
                  loading={templateLoading}
                  type="default"
                  className="ml-2"
                >
                  Download Template
                </Button>

                {((isCreateMode && formState.meetingType === MeetingType.MONTHLY && formState.meetingMethod === MeetingFormat.IN_PERSON) || 
                  (isEditMode && formState.meetingMethod === MeetingFormat.IN_PERSON) ||
                  (!isCreateMode && !isEditMode && meeting?.availability?.meetingMethod === MeetingFormat.IN_PERSON)) && (
                  <>
                    <Text className="ml-2 break-normal!">
                      Or
                    </Text>
                    {formState.feedbackFormId ? (
                      <Link 
                        href={`/feedbackForm/${formState.feedbackFormId}/edit`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="ant-btn ant-btn-link"
                      >
                        Edit Feedback Form
                      </Link>
                    ) : (
                      <Button 
                        onClick={handleCreateFeedbackForm}
                        loading={feedbackFormLoading}
                      >
                        Complete Feedback Form
                      </Button>
                    )}
                  </>
                )}
              </div>
              
              {hasFile && (
                <div className="flex items-center">
                  <a onClick={() => formState.s3Key && viewDocument(formState.s3Key)}>
                    {fileName}
                  </a>
                  <Button 
                    type="text" 
                    danger 
                    icon={<DeleteOutlined />} 
                    onClick={handleDeleteFile}
                    className="ml-2"
                  />
                </div>
              )}
            </div>
          </Space>

          <Divider />

          <div className="flex justify-end gap-3">
            <Button onClick={isEditMode ? () => { resetModalState(); onClose(); } : handleDeleteFeedbackForm}>
              Close
            </Button>
            <Button
              type="primary"
              onClick={handleSubmit}
              loading={loading}
              disabled={uploadLoading || (hasFile && !formState.s3Key)}
            >
              {isCreateMode ? 'Add Meeting' : 'Save'}
            </Button>
          </div>
        </Form>
      </Card>
    </Modal>
  );
} 