import { useState } from 'react';
import { Form, Input, DatePicker, Switch, Button, Typography, Space, Card, Divider } from 'antd';
import type { UploadFile } from 'antd/es/upload/interface';
import dayjs from 'dayjs';
import UploadPicker from '../common/UploadPicker';

const { Text, Title } = Typography;
const { TextArea } = Input;

interface FeedbackFormProps {
  onSubmit: (values: any) => void;
  initialValues?: any;
  viewOnly?: boolean;
}

export default function FeedbackForm({ onSubmit, initialValues, viewOnly = false }: FeedbackFormProps) {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [signature, setSignature] = useState<UploadFile | null>(null);

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      await onSubmit({
        ...values,
        signature: signature,
      });
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSignatureUpload = async (file: UploadFile) => {
    setSignature(file);
  };

  const renderQuestion = (
    question: string,
    hasSwitch: boolean,
    fieldName: string,
    commentFieldName: string,
    note?: string
  ) => (
    <Space direction="vertical" className="w-full">
      <Text strong>{question}</Text>
      {note && <Text type="secondary">{note}</Text>}
      {hasSwitch && (
        <Form.Item name={fieldName} valuePropName="checked">
          <Switch checkedChildren="Yes" unCheckedChildren="No" />
        </Form.Item>
      )}
      <Form.Item name={commentFieldName}>
        <TextArea
          rows={4}
          placeholder="Comments"
          disabled={viewOnly}
        />
      </Form.Item>
    </Space>
  );

  return (
    <Card>
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          ...initialValues,
          meetingDate: initialValues?.meetingDate ? dayjs(initialValues.meetingDate) : undefined,
        }}
        onFinish={handleSubmit}
        disabled={viewOnly}
      >
        <Space direction="vertical" size="large" className="w-full">
          <Title level={4}>Monthly Mentor Meeting</Title>

          <Form.Item
            name="coveringMonth"
            label="Covering work from the month of"
            rules={[{ required: true, message: 'Please enter the covering month' }]}
          >
            <Input placeholder="e.g. July 2024" />
          </Form.Item>

          <Form.Item
            name="menteeName"
            label="Mentee Name"
            rules={[{ required: true, message: 'Please enter the mentee name' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="meetingDate"
            label="Meeting Date"
            rules={[{ required: true, message: 'Please select the meeting date' }]}
          >
            <DatePicker className="w-full" format="DD/MM/YYYY" />
          </Form.Item>

          <Form.Item name="pointsToDiscuss" label="Points to discuss with mentee">
            <TextArea rows={4} placeholder="Points to discuss" />
          </Form.Item>

          <Divider>Work Quality Assessment</Divider>
          {renderQuestion(
            '1. Have you received good variety/quality work? Are there specific things you would like to gain exposure to?',
            true,
            'wasGoodWork',
            'goodWorkComments'
          )}

          {renderQuestion(
            '2. Do you feel that the level of responsibility you are receiving has increased?',
            true,
            'hasFeltResponsible',
            'responsibilityComments'
          )}

          {renderQuestion(
            '3. Do you have concerns/comments about your work you completed last month?',
            false,
            '',
            'workConcerns'
          )}

          <Divider>Supervisor Communication</Divider>
          {renderQuestion(
            '4. Are you working well with your supervisor? Do you have open and clear lines of communication?',
            true,
            'isWorkingWellSupervisor',
            'workingWellSupervisorComments'
          )}

          {renderQuestion(
            '5. Have you had an informal monthly feedback session with your supervisor?',
            true,
            'hadInformalMonthlyFeedback',
            'informalMonthlyFeedbackComments'
          )}

          <Divider>Skill Standards</Divider>
          {renderQuestion(
            '6. What are the skill standards that you still have not met in this seat?',
            false,
            '',
            'skillStandardsComments',
            'N.B: If there is a gap in the Practice Skills Standard which has not been addressed following mentee attempts, please add a note at the end of the checklist.'
          )}

          {renderQuestion(
            '7. Do you have any concerns/anything you wish to discuss?',
            false,
            '',
            'anyConcerns'
          )}

          {renderQuestion(
            '8. What further training do you feel you need at this time, if any?',
            false,
            '',
            'furtherTraining'
          )}

          <Divider>End of Seat Assessment</Divider>
          {renderQuestion(
            '9. Have you set up an appraisal meeting?',
            false,
            '',
            'appraisalMeeting'
          )}

          {renderQuestion(
            '10. Is this a firm/practice area you would consider qualifying into?',
            false,
            '',
            'qualifyingArea'
          )}

          <Form.Item name="mentorNotes" label="Mentor Notes">
            <TextArea
              rows={6}
              placeholder="Please add any other points raised or discussed with the mentee which you feel should be documented"
            />
          </Form.Item>

          <Card title="Action Box" className="w-full">
            <Text>Please add comments to any boxes that you would like HR to follow up:</Text>
            
            <Form.Item name="furtherAction" label="Any further action?">
              <TextArea rows={3} />
            </Form.Item>

            <Form.Item name="pointsToNote" label="Points to note">
              <TextArea rows={5} />
            </Form.Item>
          </Card>

          <Card title="Sign Off" className="w-full">
            <Space direction="vertical" className="w-full">
              <Text type="secondary">Signed off by Mentor/Training Principal</Text>
              
              {!viewOnly && (
                <Form.Item>
                  <UploadPicker
                    title={initialValues?.signatureUrl ? 'Change Signature' : 'Upload Signature'}
                    acceptType="image/png,image/jpeg,image/jpg"
                    onSelected={handleSignatureUpload}
                  />
                </Form.Item>
              )}

              {initialValues?.signatureUrl && (
                <img
                  src={initialValues.signatureUrl}
                  alt="Signature"
                  style={{ maxWidth: '300px' }}
                />
              )}

              <Form.Item
                name="signatureName"
                label="Print name"
                rules={[{ required: true, message: 'Please enter the signing name' }]}
              >
                <Input />
              </Form.Item>
            </Space>
          </Card>

          {!viewOnly && (
            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                className="w-full"
              >
                Submit Feedback
              </Button>
            </Form.Item>
          )}
        </Space>
      </Form>
    </Card>
  );
} 