import { MentorAvailability } from "@/types";
import MenteeAvailabilityCalendar from "./MenteeAvailabilityCalendar";

interface AvailabilityCalendarProps {
  availabilities: MentorAvailability[];
  onUpdateDateSlots: (
    date: Date,
    slots: {
      startTime: Date;
      endTime: Date;
      meetingMethod: "IN_PERSON" | "VIRTUAL";
      duration?: number;
      bufferTime?: number;
      meetingLocation?: string;
      meetingMessage?: string;
    }[]
  ) => Promise<void>;
  onDelete: (id: string) => Promise<void>;
}

/**
 * AvailabilityCalendar component - Forwards to MenteeAvailabilityCalendar
 * This component is kept for backward compatibility
 */
export default function AvailabilityCalendar(props: AvailabilityCalendarProps) {
  return <MenteeAvailabilityCalendar {...props} />;
}
