import { useState, useEffect } from 'react';
import { Table, Button, Space, Tag, Card, Popconfirm, message as messageHook, Typography, Input } from 'antd';
import { DeleteOutlined, SearchOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { Placement } from '@/types';
import moment from 'moment';

interface AdminPlacementsTableProps {
  data: Placement[];
  onDelete?: (id: string) => Promise<void>;
}

const AdminPlacementsTable: React.FC<AdminPlacementsTableProps> = ({ data, onDelete }) => {
  const [searchText, setSearchText] = useState('');
  const router = useRouter();
  const [messageApi, contextHolder] = messageHook.useMessage();

  const filteredData = data.filter(placement => 
    (placement.name || '').toLowerCase().includes(searchText.toLowerCase()) ||
    (placement.client?.name || '').toLowerCase().includes(searchText.toLowerCase()) ||
    (placement.user?.name || '').toLowerCase().includes(searchText.toLowerCase())
  );

  const handleDelete = async (id: string) => {
    try {
      if (onDelete) {
        await onDelete(id);
        messageApi.success('Placement deleted successfully');
      }
    } catch (error) {
      messageApi.error('Failed to delete placement');
    }
  };

  const columns = [
    {
      title: 'Position',
      dataIndex: 'position',
      key: 'position',
      render: (text: string) => <Typography.Text type="secondary">{text}</Typography.Text>
    },
    {
      title: 'Client',
      dataIndex: ['client', 'name'],
      key: 'client',
      render: (text: string) => <Typography.Text>{text}</Typography.Text>
    },
    {
      title: 'User',
      dataIndex: ['user', 'name'],
      key: 'user',
      render: (text: string) => <Typography.Text>{text}</Typography.Text>
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>{status}</Tag>
      )
    },
    {
      title: 'Start Date',
      dataIndex: 'startDate',
      key: 'startDate',
      render: (date: Date) => moment(date).format('DD/MM/YYYY')
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: Placement) => (
        <Space>
          <Button type="link" onClick={() => router.push(`/admin/placements/${record.id}`)}>
            View
          </Button>
          {onDelete && (
            <Popconfirm
              title="Are you sure you want to delete this placement?"
              onConfirm={() => handleDelete(record.id)}
              okText="Yes"
              cancelText="No"
            >
              <Button type="link" danger icon={<DeleteOutlined />}>
                Delete
              </Button>
            </Popconfirm>
          )}
        </Space>
      )
    }
  ];

  return (
    <Card>
      {contextHolder}
      <div style={{ marginBottom: 16 }}>
        <Input
          placeholder="Search placements..."
          prefix={<SearchOutlined />}
          onChange={e => setSearchText(e.target.value)}
          style={{ width: 200 }}
        />
      </div>
      <Table
        dataSource={filteredData}
        columns={columns}
        rowKey="id"
        pagination={{ pageSize: 10 }}
        scroll={{ x: 'max-content' }}
      />
    </Card>
  );
};

export default AdminPlacementsTable; 