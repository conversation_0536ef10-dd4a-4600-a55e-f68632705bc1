'use client';

import { Modal, Input, Form, Button } from 'antd';
import { LockOutlined } from '@ant-design/icons';

interface ChangePasswordModalProps {
  open: boolean;
  onCancel: () => void;
  onConfirm: (password: string) => void;
  title?: string;
  confirmLoading?: boolean;
}

export default function ChangePasswordModal({
  open,
  onCancel,
  onConfirm,
  title = 'Change Password',
  confirmLoading = false,
}: ChangePasswordModalProps) {
  const [form] = Form.useForm();
  
  const handleSubmit = () => {
    form.validateFields()
      .then(values => {
        onConfirm(values.password);
        form.resetFields();
      });
  };
  
  return (
    <Modal
      title={title}
      open={open}
      onCancel={() => {
        form.resetFields();
        onCancel();
      }}
      footer={null}
      maskClosable={false}
      closable={!confirmLoading}
      keyboard={!confirmLoading}
    >
      <Form 
        form={form}
        onFinish={handleSubmit}
        layout="vertical"
      >
        <Form.Item
          name="password"
          label="New Password"
          rules={[
            { required: true, message: 'Please enter a new password' },
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="Enter new password"
            autoComplete="new-password"
            disabled={confirmLoading}
          />
        </Form.Item>
        
        <Form.Item
          name="confirmPassword"
          label="Confirm Password"
          dependencies={['password']}
          rules={[
            { required: true, message: 'Please confirm the password' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('The two passwords do not match'));
              },
            }),
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="Confirm new password"
            autoComplete="new-password"
            disabled={confirmLoading}
          />
        </Form.Item>
        
        <div className="flex justify-end gap-2">
          <Button onClick={onCancel} disabled={confirmLoading}>
            Cancel
          </Button>
          <Button type="primary" htmlType="submit" loading={confirmLoading}>
            Change Password
          </Button>
        </div>
      </Form>
    </Modal>
  );
} 