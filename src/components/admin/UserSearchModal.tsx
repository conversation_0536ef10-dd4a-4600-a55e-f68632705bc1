'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { Modal, Input, List, Avatar, Spin, App } from 'antd';
import type { InputRef } from 'antd';
import { SearchOutlined, UserOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { signIn, useSession } from 'next-auth/react';
import debounce from '@/utils/debounce';
import { UserRole } from '@/types';

type User = {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  image?: string;
};

interface UserSearchModalProps {
  open: boolean;
  onClose: () => void;
}

export default function UserSearchModal({ open, onClose }: UserSearchModalProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [impersonating, setImpersonating] = useState(false);
  const inputRef = useRef<InputRef>(null);
  const router = useRouter();
  const { message } = App.useApp();
  const { data: session } = useSession();

  const searchUsers = useCallback(
    debounce(async (query: string) => {
      if (!query || query.length < 2) {
        setUsers([]);
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        const response = await fetch(`/api/admin/users/search?query=${encodeURIComponent(query)}`);
        if (response.ok) {
          const data = await response.json();
          setUsers(data);
        }
      } catch (error) {
        console.error('Error searching users:', error);
      } finally {
        setLoading(false);
      }
    }, 300),
    []
  );

  useEffect(() => {
    if (open) {
      searchUsers(searchTerm);
      // Focus the input field when modal opens
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [searchTerm, searchUsers, open]);

  const handleImpersonate = async (user: User) => {
    setImpersonating(true);
    
    try {
      message.loading('Starting impersonation...', 1);

      const response = await fetch('/api/admin/impersonate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: user.id }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to start impersonation');
      }

      const data = await response.json();

      if (typeof window !== 'undefined') {
        window.sessionStorage.setItem('adminImpersonation', JSON.stringify({
          adminId: data.adminId,
          adminEmail: session?.user?.email,
          impersonationId: data.impersonationId
        }));
      }
      
      await fetch('/api/admin/activity-log', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'START_IMPERSONATION',
          targetUserId: user.id,
          details: {
            startedAt: new Date().toISOString(),
            impersonationId: data.impersonationId,
            targetUserRole: user.role
          }
        })
      }).catch(err => console.error('Failed to log activity:', err));

      const result = await signIn('credentials', {
        impersonationData: JSON.stringify(data.user),
        redirect: false,
      });

      if (result?.error) {
        throw new Error(result.error);
      }

      onClose();
      router.push('/');
      router.refresh();
      message.success(`Now viewing as ${user.name}`);
    } catch (error) {
      console.error('Impersonation error:', error);
      message.error(error instanceof Error ? error.message : 'An error occurred during impersonation');
    } finally {
      setImpersonating(false);
    }
  };

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return '#ff4d4f';
      case UserRole.SUPERVISOR:
        return '#1890ff';
      case UserRole.MENTOR:
        return '#52c41a';
      case UserRole.TRAINEE:
        return '#faad14';
      default:
        return '#d9d9d9';
    }
  };

  const renderUserItem = (user: User) => (
    <List.Item
      key={user.id}
      onClick={() => handleImpersonate(user)}
      className="cursor-pointer hover:bg-gray-50 transition-colors"
    >
      <List.Item.Meta
        avatar={
          <Avatar
            src={user.image || undefined}
            icon={<UserOutlined />}
            style={{ backgroundColor: getRoleColor(user.role) }}
          />
        }
        title={<span>{user.name}</span>}
        description={
          <div>
            <div>{user.email}</div>
            <div style={{ color: getRoleColor(user.role), textTransform: 'capitalize' }}>
              {user.role.toLowerCase()}
            </div>
          </div>
        }
      />
    </List.Item>
  );

  return (
    <Modal
      title="Search Users"
      open={open}
      onCancel={onClose}
      footer={null}
      width={600}
    >
      <div className="mb-4">
        <Input
          placeholder="Search by name or email..."
          prefix={<SearchOutlined />}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          size="large"
          autoFocus
          ref={inputRef}
          disabled={impersonating}
        />
      </div>

      {loading || impersonating ? (
        <div className="flex justify-center py-8">
          <Spin size="large" />
        </div>
      ) : users.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          {searchTerm.length < 2
            ? 'Type at least 2 characters to search'
            : 'No users found matching your search'}
        </div>
      ) : (
        <List
          dataSource={users}
          renderItem={renderUserItem}
          className="max-h-[400px] overflow-y-auto"
        />
      )}
    </Modal>
  );
}
