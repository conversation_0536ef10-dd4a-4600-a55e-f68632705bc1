'use client';

import { Modal, Input, Form, Button, App } from 'antd';
import { LockOutlined, UserOutlined } from '@ant-design/icons';

interface PasswordModalProps {
  open: boolean;
  onCancel: () => void;
  onConfirm: (password: string) => void;
  title: string;
  confirmLoading?: boolean;
  email?: string;
}

export default function PasswordModal({
  open,
  onCancel,
  onConfirm,
  title,
  confirmLoading = false,
  email
}: PasswordModalProps) {
  const [form] = Form.useForm();
  const { message } = App.useApp();
  const handleSubmit = () => {
    form.validateFields()
      .then(values => {
        onConfirm(values.password);
      })
      .catch(() => {
        message.error('Please enter your password');
      });
  };
  
  return (
    <Modal
      title={title}
      open={open}
      onCancel={onCancel}
      footer={null}
      maskClosable={false}
      closable={!confirmLoading}
      keyboard={!confirmLoading}
    >
      {email && (
        <div className="mb-4">
          <p className="text-gray-500">
            <UserOutlined className="mr-2" />
            {email}
          </p>
        </div>
      )}
      
      <Form 
        form={form}
        onFinish={handleSubmit}
        layout="vertical"
      >
        <Form.Item
          name="password"
          rules={[{ required: true, message: 'Please enter your password' }]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="Enter your password"
            autoComplete="current-password"
            disabled={confirmLoading}
          />
        </Form.Item>
        
        <div className="flex justify-end gap-2">
          <Button onClick={onCancel} disabled={confirmLoading}>
            Cancel
          </Button>
          <Button type="primary" htmlType="submit" loading={confirmLoading}>
            Confirm
          </Button>
        </div>
      </Form>
    </Modal>
  );
}
