'use client';

import { useState, useEffect, useRef } from 'react';
import {
  Form,
  Input,
  Button,
  Switch,
  Card,
  Typography,
  Spin,
  App
} from 'antd';
import {
  SaveOutlined,
  UploadOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { CompetencyPlus, ExternalLink } from '@/types';
import ImageUpload from '@/components/common/ImageUpload';
import QuillEditor from '@/components/common/QuillEditor';
import ExternalLinksManager from './ExternalLinksManager';
import { S3_FOLDERS } from '@/lib/s3';

const { Title } = Typography;
const { TextArea } = Input;

interface CompetencyPlusFormProps {
  initialData?: CompetencyPlus;
  isEdit?: boolean;
}

export default function CompetencyPlusForm({ initialData, isEdit = false }: CompetencyPlusFormProps) {
  const [form] = Form.useForm();
  const { message } = App.useApp();
  const [loading, setLoading] = useState(false);
  const [content, setContent] = useState(initialData?.content || '');
  const [externalLinks, setExternalLinks] = useState<ExternalLink[]>(initialData?.externalLinks || []);
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [videoFile, setVideoFile] = useState<File | null>(null);
  const [audioUrl, setAudioUrl] = useState(initialData?.audioUrl || '');
  const [videoUrl, setVideoUrl] = useState(initialData?.videoUrl || '');
  const [uploading, setUploading] = useState(false);
  const router = useRouter();
  const audioInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (initialData) {
      form.setFieldsValue({
        title: initialData.title,
        shortDescription: initialData.shortDescription,
        bannerImageUrl: initialData.bannerImageUrl,
        published: initialData.published,
      });
      setContent(initialData.content);
      setExternalLinks(initialData.externalLinks || []);
    }
  }, [initialData, form]);



  const handleAudioFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      
      if (!file.type.startsWith('audio/')) {
        message.error('Please select an audio file');
        return;
      }

      if (file.size > 50 * 1024 * 1024) { // 50MB limit
        message.error('Audio file must be smaller than 50MB');
        return;
      }

      setAudioFile(file);
    }
  };

  const handleVideoFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      
      if (!file.type.startsWith('video/')) {
        message.error('Please select a video file');
        return;
      }

      if (file.size > 100 * 1024 * 1024) { // 100MB limit
        message.error('Video file must be smaller than 100MB');
        return;
      }

      setVideoFile(file);
    }
  };

  const uploadMediaFile = async (file: File, type: 'audio' | 'video'): Promise<string | null> => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('entityType', 'competency-plus');
      formData.append('folder', S3_FOLDERS.COMPETENCY_PLUS);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) throw new Error('Failed to upload file');

      const data = await response.json();
      return data.file.s3Key;
    } catch (error) {
      console.error(`Error uploading ${type} file:`, error);
      message.error(`Failed to upload ${type} file`);
      return null;
    }
  };

  const handleSubmit = async (values: CompetencyPlus) => {
    try {
      setLoading(true);
      setUploading(true);

      let finalAudioUrl = audioUrl;
      let finalVideoUrl = videoUrl;

      // Upload audio file if selected
      if (audioFile) {
        const uploadedAudioUrl = await uploadMediaFile(audioFile, 'audio');
        if (uploadedAudioUrl) {
          finalAudioUrl = uploadedAudioUrl;
        }
      }

      // Upload video file if selected  
      if (videoFile) {
        const uploadedVideoUrl = await uploadMediaFile(videoFile, 'video');
        if (uploadedVideoUrl) {
          finalVideoUrl = uploadedVideoUrl;
        }
      }

      const data = {
        title: values.title,
        shortDescription: values.shortDescription,
        content: content,
        externalLinks: externalLinks,
        audioUrl: finalAudioUrl,
        videoUrl: finalVideoUrl,
        bannerImageUrl: values.bannerImageUrl,
        published: values.published,
        wasPublished: initialData?.published || false,
      };

      const url = isEdit ? `/api/competency-plus/${initialData?.id}` : '/api/competency-plus';
      const method = isEdit ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      if (!response.ok) throw new Error('Failed to save competency plus item');

      message.success(`Competency Plus ${isEdit ? 'updated' : 'created'} successfully`);
      router.push('/admin/competency-plus');
    } catch (error) {
      console.error('Error saving competency plus item:', error);
      message.error('Failed to save competency plus item');
    } finally {
      setLoading(false);
      setUploading(false);
    }
  };

  const removeAudioFile = () => {
    setAudioFile(null);
    setAudioUrl('');
    if (audioInputRef.current) {
      audioInputRef.current.value = '';
    }
  };

  const removeVideoFile = () => {
    setVideoFile(null);
    setVideoUrl('');
    if (videoInputRef.current) {
      videoInputRef.current.value = '';
    }
  };

  return (
    <div className="p-6 min-h-full overflow-y-auto">
      <div className="max-w-4xl mx-auto">
        <Title level={2}>
          {isEdit ? 'Edit Competency Plus' : 'Create Competency Plus'}
        </Title>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="space-y-6"
        >
          <Card title="Overview" className="mb-6">
            <Form.Item
              name="title"
              label="Title"
              rules={[{ required: true, message: 'Please enter a title' }]}
            >
              <Input size="large" placeholder="Enter title" />
            </Form.Item>

            <Form.Item
              name="shortDescription"
              label="Short Description"
              rules={[{ required: true, message: 'Please enter a short description' }]}
            >
              <TextArea
                rows={3}
                placeholder="Brief description of the competency plus content"
              />
            </Form.Item>

            <Form.Item
              name="published"
              label="Published"
              valuePropName="checked"
              tooltip="When published, this item will be visible to trainees"
            >
              <Switch />
            </Form.Item>
          </Card>

          <Card title="Banner Image" className="mb-6">
            <Form.Item name="bannerImageUrl">
              <ImageUpload
                folder={S3_FOLDERS.COMPETENCY_PLUS}
                aspectRatio={16/9}
                className="w-full max-w-md !mb-0"
              />
            </Form.Item>
          </Card>

          <Card title="Content" className="mb-6">
            <div className="mb-4">
              <label className="font-medium">Content *</label>
              <div className="mt-2 overflow-visible">
                <QuillEditor
                  value={content}
                  onChange={setContent}
                  style={{ minHeight: '300px', maxHeight: '600px' }}
                  placeholder="Enter your content here..."
                />
              </div>
            </div>
          </Card>

          <Card title="External Links and Information" className="mb-6">
            <ExternalLinksManager
              value={externalLinks}
              onChange={setExternalLinks}
            />
          </Card>

          <Card title="Media Files" className="mb-6">
            <div className="mb-6">
              <label className="font-medium block mb-2">Audio Voiceover</label>
              <div className="flex items-center space-x-4">
                <input
                  type="file"
                  ref={audioInputRef}
                  onChange={handleAudioFileChange}
                  accept="audio/*"
                  className="hidden"
                />
                <Button
                  icon={<UploadOutlined />}
                  onClick={() => audioInputRef.current?.click()}
                  disabled={uploading}
                >
                  {audioFile ? 'Replace Audio' : audioUrl ? 'Change Audio' : 'Upload Audio'}
                </Button>
                
                {(audioFile || audioUrl) && (
                  <div className="flex items-center space-x-2">
                    <span className="text-sm">
                      {audioFile ? audioFile.name : 'Current audio file'}
                    </span>
                    <Button
                      icon={<DeleteOutlined />}
                      size="small"
                      danger
                      onClick={removeAudioFile}
                      disabled={uploading}
                    />
                  </div>
                )}
              </div>
            </div>

            <div className="mb-6">
              <label className="font-medium block mb-2">Video Source</label>
              <div className="flex items-center space-x-4">
                <input
                  type="file"
                  ref={videoInputRef}
                  onChange={handleVideoFileChange}
                  accept="video/*"
                  className="hidden"
                />
                <Button
                  icon={<UploadOutlined />}
                  onClick={() => videoInputRef.current?.click()}
                  disabled={uploading}
                >
                  {videoFile ? 'Replace Video' : videoUrl ? 'Change Video' : 'Upload Video'}
                </Button>
                
                {(videoFile || videoUrl) && (
                  <div className="flex items-center space-x-2">
                    <span className="text-sm">
                      {videoFile ? videoFile.name : 'Current video file'}
                    </span>
                    <Button
                      icon={<DeleteOutlined />}
                      size="small"
                      danger
                      onClick={removeVideoFile}
                      disabled={uploading}
                    />
                  </div>
                )}
              </div>
            </div>
          </Card>

          <div className="flex justify-end space-x-4 sticky bottom-6 bg-white p-4 z-10">
            <Button 
              onClick={() => router.push('/admin/competency-plus')}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              icon={<SaveOutlined />}
              loading={loading}
              disabled={uploading}
            >
              {loading ? 'Saving...' : (isEdit ? 'Update' : 'Create')}
            </Button>
          </div>
        </Form>
      </div>

      {uploading && (
        <div className="fixed top-4 right-4 z-50 pointer-events-none">
          <div className="bg-white p-4 rounded-lg shadow-lg border">
            <div className="flex items-center space-x-2">
              <Spin size="small" />
              <span className="text-sm">Uploading files...</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 