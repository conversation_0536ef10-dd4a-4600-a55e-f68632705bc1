'use client';

import { useState, useEffect } from 'react';
import { Card, Button, Input, Space, Typography, List, Popconfirm } from 'antd';
import { PlusOutlined, DeleteOutlined, LinkOutlined } from '@ant-design/icons';
import { ExternalLink } from '@/types';

const { Text } = Typography;
const { TextArea } = Input;

interface ExternalLinksManagerProps {
  value?: ExternalLink[];
  onChange?: (links: ExternalLink[]) => void;
}

export default function ExternalLinksManager({ value = [], onChange }: ExternalLinksManagerProps) {
  const [links, setLinks] = useState<ExternalLink[]>(value);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    url: '',
    description: ''
  });

  useEffect(() => {
    setLinks(value);
  }, [value]);

  const generateId = () => Math.random().toString(36).substr(2, 9);

  const handleAddLink = () => {
    if (!formData.title.trim() || !formData.url.trim()) return;

    const newLink: ExternalLink = {
      id: generateId(),
      title: formData.title.trim(),
      url: formData.url.trim(),
      description: formData.description.trim() || undefined
    };

    let updatedLinks;
    if (editingIndex !== null) {
      // Update existing link
      updatedLinks = [...links];
      updatedLinks[editingIndex] = newLink;
      setEditingIndex(null);
    } else {
      // Add new link
      updatedLinks = [...links, newLink];
    }

    setLinks(updatedLinks);
    onChange?.(updatedLinks);
    
    // Reset form
    setFormData({ title: '', url: '', description: '' });
  };

  const handleEditLink = (index: number) => {
    const link = links[index];
    setFormData({
      title: link.title,
      url: link.url,
      description: link.description || ''
    });
    setEditingIndex(index);
  };

  const handleDeleteLink = (index: number) => {
    const updatedLinks = links.filter((_, i) => i !== index);
    setLinks(updatedLinks);
    onChange?.(updatedLinks);
    
    // Reset form if we're editing the deleted link
    if (editingIndex === index) {
      setFormData({ title: '', url: '', description: '' });
      setEditingIndex(null);
    }
  };

  const handleCancelEdit = () => {
    setEditingIndex(null);
    setFormData({ title: '', url: '', description: '' });
  };

  const isValidUrl = (url: string) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  return (
    <div>
      {/* Add/Edit Link Form */}
      <Card 
        title={editingIndex !== null ? "Edit Link" : "Add New Link"} 
        size="small" 
        className="mb-4!"
      >
        <Space direction="vertical" className="w-full">
          <div>
            <Text strong>Link Title *</Text>
            <Input
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              placeholder="e.g., Official Documentation"
              className="mt-1"
            />
          </div>
          
          <div>
            <Text strong>URL *</Text>
            <Input
              value={formData.url}
              onChange={(e) => setFormData({ ...formData, url: e.target.value })}
              placeholder="https://example.com"
              className="mt-1"
              status={formData.url && !isValidUrl(formData.url) ? 'error' : ''}
            />
            {formData.url && !isValidUrl(formData.url) && (
              <Text type="danger" className="text-sm">Please enter a valid URL</Text>
            )}
          </div>
          
          <div>
            <Text strong>Description (Optional)</Text>
            <TextArea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Brief description of the link"
              rows={2}
              className="mt-1"
            />
          </div>
          
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddLink}
              disabled={!formData.title.trim() || !formData.url.trim() || (formData.url && !isValidUrl(formData.url)) || undefined}
            >
              {editingIndex !== null ? 'Update Link' : 'Add Link'}
            </Button>
            {editingIndex !== null && (
              <Button onClick={handleCancelEdit}>
                Cancel
              </Button>
            )}
          </Space>
        </Space>
      </Card>

      {/* Links List */}
      {links.length > 0 && (
        <Card title="External Links" size="small">
          <List
            dataSource={links}
            renderItem={(link, index) => (
              <List.Item
                actions={[
                  <Button
                    key="edit"
                    type="link"
                    onClick={() => handleEditLink(index)}
                  >
                    Edit
                  </Button>,
                  <Popconfirm
                    key="delete"
                    title="Are you sure you want to delete this link?"
                    onConfirm={() => handleDeleteLink(index)}
                    okText="Yes"
                    cancelText="No"
                  >
                    <Button type="link" danger icon={<DeleteOutlined />} />
                  </Popconfirm>
                ]}
              >
                <List.Item.Meta
                  avatar={<LinkOutlined />}
                  title={
                    <a href={link.url} target="_blank" rel="noopener noreferrer">
                      {link.title}
                    </a>
                  }
                  description={
                    <div>
                      <div className="text-blue-600 text-sm">{link.url}</div>
                      {link.description && (
                        <div className="text-gray-600 text-sm mt-1">{link.description}</div>
                      )}
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        </Card>
      )}
    </div>
  );
} 