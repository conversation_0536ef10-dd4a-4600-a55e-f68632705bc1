'use client';

import { useState, useEffect, useCallback } from 'react';
import { Layout, Menu, Button, Avatar, Dropdown, theme } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  LogoutOutlined,
  DashboardOutlined,
  TeamOutlined,
  FileOutlined,
  SendOutlined,
  Pie<PERSON>hartOutlined,
  BookOutlined,
  SafetyCertificateOutlined,
  CalendarOutlined,
  SwapOutlined,
} from '@ant-design/icons';
import { useRouter, usePathname } from 'next/navigation';
import { useAnalytics } from '@/hooks/useAnalytics';
import { signOut, useSession } from 'next-auth/react';
import Image from 'next/image';
import { User, UserRole } from '@/types';
import NotificationBell from '@/components/notifications/NotificationBell';
import { useAuthStore } from '@/store/authStore';
import UserSearchModal from '@/components/admin/UserSearchModal';
import ImpersonationBanner from '@/components/ImpersonationBanner';
import ProfileModal from '@/components/profile/ProfileModal';


type MenuItem = {
  key: string;
  icon?: React.ReactNode;
  label: string;
  onClick?: () => void;
  children?: MenuItem[];
};

const { Header, Sider, Content } = Layout;

interface MainLayoutProps {
  children: React.ReactNode;
}

export default function MainLayout({ children }: MainLayoutProps) {
  const [collapsed, setCollapsed] = useState(false);
  const [userSearchModalOpen, setUserSearchModalOpen] = useState(false);
  const [profileModalOpen, setProfileModalOpen] = useState(false);
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const router = useRouter();
  const pathname = usePathname();
  const { data: session } = useSession();
  const logout = useAuthStore((state) => state.logout);
  const { trackPageView, trackMenuClick } = useAnalytics();
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  const userMenuItems: MenuItem[] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Profile',
      onClick: () => setProfileModalOpen(true),
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Logout',
      onClick: async () => {
        // Clear auth store state
        logout();
        // Clear any other auth-related localStorage items
        const keysToKeep = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && key.startsWith('qualification_route_dismissed_')) {
            keysToKeep.push({ key, value: localStorage.getItem(key) });
          }
        }
        localStorage.clear();
        keysToKeep.forEach(item => {
          if (item.value) localStorage.setItem(item.key, item.value);
        });
        // Sign out from NextAuth
        await signOut({ 
          redirect: true,
          callbackUrl: '/auth/signin'
        });
      },
    },
  ];

  // Track page view on route change - only for trainee users
  useEffect(() => {
    if (pathname && session?.user?.role === UserRole.TRAINEE) {
      // Extract the page name from the pathname
      const pageName = pathname.split('/').filter(Boolean).join('/');
      trackPageView(pageName || 'home');
    }
  }, [pathname, trackPageView, session?.user?.role]);

  // Create a wrapped onClick handler to track menu clicks - only for trainee users
  const createMenuClickHandler = useCallback(
    (label: string, originalHandler: () => void) => {
      return () => {
        // Only track if user is a trainee
        if (session?.user?.role === UserRole.TRAINEE) {
          trackMenuClick(label);
        }
        originalHandler();
      };
    },
    [trackMenuClick, session?.user?.role]
  );

  // Define menu items based on user role
  const getMenuItems = (): MenuItem[] => {
    if (session?.user?.role === UserRole.ADMIN) {
      return [
        // Dashboard
        {
          key: 'dashboard',
          icon: <DashboardOutlined />,
          label: 'Dashboard',
          onClick: createMenuClickHandler('Dashboard', () => router.push('/admin/dashboard')),
        },

        // People Management
        {
          key: 'people',
          icon: <TeamOutlined />,
          label: 'People Management',
          children: [
            {
              key: 'clients',
              label: 'Clients',
              onClick: createMenuClickHandler('Clients', () => router.push('/admin/clients')),
            },
            {
              key: 'users',
              label: 'Users',
              onClick: createMenuClickHandler('Users', () => router.push('/admin/users')),
            },
          ],
        },

        // Tracking
        {
          key: 'tracking',
          icon: <FileOutlined />,
          label: 'Tracking',
          children: [
            {
              key: 'placements',
              label: 'Placements',
              onClick: createMenuClickHandler('Placements', () => router.push('/admin/placements')),
            },
            // {
            //   key: 'entries',
            //   label: 'Entries',
            //   onClick: createMenuClickHandler('Entries', () => router.push('/admin/entries')),
            // },
            {
              key: 'character',
              label: 'Character & Suitability',
              onClick: createMenuClickHandler('Character & Suitability', () => router.push('/admin/character-suitability')),
            },
            {
              key: 'skills',
              label: 'SQE & TC',
              onClick: createMenuClickHandler('SQE & TC', () => router.push('/admin/skills')),
            },
          ],
        },

        // Courses
        {
          key: 'courses-menu',
          icon: <BookOutlined />,
          label: 'Courses',
          children: [
            {
              key: 'course-categories',
              label: 'Course Categories',
              onClick: createMenuClickHandler('Course Categories', () => router.push('/admin/course-categories')),
            },
            {
              key: 'courses',
              label: 'Courses',
              onClick: createMenuClickHandler('Courses', () => router.push('/admin/courses')),
            },
          ],
        },
        {
          key: 'competency-plus',
          icon: <BookOutlined />,
          label: 'Competency Plus',
          onClick: createMenuClickHandler('Competency Plus', () => router.push('/admin/competency-plus')),
        },
        {
          key: 'meeting-templates',
          icon: <FileOutlined />,
          label: 'Meeting Templates',
          onClick: createMenuClickHandler('Meeting Templates', () => router.push('/admin/meeting-templates')),
        },
      ];
    } else if (session?.user?.role === UserRole.SUPERVISOR) {
      return [
        {
          key: 'supervisor-trainees',
          icon: <TeamOutlined />,
          label: 'My Trainees',
          onClick: createMenuClickHandler('My Trainees', () => router.push('/supervisor/trainees')),
        },
        {
          key: 'supervisor-submissions',
          icon: <SendOutlined />,
          label: 'Sign-Off Submissions',
          onClick: createMenuClickHandler('Sign-Off Submissions', () => router.push('/supervisor/submissions')),
        },
        // {
        //   key: 'supervisor-meetings-requests',
        //   icon: <TeamOutlined />,
        //   label: 'Meeting Requests',
        //   onClick: createMenuClickHandler('Meeting Requests', () => router.push('/supervisor/meetings/requests')),
        // },
        // {
        //   key: 'supervisor-meetings-completed',
        //   icon: <TeamOutlined />,
        //   label: 'Meetings',
        //   onClick: createMenuClickHandler('Completed Meetings', () => router.push('/supervisor/meetings/completed')),
        // },
      ];
    } else if (session?.user?.role === UserRole.MENTOR) {
      return [
        {
          key: 'mentor-mentees',
          icon: <TeamOutlined />,
          label: 'My Mentees',
          onClick: createMenuClickHandler('My Mentees', () => router.push('/mentor/mentees')),
        },
        {
          key: 'mentor-review-entries',
          icon: <FileOutlined />,
          label: 'Review Entries',
          onClick: createMenuClickHandler('Review Entries', () => router.push('/mentor/entries/review')),
        },
        {
          key: 'mentor-availability',
          icon: <CalendarOutlined />,
          label: 'My Availability',
          onClick: createMenuClickHandler('My Availability', () => router.push('/mentor/availability')),
        },
        {
          key: 'mentor-meeting-requests',
          icon: <TeamOutlined />,
          label: 'Meeting Requests',
          onClick: createMenuClickHandler('Meeting Requests', () => router.push('/mentor/meetings/requests')),
        },
        {
          key: 'mentor-completed-meetings',
          icon: <TeamOutlined />,
          label: 'Meetings',
          onClick: createMenuClickHandler('Meetings', () => router.push('/mentor/meetings/completed')),
        },
      ];
    } else if (session?.user?.role === UserRole.TRAINEE) {
      // Get base menu items that are always shown
      const baseMenuItems: MenuItem[] = [
        {
          key: 'trainee-dashboard',
          icon: <DashboardOutlined />,
          label: 'Trainee Dashboard',
          onClick: createMenuClickHandler('Trainee Dashboard', () => router.push('/trainee/dashboard')),
        },
        {
          key: 'trainee-qualifications',
          icon: <BookOutlined />,
          label: 'Qualifications',
          onClick: createMenuClickHandler('Qualifications', () => router.push('/trainee/qualifications')),
        },
        {
          key: 'training',
          icon: <PieChartOutlined />,
          label: 'Training Record',
          onClick: createMenuClickHandler('Training Record', () => router.push('/trainee/training')),
        },
        {
          key: 'character-suitability',
          icon: <SafetyCertificateOutlined />,
          label: 'Character & Suitability',
          onClick: createMenuClickHandler('Character & Suitability', () => router.push('/trainee/character-suitability')),
        },
        {
          key: 'courses',
          icon: <BookOutlined />,
          label: 'Courses',
          onClick: createMenuClickHandler('Courses', () => router.push('/trainee/courses')),
        },
        {
          key: 'competency-plus',
          icon: <BookOutlined />,
          label: 'Competency Plus',
          onClick: createMenuClickHandler('Competency Plus', () => router.push('/trainee/competency-plus')),
        },
        // Only show Resources menu for level 3 trainees
        ...(session?.user?.traineeLevel === 3 ? [{
          key: 'trainee-resources',
          icon: <BookOutlined />,
          label: 'Resources',
          children: [
            {
              key: 'trainee-meetings',
              icon: <TeamOutlined />,
              label: 'Mentoring',
              onClick: createMenuClickHandler('Mentoring', () => router.push('/trainee/meetings')),
            },
            {
              key: 'trainee-appraisals',
              icon: <CalendarOutlined />,
              label: 'Appraisals',
              onClick: createMenuClickHandler('Appraisals', () => router.push('/trainee/appraisals')),
            }
          ],
        }] : [])
      ];

      // if (session?.user?.qualificationRoute && session?.user?.qualificationRoute === 'SQE') {
      //   baseMenuItems.push(
      //     {
      //       key: 'trainee-resources',
      //       icon: <BookOutlined />,
      //       label: 'Resources',
      //       children: [
      //         {
      //           key: 'trainee-meetings',
      //           icon: <TeamOutlined />,
      //           label: 'Mentoring',
      //           onClick: createMenuClickHandler('Mentoring', () => router.push('/trainee/meetings')),
      //         },
      //         {
      //           key: 'trainee-appraisals',
      //           icon: <CalendarOutlined />,
      //           label: 'Appraisals',
      //           onClick: createMenuClickHandler('Appraisals', () => router.push('/trainee/appraisals')),
      //         }
      //       ],
      //     },
      //   );
      // }

      return baseMenuItems;
    } else {
      return [];
    }
  };

  // Get the active menu key based on the current path
  const getActiveMenuKey = () => {
    // First check for exact path matches
    const pathMap = {
      '/admin/clients': 'clients',
      '/admin/users': 'users', 
      '/admin/placements': 'placements',
      '/admin/skills': 'skills',
      // '/admin/entries': 'entries',
      '/admin/courses': 'courses',
      '/admin/course-categories': 'course-categories',
      '/admin/competency-plus': 'competency-plus',
      '/admin/meeting-templates': 'meeting-templates',
      '/admin/character-suitability': 'character',
      '/supervisor/trainees': 'supervisor-trainees',
      '/supervisor/submissions': 'supervisor-submissions',
      '/mentor/availability': 'mentor-availability',
      '/mentor/entries/review': 'mentor-review-entries',
      '/mentor/meetings/requests': 'mentor-meeting-requests',
      '/mentor/meetings/completed': 'mentor-completed-meetings',
      '/trainee/dashboard': 'trainee-dashboard',
      '/trainee/meetings': 'trainee-meetings',
      '/trainee/training': 'training',
      '/trainee/placements': 'trainee-placements',
      '/trainee/entries': 'trainee-entries',
      '/trainee/submissions': 'trainee-submissions',
      '/trainee/qualifications': 'trainee-qualifications',
      '/trainee/progress': 'trainee-progress',
      '/trainee/character-suitability': 'character-suitability',
      '/trainee/appraisals': 'trainee-appraisals',
      '/mentor/mentees': 'mentor-mentees',
      '/trainee/courses': 'courses',
      '/trainee/competency-plus': 'competency-plus',
      '/supervisor/meetings/completed': 'supervisor-meetings-completed',
      '/supervisor/meetings/requests': 'supervisor-meetings-requests',
    };

    return Object.entries(pathMap).find(([path]) => pathname.startsWith(path))?.[1] || '';
  };

  useEffect(() => {
    if (pathname.startsWith('/trainee/meetings') || pathname.startsWith('/trainee/appraisals')) {
      setOpenKeys(['trainee-resources']);
    } else if (pathname.startsWith('/admin/courses') || pathname.startsWith('/admin/course-categories')) {
      setOpenKeys(['courses-menu']);
    }
  }, [pathname]);

  const onOpenChange = (keys: string[]) => {
    setOpenKeys(keys);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        theme="light"
        width={260}
        style={{
          borderRight: '1px solid #f0f0f0',
          backgroundColor: '#ffffff',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
        }}
        className="!bg-white [&_.ant-layout-sider-children]:!bg-white max-w-[300px] w-full"
      >
        <div className="p-4 flex justify-center bg-white">
          <Image
            src="https://admin.accutraineepw.com/images/logos/logo--dark.svg"
            alt="Logo"
            width={collapsed ? 32 : 120}
            height={32}
          />
        </div>
        <Menu
          theme="light"
          mode="inline"
          selectedKeys={[getActiveMenuKey()]}
          openKeys={collapsed ? [] : openKeys}
          onOpenChange={onOpenChange}
          items={getMenuItems()}
          style={{
            borderRight: 'none',
            backgroundColor: '#ffffff'
          }}
          className="!bg-white"
        />
      </Sider>
      <Layout>
        {session?.user?.isImpersonated && <ImpersonationBanner />}
        <Header style={{ 
          padding: 0, 
          background: '#f37a21',
          color: '#000',
          height: 'auto',
          lineHeight: 'normal'
        }}>
          <div className="flex flex-col">
            <div className="flex justify-between items-center px-6 py-3">
              <Button
                type="text"
                icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                onClick={() => setCollapsed(!collapsed)}
                style={{ color: '#fff' }}
              />
              {
                (session?.user?.role !== UserRole.ADMIN && session?.user?.role !== UserRole.SUPERVISOR) && (
                  <div className="text-center py-2 px-4 text-sm text-white">
                    Are you looking for great paralegal, training contract or QWE opportunities?<br />
                    If so, <a href="https://careers.accutrainee.com/" className="text-black! underline!">apply now</a> for a variety of fantastic roles as well as the ability to unlock additional features on Pathways!
                  </div>
                )
              }
              <div className="flex items-center gap-4">
              {session?.user?.role === UserRole.ADMIN && !session.user.isImpersonated && (
                <Button 
                  icon={<SwapOutlined />} 
                  onClick={() => setUserSearchModalOpen(true)}
                  type="primary"
                  ghost
                  style={{ color: '#fff', borderColor: '#fff' }}
                >
                  Impersonate User
                </Button>
              )}
              {/* {
                session?.user?.role !== UserRole.ADMIN && (
                  <NotificationBell />
                )
              } */}
              <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
                <div className="flex items-center cursor-pointer gap-2">
                  {
                    session?.user?.image ? (
                      <Avatar src={session?.user?.image} />
                    ) : (
                      <Avatar icon={<UserOutlined style={{ color: 'white' }} />} />
                    )
                  }
                  {/* <Avatar icon={<UserOutlined style={{ color: 'white' }} />} /> */}
                  <span style={{ color: 'white' }}>{session?.user?.name}</span>
                </div>
              </Dropdown>
            </div>
          </div>
          </div>
        </Header>
        <Content
          style={{
            margin: '24px 16px',
            padding: 24,
            minHeight: 280,
            background: colorBgContainer,
            borderRadius: borderRadiusLG,
          }}
        >
          {children}
        </Content>
      </Layout>
      {session?.user && (
        <ProfileModal
          open={profileModalOpen}
          onClose={() => setProfileModalOpen(false)}
          user={session.user as User}
        />
      )}
      {session?.user?.role === UserRole.ADMIN && !session?.user?.isImpersonated && (
        <UserSearchModal 
          open={userSearchModalOpen} 
          onClose={() => setUserSearchModalOpen(false)} 
        />
      )}
    </Layout>
  );
}