'use client';

import { useState, useEffect } from 'react';
import { Table, Button, Space, Tag, Card, message as messageHook, Typography, Spin, Input, Select, Form, Row, Col, DatePicker } from 'antd';
import { SearchOutlined, FilterOutlined } from '@ant-design/icons';
import { useSession } from 'next-auth/react';
import { Entry, EntryStatus, UserRole } from '@/types';
import { viewDocument } from '@/lib/utils';

interface AdminEntriesTableProps {
  userId: string;
  isAdmin?: boolean;
  title?: string;
  isShowDraft?: boolean;
}

export default function AdminEntriesTable({
  userId,
  isAdmin = false,
  title = "Manage All Entries",
  isShowDraft = true
}: AdminEntriesTableProps) {
  const { data: session } = useSession();
  const [messageApi, contextHolder] = messageHook.useMessage();
  const [entries, setEntries] = useState<Entry[]>([]);
  const [filteredEntries, setFilteredEntries] = useState<Entry[]>([]);
  const [loading, setLoading] = useState(true);
  const [filterForm] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [dateRange, setDateRange] = useState<[null | Date, null | Date]>([null, null]);

  useEffect(() => {
    fetchEntries();
  }, [userId, isAdmin]);
  
  // Filter entries when search text, status, or date range changes
  useEffect(() => {
    let filtered = entries;
    
    // Enhanced search - now includes content from "View" tab
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      filtered = filtered.filter(entry => {
        // Original search fields
        const traineeMatch = entry.placement?.user?.name?.toLowerCase().includes(searchLower);
        const placementMatch = entry.placement?.name?.toLowerCase().includes(searchLower);
        const clientMatch = entry.placement?.client?.name?.toLowerCase().includes(searchLower);
        const documentMatch = entry.documentName?.toLowerCase().includes(searchLower);
        
        // New: Search within View tab content
        const titleMatch = entry.title?.toLowerCase().includes(searchLower);
        const experienceMatch = entry.experience?.toLowerCase().includes(searchLower);
        const learntMatch = entry.learnt?.toLowerCase().includes(searchLower);
        const needMoreExpMatch = entry.needMoreExperience?.toLowerCase().includes(searchLower);
        const moreExpMatch = entry.moreExperience?.toLowerCase().includes(searchLower);
        const feedbackMatch = entry.feedback?.toLowerCase().includes(searchLower);
        const taskedByMatch = entry.taskedBy?.toLowerCase().includes(searchLower);

        // Search in skills
        const skillsMatch = entry.entrySubSkills && Array.isArray(entry.entrySubSkills) &&
          entry.entrySubSkills.some(skill => 
            skill.subSkill?.name?.toLowerCase().includes(searchLower) ||
            skill.subSkill?.practiceSkillGroup?.name?.toLowerCase().includes(searchLower) ||
            skill.subSkill?.practiceSkillGroup?.practiceSkill?.name?.toLowerCase().includes(searchLower)
          );
        
        return traineeMatch || placementMatch || clientMatch || documentMatch || 
               titleMatch || experienceMatch || learntMatch || needMoreExpMatch || 
               moreExpMatch || feedbackMatch || taskedByMatch || skillsMatch;
      });
    }
    
    // Filter by status
    if (selectedStatus) {
      filtered = filtered.filter(entry => entry.status?.toLowerCase() === selectedStatus.toLowerCase());
    }
    
    // Filter by date range
    if (dateRange[0] && dateRange[1]) {
      const startDate = dateRange[0];
      const endDate = dateRange[1];
      
      filtered = filtered.filter(entry => {
        const submittedDate = entry.submittedAt ? new Date(entry.submittedAt) : null;
        if (!submittedDate) return false;
        
        return submittedDate >= startDate && submittedDate <= endDate;
      });
    }
    
    setFilteredEntries(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  }, [searchText, selectedStatus, dateRange, entries]);

  const fetchEntries = async () => {
    setLoading(true);
    try {
      const url = isAdmin ? '/api/entries' : `/api/entries/user/${userId}`;
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch entries: ${response.statusText}`);
      }
      const data = await response.json();
      //remove all entries that have a status of draft
      const filteredData = isShowDraft ? data : data.filter((entry: Entry) => entry.status !== 'draft');
      setEntries(filteredData);
      setFilteredEntries(filteredData);
    } catch (error: unknown) {
      messageApi.error(error instanceof Error ? error.message : 'Failed to load entries');
      setEntries([]);
      setFilteredEntries([]);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/entries/${id}`, {
        method: 'DELETE',
      });
      if (response.ok) {
        messageApi.success('Entry deleted successfully');
        fetchEntries();
      } else {
        const errorData = await response.json();
        messageApi.error(errorData.error || 'Failed to delete entry');
      }
    } catch (error) {
      console.error('Error deleting entry:', error);
      messageApi.error('Failed to delete entry');
    }
  };

  const getStatusColor = (status: string) => {
    if (!status) return 'default';
    const lowerStatus = status.toLowerCase();
    if (lowerStatus.includes('approved')) return 'green';
    if (lowerStatus.includes('rejected')) return 'red';
    if (lowerStatus.includes('pending')) return 'blue';
    if (lowerStatus.includes('draft')) return 'grey';
    if (lowerStatus.includes('signed_off')) return 'purple';
    return 'default';
  };

  const columns = [
    {
      title: 'File',
      dataIndex: 'fileUrl',
      key: 'fileUrl',
      render: (_: unknown, record: Entry) =>
        record.documentName && record.documentKey && record.documentKey !== 'null' ? (
          <a className='max-w-[200px] overflow-hidden text-ellipsis' onClick={() => viewDocument(record.documentKey as string)}>{record.documentName}</a>
        ) : 'No File',
    },
    {
      title: 'Trainee',
      dataIndex: ['placement', 'user', 'name'],
      key: 'trainee',
      render: (name: string | null) => name || '',
    },
    {
      title: 'Placement',
      dataIndex: ['placement', 'position'],
      key: 'placement',
      render: (_: unknown, record: Entry) =>
        record.placement ? (
          <div className='max-w-[200px] overflow-hidden text-ellipsis'>
            <Typography.Text> {record.placement.client?.name || ''}</Typography.Text>
          </div>
        ) : '',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => <Tag color={getStatusColor(status)}>{status?.toUpperCase() || ''}</Tag>,
    },
    {
      title: 'Submitted At',
      dataIndex: 'submittedAt',
      key: 'submittedAt',
      render: (date: string) => date ? new Date(date).toLocaleDateString() : '-',
    },
    {
      title: 'Reviewed At',
      dataIndex: 'reviewedAt',
      key: 'reviewedAt',
      render: (date?: string) => date ? new Date(date).toLocaleDateString() : '-',
    },
    {
      title: 'Reviewed By',
      dataIndex: ['reviewer', 'name'],
      key: 'reviewer',
      render: (name?: string | null) => name || '-',
    },
    ...(session?.user?.role === UserRole.ADMIN ? [{
      title: 'Actions',
      key: 'actions',
      render: (_: unknown, record: Entry) => (
        <Space>
          <Button
            type="link"
            onClick={() => window.open(`/entries/${record.id}`, '_blank')}
          >
            Preview
          </Button>
          {/* <Popconfirm
            title="Are you sure you want to delete this entry?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Button danger icon={<DeleteOutlined />} />
          </Popconfirm> */}
        </Space>
      ),
    }] : []),
  ];

  // Handle filter reset
  const handleFilterReset = () => {
    filterForm.resetFields();
    setSearchText('');
    setSelectedStatus(null);
    setDateRange([null, null]);
  };

  return (
    <div className="p-6" style={{ maxWidth: '100vw', overflowX: 'auto' }}>
      {contextHolder}
      <Card title={title}>
        <div className="mb-4">
          <Form form={filterForm} layout="horizontal" className="w-full">
            <Row gutter={[16, 12]} className="w-full">
              <Col xs={24} md={12}>
                <Form.Item label="Search" name="search" className="mb-0">
                  <Input
                    placeholder="Search by trainee, placement, title, content, skills, etc..."
                    prefix={<SearchOutlined />}
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    allowClear
                    className="w-full"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item label="Status" name="status" className="mb-0">
                  <Select 
                    placeholder="Filter by status"
                    allowClear
                    onChange={(value) => setSelectedStatus(value)}
                    value={selectedStatus}
                    className="w-full"
                  >
                    {
                      isShowDraft && <Select.Option value={EntryStatus.DRAFT}>DRAFT</Select.Option>
                    }
                    <Select.Option value={EntryStatus.SUBMITTED}>SUBMITTED</Select.Option>
                    <Select.Option value={EntryStatus.REJECTED}>REJECTED</Select.Option>
                    <Select.Option value={EntryStatus.SIGNEDOFF}>SIGNED OFF</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={16}>
                <Form.Item label="Submission Date Range" name="dateRange" className="mb-0">
                  <DatePicker.RangePicker 
                    style={{ width: '100%' }}
                    onChange={(_, dateStrings) => {
                      const startDate = dateStrings[0] ? new Date(dateStrings[0]) : null;
                      const endDate = dateStrings[1] ? new Date(dateStrings[1]) : null;
                      setDateRange([startDate, endDate]);
                    }}
                    className="w-full"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={8} lg={4}>
                <Form.Item className="flex justify-end mb-0">
                  <Button 
                    icon={<FilterOutlined />} 
                    onClick={handleFilterReset}
                    className="w-full md:w-auto"
                  >
                    Reset Filters
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
        {loading ? (
          <Spin />
        ) : (
          <Table
            columns={columns}
            dataSource={filteredEntries}
            loading={loading}
            rowKey="id"
            scroll={{ x: 'max-content' }}
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              onChange: (page, size) => {
                setCurrentPage(page);
                setPageSize(size || 10);
              },
              showSizeChanger: true,
              showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
            }}
          />
        )}
      </Card>
    </div>
  );
} 