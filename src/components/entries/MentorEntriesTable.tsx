'use client';

import { useState, useEffect } from 'react';
import { Table, Button, Space, Tag, Card, message as messageHook, Typography, Spin, Input, Select, Form, Row, Col, DatePicker } from 'antd';
import { SearchOutlined, FilterOutlined } from '@ant-design/icons';
import { useSession } from 'next-auth/react';
import { Submission, UserRole } from '@/types';
import { viewDocument } from '@/lib/utils';
import dayjs from 'dayjs';

interface SubmissionsTableProps {
  userId: string;
  isAdmin?: boolean;
  title?: string;
}

export default function MentorSubmissionsTable({
  userId,
  isAdmin = false,
  title = "Manage All Submissions"
}: SubmissionsTableProps) {
  const { data: session } = useSession();
  const [messageApi, contextHolder] = messageHook.useMessage();
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [filteredSubmissions, setFilteredSubmissions] = useState<Submission[]>([]);
  const [loading, setLoading] = useState(true);
  const [filterForm] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [dateRange, setDateRange] = useState<[null | Date, null | Date]>([null, null]);

  useEffect(() => {
    fetchSubmissions();
  }, [userId, isAdmin]);
  
  useEffect(() => {
    let filtered = submissions;
    
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      filtered = filtered.filter(submission => {
        const traineeMatch = submission.trainee?.name?.toLowerCase().includes(searchLower);
        const titleMatch = submission.title?.toLowerCase().includes(searchLower);
        
        const entriesMatch = submission.entries?.some(entry => {
          const entryTitleMatch = entry.title?.toLowerCase().includes(searchLower);
          const experienceMatch = entry.experience?.toLowerCase().includes(searchLower);
          const learntMatch = entry.learnt?.toLowerCase().includes(searchLower);
          const placementMatch = entry.placement?.client?.name?.toLowerCase().includes(searchLower);
          const skillsMatch = entry.entrySubSkills?.some(skill => 
            skill.subSkill?.name?.toLowerCase().includes(searchLower) ||
            skill.subSkill?.practiceSkillGroup?.name?.toLowerCase().includes(searchLower) ||
            skill.subSkill?.practiceSkillGroup?.practiceSkill?.name?.toLowerCase().includes(searchLower)
          );
          return entryTitleMatch || experienceMatch || learntMatch || placementMatch || skillsMatch;
        });
        
        return traineeMatch || titleMatch || entriesMatch;
      });
    }
    
    if (selectedStatus) {
      filtered = filtered.filter(submission => submission.status?.toLowerCase() === selectedStatus.toLowerCase());
    }
    
    if (dateRange[0] && dateRange[1]) {
      const startDate = dateRange[0];
      const endDate = dateRange[1];
      
      filtered = filtered.filter(submission => {
        const submittedDate = submission.submittedAt ? new Date(submission.submittedAt) : null;
        if (!submittedDate) return false;
        
        return submittedDate >= startDate && submittedDate <= endDate;
      });
    }
    
    setFilteredSubmissions(filtered);
    setCurrentPage(1);
  }, [searchText, selectedStatus, dateRange, submissions]);

  const fetchSubmissions = async () => {
    setLoading(true);
    try {
      const url = isAdmin ? '/api/submissions' : `/api/submissions?userId=${userId}`;
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch submissions: ${response.statusText}`);
      }
      const data = await response.json();
      
      const submissionsData = data.submissions ? data.submissions.edges : data;
      setSubmissions(submissionsData);
      setFilteredSubmissions(submissionsData);
    } catch (error: unknown) {
      messageApi.error(error instanceof Error ? error.message : 'Failed to load submissions');
      setSubmissions([]);
      setFilteredSubmissions([]);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/submissions/${id}`, {
        method: 'DELETE',
      });
      if (response.ok) {
        messageApi.success('Submission deleted successfully');
        fetchSubmissions();
      } else {
        const errorData = await response.json();
        messageApi.error(errorData.error || 'Failed to delete submission');
      }
    } catch (error) {
      console.error('Error deleting submission:', error);
      messageApi.error('Failed to delete submission');
    }
  };

  const getStatusColor = (status: string) => {
    if (!status) return 'default';
    const lowerStatus = status.toLowerCase();
    if (lowerStatus === 'approved') return 'green';
    if (lowerStatus === 'rejected') return 'red';
    if (lowerStatus === 'pending') return 'orange';
    return 'default';
  };

  const columns = [
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      render: (title: string) => (
        <div className='max-w-[200px] overflow-hidden text-ellipsis'>
          {title || 'Untitled'}
        </div>
      ),
    },
    {
      title: 'Trainee',
      dataIndex: ['trainee', 'name'],
      key: 'trainee',
      render: (name: string | null) => name || '',
    },
    {
      title: 'Placement',
      key: 'placement',
      render: (_: unknown, record: Submission) => {
        const firstEntry = record.entries?.[0];
        return firstEntry?.placement?.client?.name || '-';
      },
    },
    {
      title: 'Entries Count',
      key: 'entriesCount',
      render: (_: unknown, record: Submission) => record.entries?.length || 0,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => <Tag color={getStatusColor(status)}>{status?.toUpperCase() || ''}</Tag>,
    },
    {
      title: 'Date Range',
      key: 'dateRange',
      render: (_: unknown, record: Submission) => {
        const startDate = record.startDate ? dayjs(record.startDate).format('DD/MM/YYYY') : '';
        const endDate = record.endDate ? dayjs(record.endDate).format('DD/MM/YYYY') : '';
        return startDate && endDate ? `${startDate} - ${endDate}` : '-';
      },
    },
    {
      title: 'Submitted At',
      dataIndex: 'submittedAt',
      key: 'submittedAt',
      render: (date: string) => date ? new Date(date).toLocaleDateString() : '-',
    },
    {
      title: 'Reviewed By',
      dataIndex: ['reviewer', 'name'],
      key: 'reviewer',
      render: (name?: string | null) => name || '-',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: unknown, record: Submission) => (
        <Space>
          <Button
            type="link"
            onClick={() => window.open(`/signoff-generator/${record.id}`, '_blank')}
          >
            Preview
          </Button>
        </Space>
      ),
    },
  ];

  const handleFilterReset = () => {
    filterForm.resetFields();
    setSearchText('');
    setSelectedStatus(null);
    setDateRange([null, null]);
  };

  return (
    <div className="p-6" style={{ maxWidth: '100vw', overflowX: 'auto' }}>
      {contextHolder}
      <Card title={title}>
        <div className="mb-4">
          <Form form={filterForm} layout="horizontal" className="w-full">
            <Row gutter={[16, 12]} className="w-full">
              <Col xs={24} md={12}>
                <Form.Item label="Search" name="search" className="mb-0">
                  <Input
                    placeholder="Search by trainee, title, placement, entry content, skills, etc..."
                    prefix={<SearchOutlined />}
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    allowClear
                    className="w-full"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item label="Status" name="status" className="mb-0">
                  <Select 
                    placeholder="Filter by status"
                    allowClear
                    onChange={(value) => setSelectedStatus(value)}
                    value={selectedStatus}
                    className="w-full"
                  >
                    <Select.Option value="pending">PENDING</Select.Option>
                    <Select.Option value="approved">APPROVED</Select.Option>
                    <Select.Option value="rejected">REJECTED</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={16}>
                <Form.Item label="Submission Date Range" name="dateRange" className="mb-0">
                  <DatePicker.RangePicker 
                    style={{ width: '100%' }}
                    onChange={(_, dateStrings) => {
                      const startDate = dateStrings[0] ? new Date(dateStrings[0]) : null;
                      const endDate = dateStrings[1] ? new Date(dateStrings[1]) : null;
                      setDateRange([startDate, endDate]);
                    }}
                    className="w-full"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={8} lg={4}>
                <Form.Item className="flex justify-end mb-0">
                  <Button 
                    icon={<FilterOutlined />} 
                    onClick={handleFilterReset}
                    className="w-full md:w-auto"
                  >
                    Reset Filters
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
        {loading ? (
          <Spin />
        ) : (
          <Table
            columns={columns}
            dataSource={filteredSubmissions}
            loading={loading}
            rowKey="id"
            scroll={{ x: 'max-content' }}
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              onChange: (page, size) => {
                setCurrentPage(page);
                setPageSize(size || 10);
              },
              showSizeChanger: true,
              showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
            }}
          />
        )}
      </Card>
    </div>
  );
} 