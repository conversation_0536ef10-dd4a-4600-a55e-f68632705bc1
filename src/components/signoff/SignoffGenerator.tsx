'use client';

import React, { useState, useEffect } from 'react';
import dayjs from 'dayjs';
import { practiceAreaOptions } from '@/helpers/practiceAreas';
import { Spin, Typography, Button, message, Tag, Divider } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import { viewDocument } from '@/lib/utils';

const { Title } = Typography;

interface Entry {
  id: string;
  title: string;
  startDate: string;
  endDate: string;
  experience: string;
  learnt: string;
  needMoreExperience: string;
  taskedBy?: string;
  contentiousType?: string;
  practiceAreas?: number[];
  entrySubSkills?: {
    subSkill: {
      id: string;
      name: string;
      practiceSkillGroup: {
        id: string;
        name: string;
        practiceSkill: {
          id: string;
          name: string;
        };
      };
    };
  }[];
  documentKey?: string;
  documentName?: string;
  placement?: {
    client?: {
      name: string;
    };
    user?: {
      name: string;
    };
  };
}

interface Submission {
  id: string;
  title: string;
  trainee: {
    firstName?: string;
    lastName?: string;
    name?: string;
  };
  entries: Entry[];
}

interface Props {
  signoffId: string;
}

function SignoffGenerator({ signoffId }: Props) {
  const [submission, setSubmission] = useState<Submission | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    fetchSubmission();
  }, [signoffId]);

  const fetchSubmission = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/submissions/${signoffId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch submission');
      }
      const data = await response.json();
      setSubmission(data);
    } catch (error) {
      console.error('Error fetching submission:', error);
      setError('Failed to load submission data');
      message.error('Failed to load submission data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (error || !submission) {
    return (
      <div className="flex justify-center items-center h-64">
        <div>
          <Title level={4}>Error</Title>
          <p>{error || 'Submission not found'}</p>
        </div>
      </div>
    );
  }

    const candidate = submission.trainee;
  const sortedEntries = [...(submission.entries || [])].sort((a, b) => {
    const aDate = dayjs(a?.startDate).unix();
    const bDate = dayjs(b?.startDate).unix();
    return aDate > bDate ? 1 : -1;
  });

  const getContentiousTypeDisplay = (type?: string) => {
    switch(type) {
      case 'contentious':
        return 'Contentious';
      case 'non_contentious':
        return 'Non-Contentious';
      case 'both':
        return 'Both';
      default:
        return type || '';
    }
  };

  const getPracticeAreaNames = (practiceAreas?: number[]) => {
    return Array.isArray(practiceAreas)
      ? practiceAreas.map(id => {
          const found = practiceAreaOptions.find(area => String(area.id) === String(id));
          return found ? found.title : String(id);
        })
      : [];
  };

  const getGroupedSkills = (entrySubSkills?: Entry['entrySubSkills']) => {
    const groupedSkills: {
      [skillName: string]: {
        [groupName: string]: string[]
      }
    } = {};

    if (Array.isArray(entrySubSkills)) {
      entrySubSkills.forEach(entrySubSkill => {
        const skill = entrySubSkill.subSkill;
        const skillName = skill.practiceSkillGroup?.practiceSkill?.name || 'Other Skills';
        const groupName = skill.practiceSkillGroup?.name || 'Other Group';
        const subSkillName = skill.name;

        if (!groupedSkills[skillName]) {
          groupedSkills[skillName] = {};
        }
        if (!groupedSkills[skillName][groupName]) {
          groupedSkills[skillName][groupName] = [];
        }
        if (!groupedSkills[skillName][groupName].includes(subSkillName)) {
          groupedSkills[skillName][groupName].push(subSkillName);
        }
      });
    }

    return Object.entries(groupedSkills).sort(([a], [b]) => {
      const aPrefix = a.match(/^[A-Z]/)?.[0] || '';
      const bPrefix = b.match(/^[A-Z]/)?.[0] || '';
      return aPrefix.localeCompare(bPrefix);
    });
  };

  const getGroupPrefix = (groupName: string) => {
    const match = groupName.match(/^[A-Z](\d+)/);
    return match ? parseInt(match[1]) : 999;
  };

  return (
    <>
      <div className="text-center p-5 bg-white print:hidden">
        <Button 
          type="primary" 
          icon={<DownloadOutlined />}
          onClick={() => window.print()}
          className="mb-5"
        >
          Print / Download PDF
        </Button>
      </div>
      
      <div className="min-h-screen bg-white py-8">
        {sortedEntries.map((entry, index) => {
          const practiceAreaNames = getPracticeAreaNames(entry.practiceAreas);
          const sortedSkills = getGroupedSkills(entry.entrySubSkills);
          
          return (
            <div key={entry?.id} className={`print-preview bg-white rounded-lg shadow ${index > 0 ? 'page-break-before-always' : ''}`} style={{ padding: 32, maxWidth: 900, margin: '0 auto', marginBottom: index < sortedEntries.length - 1 ? 32 : 0 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <img src="https://admin.accutraineepw.com/images/logos/logo--dark.svg" alt="Accutrainee Logo" style={{ width: 180 }} />
                <div>
                  <div style={{ fontWeight: 700, fontSize: 14 }}>Candidate:</div>
                  <div style={{ color: '#f27b21', fontWeight: 700, fontSize: 22 }}>
                    {candidate?.name || `${candidate?.firstName || ''} ${candidate?.lastName || ''}`.trim()}
                  </div>
                </div>
              </div>
              <Divider />
              
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
                <div>
                  <Typography.Title level={4} style={{ margin: 0 }}>{entry.title}</Typography.Title>
                  {entry.placement?.client?.name && (
                    <div style={{ marginTop: 8 }}>
                      <strong>Placement:</strong> {entry.placement.client.name}
                    </div>
                  )}
                  {entry.taskedBy && (
                    <div style={{ marginTop: 4 }}>
                      <strong>Tasked By:</strong> {entry.taskedBy}
                    </div>
                  )}
                  {practiceAreaNames.length > 0 && (
                    <div style={{ marginTop: 8 }}>
                      <strong>Practice Areas:</strong>
                      <div>
                        {practiceAreaNames.map(name => (
                          <Tag key={name} color="orange">{name}</Tag>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
                <div style={{ textAlign: 'right' }}>
                  <div>
                    <strong>Start Date:</strong> {dayjs(entry.startDate).format('DD/MM/YYYY')}
                  </div>
                  <div>
                    <strong>End Date:</strong> {dayjs(entry.endDate).format('DD/MM/YYYY')}
                  </div>
                  {entry.contentiousType && (
                    <div style={{ marginTop: 4 }}>
                      <strong>Matter Type:</strong> {getContentiousTypeDisplay(entry.contentiousType)}
                    </div>
                  )}
                </div>
              </div>
              <Divider />
              
              <Typography.Title level={5}>Details of the task/work completed</Typography.Title>
              <Typography.Paragraph style={{ whiteSpace: 'pre-wrap' }}>{entry.experience}</Typography.Paragraph>
              
              <Typography.Title level={5}>Reflections/What did I learn?</Typography.Title>
              <Typography.Paragraph style={{ whiteSpace: 'pre-wrap' }}>{entry.learnt}</Typography.Paragraph>
              
              <Typography.Title level={5}>How can I improve?</Typography.Title>
              <Typography.Paragraph style={{ whiteSpace: 'pre-wrap' }}>{entry.needMoreExperience}</Typography.Paragraph>
              
              {entry.documentName && entry.documentKey && entry.documentKey !== 'null' && (
                <>
                  <Divider />
                  <Typography.Title level={5}>Supporting Document</Typography.Title>
                  <ul>
                    <li key={entry.documentKey}>
                      <a onClick={() => entry.documentKey && viewDocument(entry.documentKey)}>
                        {entry.documentName}
                      </a>
                    </li>
                  </ul>
                </>
              )}
              
              {sortedSkills.length > 0 && (
                <>
                  <Divider style={{ margin: '16px 0' }} />
                  <div style={{ marginBottom: 16 }}>
                    <Typography.Title level={5} style={{ marginBottom: 0 }}>Competency/Skills:</Typography.Title>
                    <div style={{ marginTop: 4 }}>
                      {sortedSkills.map(([skillName, groups]) => (
                        <div key={skillName} style={{ marginBottom: 32 }}>
                          <Typography.Title level={5} style={{ marginBottom: 8 }}>{skillName}</Typography.Title>
                          {Object.entries(groups)
                            .sort(([a], [b]) => {
                              const prefixA = getGroupPrefix(a);
                              const prefixB = getGroupPrefix(b);
                              return prefixA - prefixB;
                            })
                            .map(([groupName, subSkills]) => (
                              <div key={groupName} style={{ marginLeft: 20, marginBottom: 16 }}>
                                <Typography.Text strong>{groupName}</Typography.Text>
                                <ul style={{ marginTop: 4, marginBottom: 8, marginLeft: 20 }}>
                                  {subSkills.sort().map((subSkill, idx) => (
                                    <li key={idx}>{subSkill}</li>
                                  ))}
                                </ul>
                              </div>
                            ))}
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </div>
          );
        })}
      </div>
      
      <style jsx global>{`
        @media print {
          .print\\:hidden { display: none !important; }
          body { background: #fff !important; }
          .page-break-before-always { page-break-before: always; }
        }
      `}</style>
    </>
  );
}

export default SignoffGenerator; 