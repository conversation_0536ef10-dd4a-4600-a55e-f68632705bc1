'use client';

import { useState, useEffect } from 'react';
import { useSession, signIn, signOut } from 'next-auth/react';
import { <PERSON><PERSON>, But<PERSON>, App } from 'antd';
import { useRouter } from 'next/navigation';
import PasswordModal from './admin/PasswordModal';

export default function ImpersonationBanner() {
  const { data: session } = useSession();
  const router = useRouter();
  const [passwordModalOpen, setPasswordModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [adminEmail, setAdminEmail] = useState('');
  const [impersonationId, setImpersonationId] = useState('');
  const { message } = App.useApp();
  useEffect(() => {
    if (typeof window !== 'undefined' && session?.user?.isImpersonated) {
      const storedData = window.sessionStorage.getItem('adminImpersonation');
      
      if (!storedData && session.user.impersonatedBy) {
        const adminData = {
          adminId: session.user.impersonatedBy,
          adminEmail: typeof session.user.impersonatedBy === 'string' 
            ? session.user.impersonatedBy 
            : session.user.impersonatedBy.email || ''
        };
        window.sessionStorage.setItem('adminImpersonation', JSON.stringify(adminData));
      }
    }
  }, [session?.user?.isImpersonated, session?.user?.impersonatedBy]);

  const showPasswordModal = () => {
    if (typeof window === 'undefined') return;
    
    // Lấy thông tin admin từ session storage
    const storedData = window.sessionStorage.getItem('adminImpersonation');
    
    // Nếu có thông tin trong session storage
    if (storedData) {
      try {
        const adminData = JSON.parse(storedData);
        
        if (adminData.adminEmail) {
          setAdminEmail(adminData.adminEmail);
          setImpersonationId(session?.user?.impersonationId || '');
          setPasswordModalOpen(true);
          return;
        }
      } catch (error) {
        console.error('Error parsing admin data:', error);
      }
    }
    
    // Nếu không có thông tin trong session storage hoặc parse lỗi, thử lấy từ session
    if (session?.user?.impersonatedBy) {
      let email = '';
      
      if (typeof session.user.impersonatedBy === 'string') {
        email = session.user.impersonatedBy;
      } else if (session.user.impersonatedBy && typeof session.user.impersonatedBy === 'object') {
        email = session.user.impersonatedBy.email || '';
      }
      
      if (email) {
        setAdminEmail(email);
        setImpersonationId(session.user.impersonationId || '');
        setPasswordModalOpen(true);
        return;
      }
    }
    
    // Nếu không tìm thấy thông tin admin
    message.error('Admin information not found');
  };

  const handlePasswordConfirm = async (password: string) => {
    try {
      setLoading(true);
      message.loading('Returning to admin account...', 1);
      
      const response = await fetch('/api/admin/impersonate/end', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          impersonationId: session?.user?.impersonationId
        })
      });

      if (!response.ok) {
        throw new Error('Failed to end impersonation session');
      }

      await signOut({ redirect: false });

      const result = await signIn('credentials', {
        email: adminEmail,
        password: password,
        adminReturn: 'true',
        redirect: false,
      });

      if (!result || result.error) {
        throw new Error('Invalid admin password');
      }

      if (typeof window !== 'undefined') {
        window.sessionStorage.removeItem('adminImpersonation');
      }

      await fetch('/api/admin/activity-log', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'END_IMPERSONATION',
          targetUserId: session?.user?.id,
          details: {
            endedAt: new Date().toISOString(),
            impersonationId
          }
        })
      }).catch(err => console.error('Failed to log activity:', err));

      message.success('Successfully returned to admin account');

      router.push('/admin/dashboard');
      router.refresh();
    } catch (error) {
      console.error('Error ending impersonation:', error);
      message.error(error instanceof Error ? error.message : 'An error occurred while ending impersonation');
      setLoading(false);
    }
  };

  const handleEndImpersonation = () => {
    showPasswordModal();
  };

  if (!session?.user?.isImpersonated) {
    return null;
  }

  return (
    <>
      <Alert
        message="Impersonation Mode"
        description={
          <div className="flex justify-between items-center">
            <div>
              <span>
                You are viewing as <strong>{session.user.name}</strong> (Impersonated by Admin: {session.user.impersonatedBy?.name})
              </span>
            </div>
            <Button 
              type="primary" 
              danger 
              onClick={handleEndImpersonation}
              loading={loading}
            >
              Exit Impersonation
            </Button>
          </div>
        }
        type="warning"
        showIcon
        className="mb-4"
        banner
      />
      <PasswordModal
        open={passwordModalOpen}
        onCancel={() => setPasswordModalOpen(false)}
        onConfirm={handlePasswordConfirm}
        title="Enter Admin Password"
        confirmLoading={loading}
        email={adminEmail}
      />
    </>
  );
}
