import { Tag } from 'antd';

interface StatusTagProps {
  status: string;
}

export const StatusTag = ({ status }: StatusTagProps) => {
  const statusConfig = {
    pending: { color: 'gold', text: 'Pending' },
    approved: { color: 'green', text: 'Approved' },
    rejected: { color: 'red', text: 'Rejected' },
  };

  const config = statusConfig[status.toLowerCase() as keyof typeof statusConfig] || 
    { color: 'default', text: status };

  return (
    <Tag color={config.color}>
      {config.text}
    </Tag>
  );
};