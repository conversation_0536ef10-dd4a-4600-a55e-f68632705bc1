'use client';

import { useState, useEffect } from 'react';
import { Card, Typography, Button, Row, Col, Form, Input, Select, Space, App, Checkbox, Table } from 'antd';
import { EditOutlined, SaveOutlined, CloseOutlined } from '@ant-design/icons';
import { User, UserRole } from '@/types';

const { Text } = Typography;

interface UserInfoFormProps {
  user: User;
  onUpdate: (updatedUser: User) => void;
  isMentorView?: boolean;
}

export default function UserInfoForm({ user, onUpdate, isMentorView }: UserInfoFormProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [form] = Form.useForm();
  const [saveLoading, setSaveLoading] = useState(false);
  const [mentors, setMentors] = useState<User[]>([]);
  const [selectedMentees, setSelectedMentees] = useState<User[]>([]);
  const [canChangeQualificationRoute, setCanChangeQualificationRoute] = useState(true);
  const { message } = App.useApp();

  const menteeColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: 'Qualification Route',
      dataIndex: 'qualificationRoute',
      key: 'qualificationRoute',
    },
  ];

  useEffect(() => {
    const fetchMentorsAndMentees = async () => {
      try {
        const [mentorsRes] = await Promise.all([
          fetch('/api/users?role=MENTOR'),
        ]);

        if (mentorsRes.ok) {
          const mentorsData = await mentorsRes.json();
          console.log('Fetched mentors:', mentorsData);
          setMentors(mentorsData);
        }
      } catch (error) {
        console.error('Error fetching mentors and mentees:', error);
      }
    };

    fetchMentorsAndMentees();
  }, []);

  useEffect(() => {
    const checkTraineeProgress = async () => {
      if (user.role !== UserRole.TRAINEE) {
        setCanChangeQualificationRoute(true);
        return;
      }

      try {
        const response = await fetch(`/api/trainee-skills/check-progress?traineeId=${user.id}`);
        
        if (response.ok) {
          const data = await response.json();
          setCanChangeQualificationRoute(!data.hasProgress);
        } else if (response.status === 404) {
          setCanChangeQualificationRoute(true);
        } else {
          console.error('Error checking trainee progress');
          setCanChangeQualificationRoute(false);
        }
      } catch (error) {
        console.error('Error checking trainee progress:', error);
        setCanChangeQualificationRoute(false);
      }
    };

    if (user.role === UserRole.TRAINEE && !isMentorView) {
      checkTraineeProgress();
    }
  }, [user, isMentorView]);

  useEffect(() => {
    if (user.mentees) {
      setSelectedMentees(user.mentees);
    }
  }, [user.mentees]);

  const handleEdit = () => {
    form.setFieldsValue({
      name: user.name,
      email: user.email,
      role: user.role,
      phoneNumber: user.phoneNumber || '',
      address: user.address || '',
      qualificationRoute: user.qualificationRoute || '',
      mentorId: user.mentorId || null,
      traineeLevel: user.traineeLevel || 1,
      isTrainingPrincipal: user.isTrainingPrincipal || false
    });
    setIsEditing(true);
  };

  const handleSave = async () => {
    try {
      setSaveLoading(true);
      const values = await form.validateFields();
      
      if (user.role === UserRole.MENTOR) {
        values.menteeIds = selectedMentees.map(mentee => mentee.id);
      }
      
      const response = await fetch(`/api/users/${user.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (response.ok) {
        const updatedUser = await response.json();
        onUpdate(updatedUser);
        message.success('User updated successfully');
        setIsEditing(false);
      } else {
        const errorData = await response.json();
        message.error(errorData.error || 'Failed to update user');
      }
    } catch (error) {
      console.error('Error updating user:', error);
      message.error('Failed to update user');
    } finally {
      setSaveLoading(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    form.resetFields();
  };

  return (
    <Card title={isMentorView ? "Mentee Info" : "User Info"} className="mb-6">
      {isEditing && !isMentorView ? (
        <Form form={form} layout="vertical">
          <Row gutter={[24, 16]}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="Full Name"
                rules={[{ required: true, message: 'Please input full name!' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="email"
                label="Contact Email"
                rules={[
                  { required: true, message: 'Please input email!' },
                  { type: 'email', message: 'Please enter a valid email!' }
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="role"
                label="Job Title"
                rules={[{ required: true, message: 'Please select role!' }]}
              >
                <Select>
                  <Select.Option value="ADMIN">Admin</Select.Option>
                  <Select.Option value="SUPERVISOR">Supervisor</Select.Option>
                  <Select.Option value="MENTOR">Mentor</Select.Option>
                  <Select.Option value="TRAINEE">Trainee</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="phoneNumber"
                label="Phone Number"
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="address"
                label="Bio"
              >
                <Input.TextArea rows={3} placeholder="Enter bio information..." />
              </Form.Item>
            </Col>
            {user.role === UserRole.TRAINEE && (
              <>
                <Col span={12}>
                  <Form.Item
                    name="qualificationRoute"
                    label="Qualification Route"
                    tooltip={!canChangeQualificationRoute ? 
                      "Cannot change qualification route once skills progress has started" : undefined}
                  >
                    <Select 
                      placeholder="Select qualification route" 
                      disabled={!canChangeQualificationRoute}
                    >
                      <Select.Option value="TC">TC</Select.Option>
                      <Select.Option value="SQE">SQE</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="mentorId"
                    label="Mentor"
                  >
                    <Select allowClear placeholder="Select a mentor">
                      {mentors.map((mentor) => (
                        <Select.Option key={mentor.id} value={mentor.id}>
                          {mentor.name}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="traineeLevel"
                    label="Status"
                    rules={[
                      { type: 'number', min: 1, max: 3, message: 'Level must be between 1 and 3' }
                    ]}
                  >
                    <Select>
                      <Select.Option value={1}>Level 1</Select.Option>
                      <Select.Option value={2}>Level 2</Select.Option>
                      <Select.Option value={3}>Level 3</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
              </>
            )}
            {user.role === UserRole.MENTOR && (
              <>
                <Col span={12}>
                  <Form.Item
                    name="isTrainingPrincipal"
                    label="Training Principal"
                    valuePropName="checked"
                  >
                    <Checkbox>Is Training Principal</Checkbox>
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <Text strong className="mb-2!">Current Mentees</Text>
                  <Table
                    columns={menteeColumns}
                    dataSource={user.mentees || []}
                    rowKey="id"
                    size="small"
                    pagination={false}
                  />
                </Col>
              </>
            )}
          </Row>
          <div className="mt-4">
            <Space>
              <Button onClick={handleCancel} icon={<CloseOutlined />}>Cancel</Button>
              <Button 
                type="primary" 
                onClick={handleSave} 
                icon={<SaveOutlined />}
                loading={saveLoading}
              >
                Save
              </Button>
            </Space>
          </div>
        </Form>
      ) : (
        <>
          <Row gutter={[24, 16]}>
            <Col span={12}>
              <Text strong>Full Name</Text>
              <div>{user.name}</div>
            </Col>
            <Col span={12}>
              <Text strong>Contact Email</Text>
              <div>{user.email}</div>
            </Col>
            <Col span={12}>
              <Text strong>Job Title</Text>
              <div>{user.role === UserRole.TRAINEE ? 'Trainee' : user.role}</div>
            </Col>
            <Col span={12}>
              <Text strong>Phone Number</Text>
              <div>{user.phoneNumber || ''}</div>
            </Col>
            <Col span={12}>
              <Text strong>Bio</Text>
              <div style={{ whiteSpace: 'pre-wrap' }}>{user.address || ''}</div>
            </Col>
            {user.role === UserRole.TRAINEE && (
              <>
                <Col span={12}>
                  <Text strong>Qualification Route</Text>
                  <div>{user.qualificationRoute || ''}</div>
                </Col>
                <Col span={12}>
                  <Text strong>Mentor</Text>
                  <div>{user.mentor?.name || 'Not assigned'}</div>
                </Col>
                <Col span={12}>
                  <Text strong>Status</Text>
                  <div>level{user.traineeLevel || 1}</div>
                </Col>
              </>
            )}
            {user.role === UserRole.MENTOR && (
              <>
                <Col span={12}>
                  <Text strong>Training Principal</Text>
                  <div>{user.isTrainingPrincipal ? 'Yes' : 'No'}</div>
                </Col>
                <Col span={24}>
                  <Text strong className="mb-2!">Current Mentees</Text>
                  <Table
                    columns={menteeColumns}
                    dataSource={user.mentees || []}
                    rowKey="id"
                    size="small"
                    pagination={false}
                  />
                </Col>
              </>
            )}
          </Row>
          {!isMentorView && (
            <div className="mt-4">
              <Button type="primary" icon={<EditOutlined />} onClick={handleEdit}>EDIT</Button>
            </div>
          )}
        </>
      )}
    </Card>
  );
} 