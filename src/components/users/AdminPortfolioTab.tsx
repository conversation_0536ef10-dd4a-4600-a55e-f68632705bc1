'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Progress,
  Spin,
  Tag,
  Statistic,
  Row,
  Col,
  Empty,
  App
} from 'antd';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  PlusOutlined,
  MinusOutlined
} from '@ant-design/icons';
import {
  PortfolioSubSkill,
  PortfolioSkillGroup,
  PortfolioSkill,
  PortfolioProgressData,
} from '@/types';

const { Title, Text } = Typography;

interface AdminPortfolioTabProps {
  userId: string;
}


export default function AdminPortfolioTab({ userId }: AdminPortfolioTabProps) {
  const [loading, setLoading] = useState(true);
  const [progressData, setProgressData] = useState<PortfolioProgressData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const { message } = App.useApp();

  useEffect(() => {
    fetchProgress();
  }, [userId]);

  const fetchProgress = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/trainee-skills/progress?traineeId=${userId}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          const data = await response.json();
          if (data.needsInitialization) {
            setProgressData(null);
            return;
          }
        }
        throw new Error('Failed to fetch progress data');
      }

      const data = await response.json();
      console.log('data', data);
      setProgressData(data);
    } catch (error) {
      console.error('Error fetching progress:', error);
      message.error('Failed to load progress data');
      setError('Failed to load progress data');
    } finally {
      setLoading(false);
    }
  };

  const toggleExpand = (itemKey: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemKey)) {
        newSet.delete(itemKey);
      } else {
        newSet.add(itemKey);
      }
      return newSet;
    });
  };

  const renderProgressSection = (progress: number, count?: { done: number, total: number }) => {
    return (
      <div className="flex items-center justify-end gap-2 w-full">
        <Progress 
          percent={Math.round(progress)} 
          size="small"
          strokeColor={{
            '0%': '#f27b31',
            '100%': '#f9e1b4',
          }}
          className="flex-grow"
        />
        {count && (
          <span className="text-sm whitespace-nowrap flex-shrink-0">
            {count.done}/{count.total}
          </span>
        )}
      </div>
    );
  };

  const renderSkillItem = (
    name: string, 
    progress: number, 
    isCompleted: boolean,
    itemKey: string,
    hasChildren: boolean,
    count?: { done: number, total: number },
  ) => {
    const isExpanded = expandedItems.has(itemKey);
    
    return (
      <div 
        className={`flex items-center w-full border border-gray-200 rounded-md my-2 py-2 px-4 hover:bg-gray-50 ${hasChildren ? 'cursor-pointer' : ''} gap-x-4`}
        onClick={() => hasChildren && toggleExpand(itemKey)}
      >
        <div className="flex items-center gap-2 flex-grow min-w-0">
          <div className="w-6 min-w-[24px] flex-shrink-0">
            {hasChildren && (
              <div className="w-6 h-6 flex items-center justify-center bg-[#f27b31] rounded-full text-white">
                {isExpanded ? <MinusOutlined /> : <PlusOutlined />}
              </div>
            )}
          </div>
          <span className="truncate flex-1">{name}</span>
        </div>
        
        <div className="flex justify-end flex-shrink-0 w-[220px]">
          {renderProgressSection(progress, count)}
        </div>
      </div>
    );
  };

  const renderSubSkills = (subSkills: PortfolioSubSkill[]) => {
    return subSkills.map(subSkill => (
      <div key={subSkill.id}>
        {renderSkillItem(
          subSkill.name,
          subSkill.progress,
          subSkill.isCompleted,
          `subskill-${subSkill.id}`,
          false,
          { done: subSkill.doneEntryCount, total: subSkill.minSuggestedEntryCount },
        )}
      </div>
    ));
  };

  const renderGroups = (groups: PortfolioSkillGroup[]) => {
    return groups.map(group => {
      const isExpanded = expandedItems.has(`group-${group.id}`);
      
      return (
        <div key={group.id} className="pl-6">
          {renderSkillItem(
            group.name,
            group.progress,
            group.isCompleted,
            `group-${group.id}`,
            group.subSkills.length > 0,
            { done: group.subSkills.filter(s => s.isCompleted).length, total: group.subSkills.length },
          )}
          {isExpanded && group.subSkills.length > 0 && (
            <div className="pl-6">
              {renderSubSkills(group.subSkills)}
            </div>
          )}
        </div>
      );
    });
  };

  const renderSkills = (skills: PortfolioSkill[]) => {
    const skillsMap = new Map<string, PortfolioSkill>();
    skills.forEach(skill => {
      const existingSkill = skillsMap.get(skill.name);
      if (existingSkill) {
        const groupsMap = new Map<string, PortfolioSkillGroup>();
        
        existingSkill.groups.forEach(group => {
          groupsMap.set(group.name, {
            id: group.id,
            name: group.name,
            progress: group.progress,
            isCompleted: group.isCompleted,
            subSkills: [...group.subSkills]
          });
        });
        
        skill.groups.forEach(group => {
          groupsMap.set(group.name, {
            id: group.id,
            name: group.name,
            progress: group.progress,
            isCompleted: group.isCompleted,
            subSkills: [...group.subSkills]
          });
        });
        
        existingSkill.groups = Array.from(groupsMap.values());
      } else {
        skillsMap.set(skill.name, {
          id: skill.id,
          name: skill.name,
          progress: skill.progress,
          isCompleted: skill.isCompleted,
          groups: skill.groups.map(group => ({
            id: group.id,
            name: group.name,
            progress: group.progress,
            isCompleted: group.isCompleted,
            subSkills: [...group.subSkills]
          }))
        });
      }
    });
    console.log('skillsMap', skillsMap);
    return Array.from(skillsMap.values()).map(skill => {
      const isExpanded = expandedItems.has(`skill-${skill.id}`);
      return (
        <div key={skill.id}>
          {renderSkillItem(
            skill.name,
            skill.progress,
            skill.isCompleted,
            `skill-${skill.id}`,
            skill.groups.length > 0,
            { done: skill.groups.filter(g => g.isCompleted).length, total: skill.groups.length },
          )}
          {isExpanded && skill.groups.length > 0 && (
            <div>
              {renderGroups(skill.groups)}
            </div>
          )}
        </div>
      );
    });
  };

  const getStatusTag = (isCompleted: boolean) => (
    isCompleted ? 
      <Tag color="success" icon={<CheckCircleOutlined />}>Completed</Tag> :
      <Tag color="processing" icon={<ClockCircleOutlined />}>In Progress</Tag>
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-64">
        <Text type="danger">{error}</Text>
      </div>
    );
  }

  if (!progressData) {
    return (
      <Empty 
        description="No portfolio data available. The trainee needs to submit entries to initialize their skills tracking."
      />
    );
  }

  // Filter practice area based on qualification route
  const practiceArea = progressData.practiceAreas.find(area => {
    const areaType = area.name.toLowerCase();
    const route = progressData.trainee.qualificationRoute?.toLowerCase() || 'tc';
    return areaType.includes(route);
  });

  if (!practiceArea) {
    return <Empty description="No practice area found for this qualification route." />;
  }


  return (
    <div className="space-y-6">
      {/* Overall Progress */}
      <Card>
        <Row gutter={24}>
          <Col span={8}>
            <Statistic
              title="Overall Progress"
              value={Math.round(progressData.overallProgress)}
              suffix="%"
            />
            <Progress
              percent={Math.round(progressData.overallProgress)}
              status="active"
              className="mt-2"
              strokeColor={{
                '0%': '#f27b31',
                '100%': '#f9e1b4',
              }}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="Completed Skills"
              value={progressData.completedSkills}
              suffix={`/ ${progressData.totalSkills}`}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="Qualification Route"
              value={progressData.trainee.qualificationRoute || 'TC'}
            />
          </Col>
        </Row>
      </Card>

      {/* Skills Hierarchy */}
      <Card>
        <Title level={4}>Skills Progress</Title>
        <div className="overflow-hidden">
          {renderSkills(practiceArea.skills)}
        </div>
      </Card>
    </div>
  );
}