'use client';

import { useState } from 'react';
import { Table, Button, Space, App } from 'antd';
import { EditOutlined, PlusOutlined } from '@ant-design/icons';
import type { Placement } from '@/types';
import dayjs from 'dayjs';
import PlacementFormModal from '@/components/placement/PlacementFormModal';

interface UserPlacementsTabProps {
  placements: Placement[];
  userId: string;
  onPlacementUpdate: (tab: string) => void;
}

export default function UserPlacementsTab({
  placements,
  userId,
  onPlacementUpdate
}: UserPlacementsTabProps) {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingPlacement, setEditingPlacement] = useState<Placement | null>(null);
  const [saveLoading, setSaveLoading] = useState(false);
  const { message } = App.useApp();
  const handleEdit = (placement: Placement) => {
    setEditingPlacement(placement);
    setIsModalVisible(true);
  };

  // Columns based on src/app/admin/placements/page.tsx
  const placementsColumns = [
    {
      title: 'Organisation Name',
      dataIndex: ['client', 'name'],
      key: 'client',
      render: (name: string) => name || '',
    },
    {
      title: 'Supervisor',
      dataIndex: ['supervisor', 'name'],
      key: 'supervisor',
      render: (text: string) => text || 'Not assigned',
    },
    {
      title: 'Start Date',
      dataIndex: 'startDate',
      key: 'startDate',
      render: (date: string) => date ? dayjs(date).format('DD/MM/YYYY') : '',
    },
    {
      title: 'End Date',
      dataIndex: 'endDate',
      key: 'endDate',
      render: (date: string) => date ? dayjs(date).format('DD/MM/YYYY') : 'Ongoing',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: unknown, record: Placement) => (
        <Space>
          {/* <Button
            type="link"
            onClick={(e) => {
              e.stopPropagation();
              handleEdit(record);
            }}
          >
            View
          </Button> */}
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              handleEdit(record);
            }}
          />
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setEditingPlacement(null);
    setIsModalVisible(true);
  };

  const handleSavePlacement = async (values: Partial<Placement>) => {
    setSaveLoading(true);
    try {
      const url = editingPlacement
        ? `/api/placements/${editingPlacement.id}`
        : '/api/placements';
      const method = editingPlacement ? 'PATCH' : 'POST';

      // Ensure the userId is included when creating a new placement
      const payload = editingPlacement ? values : { ...values, userId };

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        message.success(`Placement ${editingPlacement ? 'updated' : 'created'} successfully`);
        setIsModalVisible(false);
        setEditingPlacement(null);
        onPlacementUpdate('placements'); // Pass the tab key to stay on placements tab
      } else {
        const errorData = await response.json();
        message.error(errorData.error || `Failed to ${editingPlacement ? 'update' : 'create'} placement`);
      }
    } catch (error) {
      console.error('Error saving placement:', error);
      message.error('Failed to save placement');
    } finally {
      setSaveLoading(false);
    }
  };

  return (
    <div>
      <Space style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAdd}
        >
          Add Placement
        </Button>
      </Space>
      <Table
        dataSource={placements}
        columns={placementsColumns}
        rowKey="id"
        pagination={{ pageSize: 10 }}
        scroll={{ x: 'max-content' }}
      />
      <PlacementFormModal
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingPlacement(null);
        }}
        onSubmit={handleSavePlacement}
        placement={editingPlacement || undefined}
        loading={saveLoading}
      />
    </div>
  );
} 