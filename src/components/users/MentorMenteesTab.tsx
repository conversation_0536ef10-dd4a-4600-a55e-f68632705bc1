'use client';

import { useState, useEffect } from 'react';
import { Card, Table, Tabs, Typography, Tag, Space, Spin, App } from 'antd';
import { User } from '@/types';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

interface MentorMenteesTabProps {
  mentorId: string;
}

interface MenteeData {
  id: string;
  name: string;
  email: string;
  qualificationRoute: string;
  traineeLevel: number;
  competencyProgress: number;
  lastActivity: string | null;
  relationshipStartDate: string;
  relationshipEndDate?: string;
  isActive: boolean;
}

export default function MentorMenteesTab({ mentorId }: MentorMenteesTabProps) {
  const [currentMentees, setCurrentMentees] = useState<MenteeData[]>([]);
  const [pastMentees, setPastMentees] = useState<MenteeData[]>([]);
  const [loading, setLoading] = useState(true);
  const { message } = App.useApp();

  useEffect(() => {
    fetchMentees();
  }, [mentorId]);

  const fetchMentees = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/mentors/${mentorId}/mentees?type=all`);
      
      if (response.ok) {
        const data = await response.json();
        setCurrentMentees(data.current || []);
        setPastMentees(data.past || []);
      } else {
        message.error('Failed to fetch mentees');
      }
    } catch (error) {
      console.error('Error fetching mentees:', error);
      message.error('Failed to fetch mentees');
    } finally {
      setLoading(false);
    }
  };

  const menteeColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (name: string) => name || 'N/A',
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      render: (email: string) => email || 'N/A',
    },
    {
      title: 'Qualification Route',
      dataIndex: 'qualificationRoute',
      key: 'qualificationRoute',
      render: (route: string) => route || 'N/A',
    },
    {
      title: 'Level',
      dataIndex: 'traineeLevel',
      key: 'traineeLevel',
      render: (level: number) => `Level ${level || 1}`,
    },
    {
      title: 'Progress',
      dataIndex: 'competencyProgress',
      key: 'competencyProgress',
      render: (progress: number) => (
        <Tag color={progress >= 80 ? 'green' : progress >= 50 ? 'orange' : 'red'}>
          {progress}%
        </Tag>
      ),
    },
    {
      title: 'Last Activity',
      dataIndex: 'lastActivity',
      key: 'lastActivity',
      render: (date: string | null) => date ? dayjs(date).format('DD/MM/YYYY') : 'Never',
    },
    {
      title: 'Relationship Start',
      dataIndex: 'relationshipStartDate',
      key: 'relationshipStartDate',
      render: (date: string) => dayjs(date).format('DD/MM/YYYY'),
    },
  ];

  const pastMenteeColumns = [
    ...menteeColumns,
    {
      title: 'Relationship End',
      dataIndex: 'relationshipEndDate',
      key: 'relationshipEndDate',
      render: (date: string | null) => date ? dayjs(date).format('DD/MM/YYYY') : 'N/A',
    },
  ];

  if (loading) {
    return (
      <div className="text-center py-8">
        <Spin size="large" />
      </div>
    );
  }

  const tabItems = [
    {
      key: 'current',
      label: `Current Mentees (${currentMentees.length})`,
      children: (
        <Table
          columns={menteeColumns}
          dataSource={currentMentees}
          rowKey="id"
          pagination={false}
          size="small"
        />
      ),
    },
    {
      key: 'past',
      label: `Past Mentees (${pastMentees.length})`,
      children: (
        <Table
          columns={pastMenteeColumns}
          dataSource={pastMentees}
          rowKey="id"
          pagination={false}
          size="small"
        />
      ),
    },
  ];

  return (
    <Card>
      <Tabs items={tabItems} defaultActiveKey="current" />
    </Card>
  );
}
