'use client';

import { useState, useEffect, useCallback } from 'react';
import { Table, Button, App } from 'antd';
import { FileOutlined } from '@ant-design/icons';
import { Meeting, MeetingStatus, MeetingType } from '@/types';
import { viewDocument } from '@/lib/utils';

interface AdminAppraisalsTabProps {
  userId: string;
}

export default function AdminAppraisalsTab({ userId }: AdminAppraisalsTabProps) {
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [loading, setLoading] = useState(true);
  const { message } = App.useApp();
  const fetchMeetings = useCallback(async () => {
    try {
      const response = await fetch(`/api/meetings?userId=${userId}&type=${MeetingType.APPRAISAL}&status=${MeetingStatus.COMPLETED}`);
      if (!response.ok) throw new Error('Failed to fetch appraisal meetings');
      const data = await response.json();

      const filteredMeetings = data.filter((meeting: Meeting) => {
        return meeting.type === MeetingType.APPRAISAL;
      });
      
      setMeetings(filteredMeetings);
    } catch (error) {
      console.error('Error fetching appraisal meetings:', error);
      message.error('Failed to fetch appraisal meetings');
    } finally {
      setLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    fetchMeetings();
  }, [fetchMeetings]);

  const handleViewDocument = (s3Key: string) => {
    if (s3Key) {
      viewDocument(s3Key);
    } else {
      message.error('Document not found');
    }
  };

  // Appraisal meetings don't have feedback forms, only documents

  const getStatusColor = (status: string) => {
    switch (status) {
      case MeetingStatus.PENDING:
        return 'gold';
      case MeetingStatus.ACCEPTED:
        return 'green';
      case MeetingStatus.DECLINED:
        return 'red';
      case MeetingStatus.COMPLETED:
        return 'blue';
      default:
        return 'default';
    }
  };

  const appraisalsColumns = [
    {
      title: 'Mentor',
      dataIndex: ['mentor', 'name'],
      key: 'mentor',
    },
    {
      title: 'Meeting Type',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <span style={{ color: getStatusColor(status) }}>{status}</span>
      ),
    },
    {
      title: 'Meeting Time',
      dataIndex: 'proposedTime',
      key: 'proposedTime',
      render: (time: string) => new Date(time).toLocaleString(),
    },
  ];

  const documentColumns = [
    {
      title: 'Document Name',
      dataIndex: ['monthlyReview', 'documentName'],
      key: 'documentName',
    },
    {
      title: 'Completion Date',
      dataIndex: ['monthlyReview', 'completionDate'],
      key: 'completionDate',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Action',
      dataIndex: ['monthlyReview', 'action'],
      key: 'action',
    },
    {
      title: 'Document Type',
      dataIndex: ['monthlyReview', 'documentType'],
      key: 'documentType',
      render: (docType: string) => {
        return (
          <span>
            {docType === 'APPRAISAL_FORM' ? 'Appraisal Form' : ''}
          </span>
        );
      },
    },
    {
      title: 'Download Form',
      dataIndex: ['monthlyReview', 's3Key'],
      key: 's3Key',
      render: (s3Key: string) => (
        s3Key ? (
          <Button
            icon={<FileOutlined />}
            onClick={() => handleViewDocument(s3Key)}
          >
            View
          </Button>
        ) : null
      ),
    },
  ];

  const meetingsWithDocuments = meetings.filter(
    meeting => meeting.monthlyReview
  );

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-lg font-semibold mb-4">Appraisal Meetings</h2>
        <Table
          columns={appraisalsColumns}
          dataSource={meetings}
          rowKey="id"
          loading={loading}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            showTotal: (total) => `Total ${total} appraisals`,
          }}
        />
      </div>

      <div>
        <h2 className="text-lg font-semibold mb-4">Appraisal Documents</h2>
        <Table
          columns={documentColumns}
          dataSource={meetingsWithDocuments}
          rowKey="id"
          loading={loading}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            showTotal: (total) => `Total ${total} documents`,
          }}
        />
      </div>
    </div>
  );
}
