'use client';

import { useState, useEffect } from 'react';
import { Card, Table, Tabs, Typography, Tag, Space, Spin, App } from 'antd';
import { User } from '@/types';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

interface SupervisorTraineesTabProps {
  supervisorId: string;
}

interface TraineeData {
  id: string;
  name: string;
  email: string;
  qualificationRoute: string;
  traineeLevel: number;
  competencyProgress: number;
  lastActivity: string | null;
  relationshipStartDate: string;
  relationshipEndDate?: string;
  isActive: boolean;
  placement: {
    id: string;
    name: string;
    startDate: string;
    endDate: string | null;
    client: {
      name: string;
    };
  };
}

export default function SupervisorTraineesTab({ supervisorId }: SupervisorTraineesTabProps) {
  const [currentTrainees, setCurrentTrainees] = useState<TraineeData[]>([]);
  const [pastTrainees, setPastTrainees] = useState<TraineeData[]>([]);
  const [loading, setLoading] = useState(true);
  const { message } = App.useApp();

  useEffect(() => {
    fetchTrainees();
  }, [supervisorId]);

  const fetchTrainees = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/supervisors/${supervisorId}/trainees?type=all`);
      
      if (response.ok) {
        const data = await response.json();
        setCurrentTrainees(data.current || []);
        setPastTrainees(data.past || []);
      } else {
        message.error('Failed to fetch trainees');
      }
    } catch (error) {
      console.error('Error fetching trainees:', error);
      message.error('Failed to fetch trainees');
    } finally {
      setLoading(false);
    }
  };

  const traineeColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (name: string) => name || 'N/A',
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      render: (email: string) => email || 'N/A',
    },
    {
      title: 'Qualification Route',
      dataIndex: 'qualificationRoute',
      key: 'qualificationRoute',
      render: (route: string) => route || 'N/A',
    },
    {
      title: 'Level',
      dataIndex: 'traineeLevel',
      key: 'traineeLevel',
      render: (level: number) => `Level ${level || 1}`,
    },
    {
      title: 'Progress',
      dataIndex: 'competencyProgress',
      key: 'competencyProgress',
      render: (progress: number) => (
        <Tag color={progress >= 80 ? 'green' : progress >= 50 ? 'orange' : 'red'}>
          {progress}%
        </Tag>
      ),
    },
    {
      title: 'Placement',
      dataIndex: 'placement',
      key: 'placement',
      render: (placement: any) => (
        <div>
          <div className="font-medium">{placement?.name || 'N/A'}</div>
          <div className="text-sm text-gray-500">{placement?.client?.name || 'N/A'}</div>
        </div>
      ),
    },
    {
      title: 'Last Activity',
      dataIndex: 'lastActivity',
      key: 'lastActivity',
      render: (date: string | null) => date ? dayjs(date).format('DD/MM/YYYY') : 'Never',
    },
    {
      title: 'Relationship Start',
      dataIndex: 'relationshipStartDate',
      key: 'relationshipStartDate',
      render: (date: string) => dayjs(date).format('DD/MM/YYYY'),
    },
  ];

  const pastTraineeColumns = [
    ...traineeColumns,
    {
      title: 'Relationship End',
      dataIndex: 'relationshipEndDate',
      key: 'relationshipEndDate',
      render: (date: string | null) => date ? dayjs(date).format('DD/MM/YYYY') : 'N/A',
    },
  ];

  if (loading) {
    return (
      <div className="text-center py-8">
        <Spin size="large" />
      </div>
    );
  }

  const tabItems = [
    {
      key: 'current',
      label: `Current Trainees (${currentTrainees.length})`,
      children: (
        <Table
          columns={traineeColumns}
          dataSource={currentTrainees}
          rowKey="id"
          pagination={false}
          size="small"
        />
      ),
    },
    {
      key: 'past',
      label: `Past Trainees (${pastTrainees.length})`,
      children: (
        <Table
          columns={pastTraineeColumns}
          dataSource={pastTrainees}
          rowKey="id"
          pagination={false}
          size="small"
        />
      ),
    },
  ];

  return (
    <Card>
      <Tabs items={tabItems} defaultActiveKey="current" />
    </Card>
  );
}
