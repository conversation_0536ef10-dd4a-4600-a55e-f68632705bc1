'use client';

import { useState, useEffect, useCallback } from 'react';
import { Table, App, Button } from 'antd';
import { FileOutlined } from '@ant-design/icons';
import { Meeting, MeetingStatus, MeetingType } from '@/types';
import { viewDocument } from '@/lib/utils';

interface AdminMentorMeetingsTabProps {
  userId: string;
}

export default function AdminMentorMeetingsTab({ userId }: AdminMentorMeetingsTabProps) {
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [loading, setLoading] = useState(true);
  const { message } = App.useApp();
  const fetchMeetings = useCallback(async () => {
    try {
      const response = await fetch(`/api/meetings?userId=${userId}&type=${MeetingType.MONTHLY}&status=${MeetingStatus.COMPLETED}`);
      if (!response.ok) throw new Error('Failed to fetch meetings');
      const data = await response.json();
      setMeetings(data);
    } catch (error) {
      console.error('Error fetching meetings:', error);
      message.error('Failed to fetch meetings');
    } finally {
      setLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    fetchMeetings();
  }, [fetchMeetings]);

  const handleViewDocument = (s3Key: string) => {
    if (s3Key) {
      viewDocument(s3Key);
    } else {
      message.error('Document not found');
    }
  };

  const handleViewFeedbackForm = (feedbackFormId: string) => {
    if (feedbackFormId) {
      window.open(`/feedbackForm/${feedbackFormId}/edit`, '_blank');
    } else {
      message.error('Feedback form not found');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case MeetingStatus.PENDING:
        return 'gold';
      case MeetingStatus.ACCEPTED:
        return 'green';
      case MeetingStatus.DECLINED:
        return 'red';
      case MeetingStatus.COMPLETED:
        return 'blue';
      default:
        return 'default';
    }
  };

  const meetingsColumns = [
    {
      title: 'Mentor',
      dataIndex: ['mentor', 'name'],
      key: 'mentor',
    },
    {
      title: 'Meeting Type',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => type.replace('_', ' '),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <span style={{ color: getStatusColor(status) }}>{status}</span>
      ),
    },
    {
      title: 'Meeting Time',
      dataIndex: 'proposedTime',
      key: 'proposedTime',
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: 'File',
      key: 'file',
      render: (_: unknown, record: Meeting) => (
        record.monthlyReview?.s3Key ? (
          <Button
            icon={<FileOutlined />}
            onClick={() => handleViewDocument(record.monthlyReview!.s3Key!)}
          >
            View
          </Button>
        ) : null
      ),
    },
  ];

  const feedbackColumns = [
    {
      title: 'Document Name',
      dataIndex: ['monthlyReview', 'documentName'],
      key: 'documentName',
    },
    {
      title: 'Completion Date',
      dataIndex: ['monthlyReview', 'completionDate'],
      key: 'completionDate',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Action',
      dataIndex: ['monthlyReview', 'action'],
      key: 'action',
    },
    {
      title: 'Document Type',
      dataIndex: ['monthlyReview', 'documentType'],
      key: 'documentType',
    },
    {
      title: 'Download Form',
      key: 'downloadForm',
      render: (_: unknown, record: Meeting) => (
        record.monthlyReview?.feedbackForm?.id ? (
          <Button
            icon={<FileOutlined />}
            onClick={() => handleViewFeedbackForm(record.monthlyReview!.feedbackForm!.id)}
          >
            View Form
          </Button>
        ) : null
      ),
    },
  ];

  const feedbackMeetings = meetings.filter(
    meeting => meeting.type === MeetingType.MONTHLY && meeting.monthlyReview
  );

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-lg font-semibold mb-4">Mentor Meetings</h2>
        <Table
          columns={meetingsColumns}
          dataSource={meetings}
          rowKey="id"
          loading={loading}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            showTotal: (total) => `Total ${total} meetings`,
          }}
        />
      </div>

      <div>
        <h2 className="text-lg font-semibold mb-4">Mentor Feedback</h2>
        <Table
          columns={feedbackColumns}
          dataSource={feedbackMeetings}
          rowKey="id"
          loading={loading}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            showTotal: (total) => `Total ${total} feedback forms`,
          }}
        />
      </div>
    </div>
  );
} 