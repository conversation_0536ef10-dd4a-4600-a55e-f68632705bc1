import { useState, useEffect } from 'react';
import { Table, Button, App, Modal, Select } from 'antd';
import { DeleteOutlined, UserAddOutlined } from '@ant-design/icons';
import type { User } from '@/types';
import Link from 'next/link';

interface ClientSupervisorManagerProps {
  clientId: string;
  onSupervisorsChange?: (supervisors: User[]) => void;
  onUpdate?: () => Promise<void>;
}

export default function ClientSupervisorManager({
  clientId,
  onSupervisorsChange,
  onUpdate
}: ClientSupervisorManagerProps) {
  const [loading, setLoading] = useState(false);
  const [supervisors, setSupervisors] = useState<User[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [availableSupervisors, setAvailableSupervisors] = useState<User[]>([]);
  const [selectedSupervisors, setSelectedSupervisors] = useState<string[]>([]);
  const { message } = App.useApp();
  const fetchSupervisors = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/clients/${clientId}/supervisors`);
      if (response.ok) {
        const data = await response.json();
        setSupervisors(data);
        onSupervisorsChange?.(data);
      }
    } catch (error) {
      console.error('Error fetching supervisors:', error);
      message.error('Failed to load supervisors');
    } finally {
      setLoading(false);
    }
  };

  const fetchAvailableSupervisors = async () => {
    try {
      setLoading(true);
      // Fetch all supervisors
      const response = await fetch('/api/users?role=SUPERVISOR');
      if (!response.ok) throw new Error('Failed to fetch supervisors');
      const allSupervisors = await response.json();
      
      // Fetch available supervisors (not assigned to any client)
      const response2 = await fetch('/api/supervisors/available');
      if (!response2.ok) throw new Error('Failed to fetch available supervisors');
      const availableSupervisorIds = await response2.json();
      
      // Filter supervisors that are available
      const available = allSupervisors.filter((sup: User) => 
        availableSupervisorIds.includes(sup.id)
      );
      
      setAvailableSupervisors(available);
    } catch (error) {
      console.error('Error fetching available supervisors:', error);
      message.error('Failed to load available supervisors');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSupervisors();
  }, [clientId]);

  const handleRemoveSupervisor = async (supervisorId: string) => {
    try {
      const response = await fetch(`/api/clients/${clientId}/supervisors`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ supervisorIds: [supervisorId] }),
      });

      if (!response.ok) throw new Error('Failed to remove supervisor');

      message.success('Supervisor removed successfully');
      fetchSupervisors();
      if (onUpdate) await onUpdate();
    } catch (error) {
      console.error('Error removing supervisor:', error);
      message.error('Failed to remove supervisor');
    }
  };

  const handleAddClick = () => {
    fetchAvailableSupervisors();
    setSelectedSupervisors([]);
    setIsModalVisible(true);
  };

  const handleModalOk = async () => {
    if (!selectedSupervisors.length) {
      setIsModalVisible(false);
      return;
    }

    try {
      const response = await fetch(`/api/clients/${clientId}/supervisors`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ supervisorIds: selectedSupervisors }),
      });

      if (!response.ok) throw new Error('Failed to add supervisors');

      message.success('Supervisors added successfully');
      fetchSupervisors();
      if (onUpdate) await onUpdate();
      setIsModalVisible(false);
    } catch (error) {
      console.error('Error adding supervisors:', error);
      message.error('Failed to add supervisors');
    }
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: unknown, record: User) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleRemoveSupervisor(record.id)}
        />
      ),
    },
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between mb-4">
        <Button 
          type="primary"
          icon={<UserAddOutlined />}
          onClick={handleAddClick}
          style={{ backgroundColor: 'rgb(199, 100, 27)' }}
        >
          Add Supervisor
        </Button>
        <Link href="/admin/users?role=SUPERVISOR">
          <Button 
            type="link"
            icon={<UserAddOutlined />}
          >
            Create New Supervisor
          </Button>
        </Link>
      </div>

      <Table
        columns={columns}
        dataSource={supervisors}
        rowKey="id"
        loading={loading}
        pagination={false}
      />

      <Modal
        title="Add Supervisors"
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={() => setIsModalVisible(false)}
      >
        <Select
          mode="multiple"
          placeholder="Select supervisors to add"
          style={{ width: "100%", minWidth: "200px" }}
          value={selectedSupervisors}
          onChange={setSelectedSupervisors}
          optionFilterProp="label"
          options={availableSupervisors.map(sup => ({
            label: `${sup.name} (${sup.email})`,
            value: sup.id
          }))}
        />
      </Modal>
    </div>
  );
}