import { Modal, Table, Form, Select, App } from 'antd';
import { useState } from 'react';
import type { Client, Placement } from '@/types';

interface MovePlacementsModalProps {
  sourceClient: Client;
  placements: Placement[];
  availableClients: Client[];
  onClose: () => void;
  onSuccess: () => void;
}

export default function MovePlacementsModal({
  sourceClient,
  placements,
  availableClients,
  onClose,
  onSuccess
}: MovePlacementsModalProps) {
  const [loading, setLoading] = useState(false);
  const [selectedClientId, setSelectedClientId] = useState<string>();
  const { message } = App.useApp();
  const handleMovePlacements = async () => {
    if (!selectedClientId) {
      message.error('Please select a target client');
      return;
    }

    try {
      setLoading(true);
      
      const movePromises = placements.map(async (placement) => {
        const response = await fetch(`/api/placements/${placement.id}/move`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ newClientId: selectedClientId }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Failed to move placement ${placement.name}`);
        }

        return placement;
      });

      const results = await Promise.allSettled(movePromises);
      
      // Check for any failures
      const failures = results.filter((result): result is PromiseRejectedResult => 
        result.status === 'rejected'
      );
      
      const successes = results.filter((result): result is PromiseFulfilledResult<Placement> => 
        result.status === 'fulfilled'
      );

      if (failures.length > 0) {
        // Some placements failed to move
        const failureMessages = failures.map(failure => failure.reason.message);
        message.error(
          <>
            <div>Failed to move some placements:</div>
            <ul>
              {failureMessages.map((msg, idx) => (
                <li key={idx}>{msg}</li>
              ))}
            </ul>
          </>
        );
      }

      if (successes.length > 0) {
        // Some placements were moved successfully
        message.success(
          `Successfully moved ${successes.length} placement(s) from ${sourceClient.name}`
        );
        onSuccess();
      }

      if (successes.length === placements.length) {
        // All placements were moved successfully
        onClose();
      }
    } catch (error) {
      console.error('Error moving placements:', error);
      message.error('Failed to move placements');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string | Date | null) => {
    if (!dateString) return '';
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    return date.toLocaleDateString();
  };

  return (
    <Modal
      title={`Move Placements from ${sourceClient.name}`}
      open={true}
      onCancel={onClose}
      okText="Move Placements"
      cancelText="Cancel"
      onOk={handleMovePlacements}
      width={600}
      confirmLoading={loading}
      okButtonProps={{ 
        disabled: !selectedClientId,
        style: { backgroundColor: 'rgb(199, 100, 27)' }
      }}
    >
      <div>
        <p>Select a client to move the following placements to:</p>
        <Table
          dataSource={placements}
          rowKey="id"
          columns={[
            {
              title: 'Name',
              dataIndex: 'name',
              key: 'name',
            },
            {
              title: 'Start Date',
              dataIndex: 'startDate',
              key: 'startDate',
              render: (date: string | Date | null) => formatDate(date),
            },
            {
              title: 'Supervisor',
              dataIndex: 'supervisorName',
              key: 'supervisorName',
            }
          ]}
          pagination={false}
          size="small"
          style={{ marginBottom: '16px' }}
        />
        <Form layout="vertical">
          <Form.Item
            label="Select Target Client"
            required
            style={{ marginBottom: 0 }}
          >
            <Select
              placeholder="Select a client"
              style={{ width: '100%' }}
              onChange={setSelectedClientId}
              value={selectedClientId}
            >
              {availableClients.map(client => (
                <Select.Option key={client.id} value={client.id}>
                  {client.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
} 