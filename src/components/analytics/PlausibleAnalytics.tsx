'use client';

import { useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { UserRole } from '@/types';

declare global {
  interface Window {
    plausible: (event: string, options?: { props?: Record<string, string | number | boolean> }) => void;
  }
}

export default function PlausibleAnalytics() {
  const { data: session } = useSession();
  const isTrainee = session?.user?.role === UserRole.TRAINEE;

  useEffect(() => {
    if (isTrainee) {
      const script = document.createElement('script');
      script.defer = true;
      
      script.dataset.domain = 'pathways-new.vercel.app';
      
      const hostname = window.location.hostname;
      const isLocalhost = hostname === 'localhost' || hostname === '127.0.0.1';
      
      if (isLocalhost) {
        script.setAttribute('data-local', 'true');
        script.setAttribute('data-track-localhost', 'true');
      }
      
      script.src = 'https://cd-analytics.up.railway.app/js/script.js';
      
      script.onload = () => {
        console.log('✅ Plausible Analytics script loaded successfully');
        if (typeof window.plausible === 'function') {
          console.log('✅ Plausible function is available');
          window.plausible('Test Event', { props: { test: 'test' } });
          console.log('✅ Test event sent to Plausible');
        } else {
          console.error('❌ Plausible function is not available');
        }
      };
      
      script.onerror = () => {
        console.error('❌ Failed to load Plausible Analytics script');
      };
      
      document.head.appendChild(script);
      
      console.log(`Plausible Analytics initialized for domain: ${script.dataset.domain}`);

      return () => {
        document.head.removeChild(script);
      };
    }
  }, [isTrainee]);

  const trackPageView = useCallback((pageName: string) => {
    if (isTrainee && window.plausible) {
      window.plausible('Page View', { props: { page: pageName } });
    }
  }, [isTrainee]);

  const trackButtonClick = useCallback((buttonName: string) => {
    if (isTrainee && window.plausible) {
      window.plausible('Button Click', { props: { button: buttonName } });
    }
  }, [isTrainee]);

  const trackMenuClick = useCallback((menuItem: string) => {
    if (isTrainee && window.plausible) {
      window.plausible('MenuBar Click', { props: { item: menuItem } });
    }
  }, [isTrainee]);

  const trackCtaClick = useCallback((ctaName: string) => {
    if (isTrainee && window.plausible) {
      window.plausible('Cta Links Click', { props: { cta: ctaName } });
    }
  }, [isTrainee]);

  const trackTextSelection = useCallback((textName: string) => {
    if (isTrainee && window.plausible) {
      window.plausible('Text Selected', { props: { text: textName } });
    }
  }, [isTrainee]);

  const trackTitleSelection = useCallback((titleName: string) => {
    if (isTrainee && window.plausible) {
      window.plausible('Title Selected', { props: { title: titleName } });
    }
  }, [isTrainee]);

  useEffect(() => {
    if (isTrainee) {
      window.pathwaysAnalytics = {
        trackPageView,
        trackButtonClick,
        trackMenuClick,
        trackCtaClick,
        trackTextSelection,
        trackTitleSelection
      };
    }
  }, [isTrainee, trackPageView, trackButtonClick, trackMenuClick, trackCtaClick, trackTextSelection, trackTitleSelection]);

  return null;
}

declare global {
  interface Window {
    pathwaysAnalytics?: {
      trackPageView: (pageName: string) => void;
      trackButtonClick: (buttonName: string) => void;
      trackMenuClick: (menuItem: string) => void;
      trackCtaClick: (ctaName: string) => void;
      trackTextSelection: (textName: string) => void;
      trackTitleSelection: (titleName: string) => void;
    };
  }
}
