'use client';

import React from 'react';
import Link from 'next/link';
import { useAnalytics } from '@/hooks/useAnalytics';

interface AnalyticsLinkProps {
  href: string;
  analyticsLabel: string;
  className?: string;
  children: React.ReactNode;
  onClick?: (e: React.MouseEvent<HTMLAnchorElement>) => void;
}

/**
 * A link component that automatically tracks clicks for analytics
 * Only tracks for trainee users due to the useAnalytics hook implementation
 */
export default function AnalyticsLink({
  href,
  analyticsLabel,
  className,
  children,
  onClick,
}: AnalyticsLinkProps) {
  const { trackCtaClick } = useAnalytics();

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    // Track the CTA click
    trackCtaClick(analyticsLabel);
    
    // Call the original onClick handler if provided
    if (onClick) {
      onClick(e);
    }
  };

  return (
    <Link href={href} className={className} onClick={handleClick}>
      {children}
    </Link>
  );
}
