'use client';

import React from 'react';
import { Button as AntButton } from 'antd';
import { ButtonProps } from 'antd/es/button';
import { useAnalytics } from '@/hooks/useAnalytics';

interface AnalyticsButtonProps extends ButtonProps {
  analyticsLabel: string;
}

/**
 * A button component that automatically tracks clicks for analytics
 * Only tracks for trainee users due to the useAnalytics hook implementation
 */
export default function AnalyticsButton({
  analyticsLabel,
  onClick,
  children,
  ...props
}: AnalyticsButtonProps) {
  const { trackButtonClick } = useAnalytics();

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    // Track the button click
    trackButtonClick(analyticsLabel);
    
    // Call the original onClick handler if provided
    if (onClick) {
      onClick(e);
    }
  };

  return (
    <AntButton onClick={handleClick} {...props}>
      {children}
    </AntButton>
  );
}
