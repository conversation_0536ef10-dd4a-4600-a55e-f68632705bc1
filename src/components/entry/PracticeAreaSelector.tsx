"use client";

import { Select } from "antd";
import type { SelectProps } from "antd";
import { useMemo } from "react";
import { practiceAreaOptions } from "@/helpers/practiceAreas";

const { Option } = Select;

interface PracticeAreaSelectorProps {
  value?: string[] | number[];
  onChange?: (value: string[]) => void;
  disabled?: boolean;
}

export default function PracticeAreaSelector({
  value = [],
  onChange,
  disabled = false,
}: PracticeAreaSelectorProps) {
  // Convert number IDs to strings if needed
  const normalizedValue = useMemo(() => 
    value.map(v => typeof v === 'number' ? v.toString() : v),
    [value]
  );
  
  const handleChange: SelectProps["onChange"] = (selectedValues) => {
    if (onChange) {
      const values = Array.isArray(selectedValues) ? selectedValues : [];
      // Convert all values to strings
      onChange(values.map(v => v.toString()));
    }
  };

  return (
    <div className="mb-2">
      <Select
        mode="multiple"
        style={{ width: "100%" }}
        placeholder="Select practice areas"
        value={normalizedValue}
        onChange={handleChange}
        optionFilterProp="children"
        disabled={disabled}
        optionLabelProp="label"
      >
        {practiceAreaOptions.map((area) => (
          <Option 
            key={area.id} 
            value={area.id.toString()} 
            label={area.title}
          >
            {area.title}
          </Option>
        ))}
      </Select>
    </div>
  );
}
