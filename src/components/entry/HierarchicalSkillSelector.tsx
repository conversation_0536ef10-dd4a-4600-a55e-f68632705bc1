'use client';

import { useState, useEffect } from 'react';
import {
  Spin,
  Typography,
  App,
  Dropdown,
  Button,
  Space,
  Checkbox,
  Card,
  Tag,
  Input,
  Badge,
  Collapse,
  Tooltip
} from 'antd';
import { DownOutlined, SearchOutlined, CaretRightOutlined } from '@ant-design/icons';
import { useSession } from 'next-auth/react';

const { Text, Title } = Typography;
const { Panel } = Collapse;

interface SubSkill {
  id: string;
  name: string;
  practiceSubSkillType: 'sqe' | 'tc' | 'both';
  minSuggestedEntryCount: number;
  order: number;
}

interface SkillGroup {
  id: string;
  name: string;
  practiceSubSkills: SubSkill[];
}

interface PracticeSkill {
  id: string;
  name: string;
  practiceSkillGroups: SkillGroup[];
}

interface HierarchicalSkillSelectorProps {
  value?: string[];
  onChange?: (value: string[]) => void;
  disabled?: boolean;
  qualificationRoute?: string;
}

export default function HierarchicalSkillSelector({
  value = [],
  onChange,
  disabled = false,
  qualificationRoute
}: HierarchicalSkillSelectorProps) {
  const { data: session } = useSession();
  const [skillData, setSkillData] = useState<PracticeSkill[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [selectedCount, setSelectedCount] = useState(0);
  const [userQualificationRoute, setUserQualificationRoute] = useState<string>('');
  const { message } = App.useApp();
  
  useEffect(() => {
    fetchSkills();
    
    if (qualificationRoute) {
      setUserQualificationRoute(qualificationRoute);
    } else if (session?.user?.qualificationRoute) {
      setUserQualificationRoute(session.user.qualificationRoute);
    }
  }, [qualificationRoute, session]);

  useEffect(() => {
    setSelectedCount(value.length);
  }, [value]);

  const fetchSkills = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/skills');
      if (response.ok) {
        const data = await response.json();
        setSkillData(data);
      } else {
        message.error('Failed to fetch skills');
      }
    } catch (error) {
      console.error('Error fetching skills:', error);
      message.error('Failed to fetch skills');
    } finally {
      setLoading(false);
    }
  };

  const isTCRoute = () => {
    return userQualificationRoute.toLowerCase() === 'tc';
  };

  const handleSubSkillSelect = (subSkillId: string, checked: boolean) => {
    let newSelectedSubSkills: string[];

    if (checked) {
      newSelectedSubSkills = [...value, subSkillId];
    } else {
      newSelectedSubSkills = value.filter((id: string) => id !== subSkillId);
    }

    if (onChange) {
      onChange(newSelectedSubSkills);
    }
  };

  const isSubSkillSelected = (subSkillId: string) => {
    return value.includes(subSkillId);
  };

  const isSkillGroupSelected = (skillGroup: SkillGroup) => {
    return skillGroup.practiceSubSkills.some(subSkill =>
      value.includes(subSkill.id)
    );
  };

  const isPracticeSkillSelected = (practiceSkill: PracticeSkill) => {
    return practiceSkill.practiceSkillGroups.some(skillGroup =>
      isSkillGroupSelected(skillGroup)
    );
  };

  const getSkillGroupSelectedCount = (skillGroup: SkillGroup) => {
    return skillGroup.practiceSubSkills.filter(subSkill =>
      value.includes(subSkill.id)
    ).length;
  };

  const getPracticeSkillSelectedCount = (practiceSkill: PracticeSkill) => {
    return practiceSkill.practiceSkillGroups.reduce(
      (count, skillGroup) => count + getSkillGroupSelectedCount(skillGroup),
      0
    );
  };

  const getAllSubSkillsForPracticeSkill = (practiceSkill: PracticeSkill) => {
    return practiceSkill.practiceSkillGroups.flatMap(group => 
      group.practiceSubSkills.filter(filterByQualificationRoute)
    );
  };

  const filterBySearch = (item: PracticeSkill | SkillGroup | SubSkill) => {
    if (!searchText) return true;
    return item.name.toLowerCase().includes(searchText.toLowerCase());
  };

  const filterByQualificationRoute = (subSkill: SubSkill) => {
    if (!userQualificationRoute) return true;
    const route = userQualificationRoute.toLowerCase();
    const type = subSkill.practiceSubSkillType.toLowerCase();
    return type === 'both' || type === route;
  };

  function getFilteredSkillData() {
    return skillData
      .map(practiceSkill => {
        const filteredGroups = practiceSkill.practiceSkillGroups
          .map(skillGroup => {
            const filteredSubSkills = skillGroup.practiceSubSkills.filter(filterByQualificationRoute);
            if (filteredSubSkills.length === 0) return null;
            return { ...skillGroup, practiceSubSkills: filteredSubSkills };
          })
          .filter(Boolean) as SkillGroup[];
        if (filteredGroups.length === 0) return null;
        return { ...practiceSkill, practiceSkillGroups: filteredGroups };
      })
      .filter(Boolean) as PracticeSkill[];
  }

  const renderSelectedSkills = () => {
    const filteredSkillData = getFilteredSkillData();
    if (!filteredSkillData) return null;
    const selectedPracticeSkills = filteredSkillData.filter(isPracticeSkillSelected);
    
    if (selectedPracticeSkills.length === 0) {
      return <Text type="secondary">No skills selected</Text>;
    }

    if (isTCRoute()) {
      return (
        <div className="space-y-4">
          <Collapse
            ghost
            expandIcon={({ isActive }) => (
              <CaretRightOutlined rotate={isActive ? 90 : 0} style={{ fontSize: '12px' }} />
            )}
          >
            {selectedPracticeSkills.map(practiceSkill => {
              const selectedSubSkills = getAllSubSkillsForPracticeSkill(practiceSkill)
                .filter(subSkill => isSubSkillSelected(subSkill.id));
              const skillCount = selectedSubSkills.length;
              
              return (
                <Panel
                  key={practiceSkill.id}
                  header={
                    <Space>
                      <Text strong>{practiceSkill.name}</Text>
                      <Badge count={skillCount} style={{ backgroundColor: '#1890ff' }} />
                    </Space>
                  }
                >
                  <div className="ml-4 flex flex-wrap gap-2">
                    {selectedSubSkills.map(subSkill => (
                      <Tooltip 
                        key={subSkill.id} 
                        title={
                          <div>
                            <div>Type: {subSkill.practiceSubSkillType.toUpperCase()}</div>
                            <div>Min Entries: {subSkill.minSuggestedEntryCount}</div>
                            <div>Order: {subSkill.order}</div>
                          </div>
                        }
                      >
                        <Tag
                          closable={!disabled}
                          onClose={() => handleSubSkillSelect(subSkill.id, false)}
                          color="blue"
                          style={{ 
                            maxWidth: '100%', 
                            overflow: 'hidden', 
                            textOverflow: 'ellipsis',
                            whiteSpace: 'normal',
                            wordBreak: 'break-word'
                          }}
                        >
                          {subSkill.name}
                        </Tag>
                      </Tooltip>
                    ))}
                  </div>
                </Panel>
              );
            })}
          </Collapse>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        <Collapse
          ghost
          expandIcon={({ isActive }) => (
            <CaretRightOutlined rotate={isActive ? 90 : 0} style={{ fontSize: '12px' }} />
          )}
        >
          {selectedPracticeSkills.map(practiceSkill => {
            const selectedSkillGroups = practiceSkill.practiceSkillGroups.filter(isSkillGroupSelected);
            const skillCount = getPracticeSkillSelectedCount(practiceSkill);
            return (
              <Panel
                key={practiceSkill.id}
                header={
                  <Space>
                    <Text strong>{practiceSkill.name}</Text>
                    <Badge count={skillCount} style={{ backgroundColor: '#1890ff' }} />
                  </Space>
                }
              >
                <div className="space-y-4">
                  {selectedSkillGroups.map(skillGroup => {
                    const selectedSubSkills = skillGroup.practiceSubSkills.filter(subSkill =>
                      isSubSkillSelected(subSkill.id) && filterByQualificationRoute(subSkill)
                    );
                    const groupCount = getSkillGroupSelectedCount(skillGroup);
                    return (
                      <div key={skillGroup.id} className="ml-4">
                        <Collapse
                          ghost
                          expandIcon={({ isActive }) => (
                            <CaretRightOutlined rotate={isActive ? 90 : 0} style={{ fontSize: '12px' }} />
                          )}
                        >
                          <Panel
                            key={skillGroup.id}
                            header={
                              <Space>
                                <Text>{skillGroup.name}</Text>
                                <Badge count={groupCount} style={{ backgroundColor: '#52c41a' }} />
                              </Space>
                            }
                          >
                            <div className="ml-4 flex flex-wrap gap-2">
                              {selectedSubSkills.map(subSkill => (
                                <Tooltip 
                                  key={subSkill.id} 
                                  title={
                                    <div>
                                      <div>Type: {subSkill.practiceSubSkillType.toUpperCase()}</div>
                                      <div>Min Entries: {subSkill.minSuggestedEntryCount}</div>
                                      <div>Order: {subSkill.order}</div>
                                    </div>
                                  }
                                >
                                  <Tag
                                    closable={!disabled}
                                    onClose={() => handleSubSkillSelect(subSkill.id, false)}
                                    color="blue"
                                    style={{ 
                                      maxWidth: '100%', 
                                      overflow: 'hidden', 
                                      textOverflow: 'ellipsis',
                                      whiteSpace: 'normal',
                                      wordBreak: 'break-word'
                                    }}
                                  >
                                    {subSkill.name}
                                  </Tag>
                                </Tooltip>
                              ))}
                            </div>
                          </Panel>
                        </Collapse>
                      </div>
                    );
                  })}
                </div>
              </Panel>
            );
          })}
        </Collapse>
      </div>
    );
  };

  const dropdownContent = (
    <Card className='w-[calc(100%-1rem)] h-full max-h-[600px] min-h-[600px] mr-5 overflow-auto max-w-[800px]'>
      <div className="mb-4">
        <Input
          placeholder="Search skills..."
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={e => setSearchText(e.target.value)}
          allowClear
        />
      </div>
      <div className="space-y-4">
        <Collapse
          ghost
          expandIcon={({ isActive }) => (
            <CaretRightOutlined rotate={isActive ? 90 : 0} style={{ fontSize: '12px' }} />
          )}
        >
          {getFilteredSkillData()
            .filter(filterBySearch)
            .sort((a, b) => a.name.localeCompare(b.name))
            .map(practiceSkill => {
              const skillCount = getPracticeSkillSelectedCount(practiceSkill);
              return (
                <Panel
                  key={practiceSkill.id}
                  header={
                    <Space align="center">
                      <Title level={5} style={{ margin: 0 }}>{practiceSkill.name}</Title>
                      {skillCount > 0 && (
                        <Badge count={skillCount} style={{ backgroundColor: '#1890ff' }} />
                      )}
                    </Space>
                  }
                >
                  {isTCRoute() ? (
                    <div className="ml-4 space-y-2">
                      {getAllSubSkillsForPracticeSkill(practiceSkill)
                        .filter(filterBySearch)
                        .sort((a, b) => a.order - b.order)
                        .map(subSkill => (
                          <div key={subSkill.id} className="flex items-center gap-2">
                            <Checkbox
                              checked={isSubSkillSelected(subSkill.id)}
                              onChange={e => handleSubSkillSelect(subSkill.id, e.target.checked)}
                              disabled={disabled}
                            >
                              {subSkill.name}
                            </Checkbox>
                            <Space>
                              <Tag color="cyan">Min: {subSkill.minSuggestedEntryCount}</Tag>
                              <Tag color={subSkill.practiceSubSkillType === 'both' ? 'purple' : (subSkill.practiceSubSkillType === 'tc' ? 'blue' : 'orange')}>
                                {subSkill.practiceSubSkillType.toUpperCase()}
                              </Tag>
                            </Space>
                          </div>
                        ))}
                    </div>
                  ) : (
                    practiceSkill.practiceSkillGroups.filter(filterBySearch).map(skillGroup => {
                      const groupCount = getSkillGroupSelectedCount(skillGroup);
                      const filteredSubSkills = skillGroup.practiceSubSkills.filter(subSkill => 
                        filterBySearch(subSkill) && filterByQualificationRoute(subSkill)
                      );
                      if (filteredSubSkills.length === 0) return null;
                      return (
                        <div key={skillGroup.id} className="ml-4 mb-4">
                          <Collapse
                            ghost
                            expandIcon={({ isActive }) => (
                              <CaretRightOutlined rotate={isActive ? 90 : 0} style={{ fontSize: '12px' }} />
                            )}
                          >
                            <Panel
                              key={skillGroup.id}
                              header={
                                <Space align="center">
                                  <Text strong>{skillGroup.name}</Text>
                                  {groupCount > 0 && (
                                    <Badge count={groupCount} style={{ backgroundColor: '#52c41a' }} />
                                  )}
                                </Space>
                              }
                            >
                              <div className="ml-4 space-y-2">
                                {filteredSubSkills.map(subSkill => (
                                  <div key={subSkill.id} className="flex items-center gap-2">
                                    <Checkbox
                                      checked={isSubSkillSelected(subSkill.id)}
                                      onChange={e => handleSubSkillSelect(subSkill.id, e.target.checked)}
                                      disabled={disabled}
                                    >
                                      {subSkill.name}
                                    </Checkbox>
                                    <Space>
                                      <Tag color="cyan">Min: {subSkill.minSuggestedEntryCount}</Tag>
                                      <Tag color={subSkill.practiceSubSkillType === 'both' ? 'purple' : (subSkill.practiceSubSkillType === 'tc' ? 'blue' : 'orange')}>
                                        {subSkill.practiceSubSkillType.toUpperCase()}
                                      </Tag>
                                    </Space>
                                  </div>
                                ))}
                              </div>
                            </Panel>
                          </Collapse>
                        </div>
                      );
                    })
                  )}
                </Panel>
              );
            })}
        </Collapse>
      </div>
    </Card>
  );

  return (
    <Spin spinning={loading}>
      <div className="mb-2">
        <Space>
          {selectedCount > 0 && (
            <Badge count={selectedCount} style={{ backgroundColor: '#1890ff' }} />
          )}
        </Space>
      </div>

      <div className="space-y-4">
        <Dropdown
          open={dropdownVisible}
          onOpenChange={setDropdownVisible}
          trigger={['click']}
          dropdownRender={() => dropdownContent}
          disabled={disabled}
        >
          <Button>
            <Space>
              Select Skills
              <DownOutlined />
            </Space>
          </Button>
        </Dropdown>

        <div className="border rounded-md p-4 min-h-[100px] bg-gray-50">
          {renderSelectedSkills()}
        </div>
      </div>
    </Spin>
  );
}

