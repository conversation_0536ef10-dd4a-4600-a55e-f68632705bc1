'use client';

import { useState, useEffect, useRef } from 'react';
import {
  Form,
  Input,
  Button,
  Select,
  App,
  Spin,
  Card,
  DatePicker,
  Checkbox,
} from 'antd';
import { UploadOutlined, SaveOutlined, DeleteOutlined } from '@ant-design/icons';
import type { Placement, PracticeArea, EntrySubSkill } from '@/types';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import dayjs from 'dayjs';
import PracticeAreaSelector from './PracticeAreaSelector';
import HierarchicalSkillSelector from './HierarchicalSkillSelector';

const { TextArea } = Input;
const { Option } = Select;
interface EntryFormProps {
  entryId?: string;
  placementId?: string;
  onSuccess?: () => void;
}
export default function EntryForm({ entryId, placementId, onSuccess }: EntryFormProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [placements, setPlacements] = useState<Placement[]>([]);
  const [selectedPlacementId, setSelectedPlacementId] = useState<string | undefined>(placementId);
  const [loadingPlacements, setLoadingPlacements] = useState(false);
  const [documentKey, setDocumentKey] = useState<string | null>(null);
  const [documentName, setDocumentName] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [qualificationRoute, setQualificationRoute] = useState<string | undefined>(undefined);
  const [confidentialityConfirmed, setConfidentialityConfirmed] = useState(false);

  // Check user qualification route
  useEffect(() => {
    if (session?.user?.qualificationRoute) {
      setQualificationRoute(session.user.qualificationRoute);
    } else {
      const checkQualificationRoute = async () => {
        try {
          const response = await fetch('/api/users/me');
          if (response.ok) {
            const userData = await response.json();
            if (userData.qualificationRoute) {
              setQualificationRoute(userData.qualificationRoute);
            }
          }
        } catch (error) {
          console.error('Error checking qualification route:', error);
        }
      };
      
      checkQualificationRoute();
    }
  }, [session]);

  // Fetch placements
  useEffect(() => {
    const fetchPlacements = async () => {
      try {
        setLoadingPlacements(true);
        const response = await fetch('/api/placements?forEntries=true');
        if (response.ok) {
          const data = await response.json();
          setPlacements(data.placements.edges);

          if (placementId && !entryId) {
            form.setFieldsValue({ placementId });
            setSelectedPlacementId(placementId);
          }
        } else {
          message.error('Failed to fetch placements');
        }
      } catch (error) {
        console.error('Error fetching placements:', error);
        message.error('Failed to fetch placements');
      } finally {
        setLoadingPlacements(false);
      }
    };

    fetchPlacements();
  }, [placementId, entryId, form]);

  // Fetch entry if editing
  useEffect(() => {
    const fetchEntry = async () => {
      if (!entryId) return;

      try {
        setLoading(true);
        const response = await fetch(`/api/entries/${entryId}`);
        if (response.ok) {
          const entry = await response.json();

          const formData = {
            ...entry,
            startDate: dayjs(entry.startDate),
            endDate: dayjs(entry.endDate),
            skills: entry.entrySubSkills.map((skill: EntrySubSkill) => skill.subSkillId),
            practiceAreas: entry.practiceAreas,
            confidentialityConfirmed: entry.confidentialityConfirmed || false
          };

          form.setFieldsValue(formData);
          setSelectedPlacementId(entry.placementId);

          if (entry.documentKey) {
            setDocumentKey(entry.documentKey);
            setDocumentName(entry.documentName || 'Document');
            setSelectedFile(new File([], entry.documentName || 'Document'));
          }
        } else {
          message.error('Failed to fetch entry');
        }
      } catch (error) {
        console.error('Error fetching entry:', error);
        message.error('Failed to fetch entry');
      } finally {
        setLoading(false);
      }
    };

    if (entryId) {
      fetchEntry();
    }
  }, [entryId, form]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      
      const isValidType = [
        'application/pdf',
        'image/jpeg',
        'image/png',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ].includes(file.type);

      if (!isValidType) {
        message.error('You can only upload PDF, JPG, PNG, DOC, or DOCX files!');
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        return;
      }

      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('File must be smaller than 10MB!');
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        return;
      }

      setSelectedFile(file);
      setDocumentName(file.name);
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    setDocumentName(null);
    setDocumentKey(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSubmit = async (values: Record<string, unknown>) => {
    try {
      setSubmitting(true);

      if (!values.placementId && !selectedPlacementId) {
        message.error('Please select a placement before saving');
        setSubmitting(false);
        return;
      }

      let entryDocumentKey = documentKey;
      let entryDocumentName = documentName;

      if (selectedFile && !documentKey) {
        if (!confidentialityConfirmed) {
          message.error('Please confirm the document contains no confidential information');
          setSubmitting(false);
          return;
        }

        message.loading('Uploading document...', 0);
        
        const uploadResult = await uploadFile(selectedFile);
        
        message.destroy();
        
        if (uploadResult) {
          entryDocumentKey = uploadResult.key;
          entryDocumentName = uploadResult.name;
          message.success('Document uploaded successfully');
        } else {
          const continueWithoutFile = window.confirm('Failed to upload document. Do you want to continue without the document?');
          if (!continueWithoutFile) {
            setSubmitting(false);
            return;
          }
        }
      }

      const validPracticeAreas = Array.isArray(values.practiceAreas) 
        ? values.practiceAreas.filter((area: PracticeArea) => area !== null)
        : [];

      const entryData = {
        title: values.title,
        startDate: (values.startDate as dayjs.Dayjs).toISOString(),
        endDate: (values.endDate as dayjs.Dayjs).toISOString(),
        experience: values.experience,
        learnt: values.learnt,
        needMoreExperience: values.needMoreExperience,
        contentiousType: values.contentiousType || 'non_contentious',
        placementId: values.placementId || selectedPlacementId,
        taskedBy: values.taskedBy,
        documentKey: entryDocumentKey,
        documentName: entryDocumentName,
        practiceAreas: validPracticeAreas,
        practiceSubSkillIds: values.skills,
      };

      message.loading('Saving entry...', 0);

      const url = entryId ? `/api/entries/${entryId}` : '/api/entries';
      const method = entryId ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(entryData),
      });

      message.destroy();

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save entry');
      }

      message.success(`Entry ${entryId ? 'updated' : 'created'} successfully`);
      
      form.resetFields();
      setSelectedFile(null);
      setDocumentKey(null);
      setDocumentName(null);

      if (onSuccess) {
        onSuccess();
      } else {
        router.push('/trainee/training?tab=portfolio');
      }
    } catch (error) {
      console.error('Error saving entry:', error);
      message.error(error instanceof Error ? error.message : 'Failed to save entry');
    } finally {
      setSubmitting(false);
    }
  };

  const uploadFile = async (file: File): Promise<{key: string, name: string} | null> => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('entityType', 'entry');
      formData.append('entityId', entryId || 'new');
      formData.append('isPublic', 'false');

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to upload file');
      }

      const data = await response.json();
      return {
        key: data.file.s3Key,
        name: data.file.name
      };
    } catch (error) {
      console.error('Error uploading file:', error);
      message.error('Failed to upload document');
      return null;
    }
  };

  return (
    <Card title={entryId ? "Edit Entry" : "Create New Entry"} className="mb-6">
      <Spin spinning={loading || loadingPlacements}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            placementId: selectedPlacementId,
            contentiousType: 'non_contentious',
            startDate: dayjs(),
            endDate: dayjs(),
            confidentialityConfirmed: false
          }}
        >
          {/* Entry Title */}
          <div className="grid grid-cols-1 gap-4">
            <Form.Item
              name="title"
              label="Entry Title"
              rules={[{ required: true, message: 'Please enter a title' }]}
            >
              <Input placeholder="The title of the task. e.g. Managing conditions precedent list on Project Pathways" />
            </Form.Item>
          </div>

          {/* Tasked By */}
          <div className="grid grid-cols-1 gap-4">
            <Form.Item
              name="taskedBy"
              label="Tasked By"
            >
              <Input placeholder="The name of colleague who gave you the task. e.g. John Smith" />
            </Form.Item>
          </div>

          {/* Placement Organisation section */}
          <div className="grid grid-cols-1 gap-4">
            <h3>Placement Organisation</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Placement */}
              <Form.Item
                name="placementId"
                label="Placement"
                rules={[{ required: true, message: 'Please select a placement' }]}
              >
                <Select
                  placeholder="Select a placement"
                  loading={loadingPlacements}
                  disabled={!!placementId}
                  onChange={(value) => setSelectedPlacementId(value)}
                  optionLabelProp="label"
                  className="w-full"
                >
                  {placements.map((placement) => (
                    <Option 
                      key={placement.id} 
                      value={placement.id}
                      label={placement.client.name + ' - ' + dayjs(placement.startDate).format('DD/MM/YYYY') + ' - ' + (placement.endDate ? dayjs(placement.endDate).format('DD/MM/YYYY') : 'Present')}
                    >
                      <div className="flex flex-col min-w-0">
                        <div className="font-medium truncate">{placement.client.name}</div>
                        <div className="text-xs text-gray-500 whitespace-nowrap">
                          {dayjs(placement.startDate).format('DD/MM/YYYY')} - 
                          {placement.endDate ? dayjs(placement.endDate).format('DD/MM/YYYY') : 'Present'}
                        </div>
                      </div>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
              
              {/* Matter Type */}
              <Form.Item
                name="contentiousType"
                label="Contentious Type"
                rules={[{ required: true, message: 'Please select a matter type' }]}
              >
                <Select>
                  <Option value="non_contentious">Non-Contentious</Option>
                  <Option value="contentious">Contentious</Option>
                  <Option value="both">Both</Option>
                </Select>
              </Form.Item>

              {/* Practice Area */}
              <Form.Item
                name="practiceAreas"
                label="Practice Area"
                rules={[{
                  required: true,
                  message: 'Please select at least one practice area',
                  type: 'array',
                  min: 1,
                }]}
              >
                <PracticeAreaSelector />
              </Form.Item>
            </div>
          </div>

          {/* Start Date/End Date */}
          <div className="grid grid-cols-1 gap-4">
            <h3>Start Date/End Date</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Form.Item
                name="startDate"
                label="Start Date"
                rules={[{ required: true, message: 'Please select a start date' }]}
              >
                <DatePicker className="w-full" format="DD/MM/YYYY" />
              </Form.Item>

              <Form.Item
                name="endDate"
                label="End Date"
                rules={[{ required: true, message: 'Please select an end date' }]}
              >
                <DatePicker className="w-full" format="DD/MM/YYYY" />
              </Form.Item>
            </div>
          </div>

          {/* Details of the task/work completed */}
          <Form.Item
            name="experience"
            label="Details of the task/work completed"
            rules={[{ required: true, message: 'Please describe the task and work completed' }]}
          >
            <TextArea rows={4} placeholder="Use this box to describe the task you were asked to do and what you actually did." />
          </Form.Item>

          {/* Reflections/What did I learn? */}
          <Form.Item
            name="learnt"
            label="Reflections/What did I learn?"
            rules={[{ required: true, message: 'Please describe what you learned' }]}
          >
            <TextArea rows={4} placeholder="Reflections are extremely important. Stop and think about what you learnt. What did you gain from the experience that you would not have gained in a classroom." />
          </Form.Item>

          {/* How can I improve? */}
          <Form.Item
            name="needMoreExperience"
            label="How can I improve?"
            rules={[{ required: true, message: 'Please describe how you can improve' }]}
          >
            <TextArea rows={4} placeholder="Some of the most valuable experiences are when we learn from our mistakes. Consider what you could have done differently/better and how you can aim to do that next time." />
          </Form.Item>

          {/* Competency/Skills */}
          <Form.Item
            name="skills"
            label="Competency/Skills"
            rules={[{ required: true, message: 'Please select at least one skill' }]}
          >
            <HierarchicalSkillSelector qualificationRoute={qualificationRoute} />
          </Form.Item>

          <Form.Item
            name="file"
          >
            <div>
              <div className="mt-2 mb-4 text-gray-500 text-sm">
                You can add a document to this entry. However, you should only upload work that is predominantly your own, such as a letter of advice or a legal research note you have prepared. You should not add things like precedent documents where you have not been responsible for putting the document together but have been involved in amending it.
              </div>
              
              <div className="mb-4">
                <Checkbox
                  checked={confidentialityConfirmed}
                  onChange={(e) => setConfidentialityConfirmed(e.target.checked)}
                >
                  Tick this box to confirm that the document you wish to upload contains no confidential information or that any confidential information has been appropriately redacted.
                </Checkbox>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                  id="document-upload"
                  className="hidden"
                />
                <Button 
                  icon={<UploadOutlined />}
                  onClick={() => fileInputRef.current?.click()}
                  disabled={!confidentialityConfirmed}
                >
                  {selectedFile ? 'Replace Document' : 'Upload Document'}
                </Button>
                
                {selectedFile && (
                  <div className="flex items-center">
                    <span className="ml-2">{documentName || selectedFile.name}</span>
                    <Button 
                      type="text" 
                      danger 
                      icon={<DeleteOutlined />} 
                      onClick={handleRemoveFile}
                      className="ml-1"
                      disabled={submitting}
                    />
                  </div>
                )}
              </div>
            </div>
          </Form.Item>

          <div className="flex justify-end space-x-4 mt-6">
            <Button
              onClick={() => router.push('/trainee/entries')}
              disabled={submitting}
            >
              Cancel
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={submitting}
              icon={<SaveOutlined />}
              disabled={submitting}
            >
              {entryId ? 'Update Entry' : 'Save Entry'}
            </Button>
          </div>
          
          <div className="mt-4 text-right max-w-2xl text-sm text-gray-600 ml-auto">
            By clicking "Save Entry", you will save the information you have added to this
            entry. You will be able to amend its content at any time until you are ready to
            send the entry.
          </div>
        </Form>
      </Spin>
    </Card>
  );
}
