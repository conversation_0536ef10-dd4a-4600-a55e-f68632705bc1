import { Meeting, MeetingType } from "@/types";
import dayjs from "dayjs";
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

export function generateICSContent(meeting: Meeting) {
  const startTime = meeting.availability?.startTime || meeting.proposedTime;
  const endTime = meeting.availability?.endTime || dayjs(startTime).add(1, 'hour').toISOString();

  let meetingType = "";
  switch (meeting.type) {
    case MeetingType.MONTHLY:
      meetingType = "Monthly Meeting";
      break;
    case MeetingType.TPM:
      meetingType = "TPM Meeting";
      break;
    case MeetingType.APPRAISAL:
      meetingType = "Appraisal Meeting";
      break;
  }

  const description = meeting.notes
    ? `Notes: ${meeting.notes}\\n\\n`
    : "";

  // Convert times to Europe/London timezone first, then to UTC for ICS format
  const startTimeUTC = dayjs(startTime).tz('Europe/London').utc().format('YYYYMMDDTHHmmss[Z]');
  const endTimeUTC = dayjs(endTime).tz('Europe/London').utc().format('YYYYMMDDTHHmmss[Z]');
  const currentTimeUTC = dayjs().utc().format('YYYYMMDDTHHmmss[Z]');

  return `BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//Accutrainee//Meeting Calendar//EN
CALSCALE:GREGORIAN
BEGIN:VEVENT
UID:${meeting.id}
SUMMARY:Accutrainee ${meetingType}
DTSTAMP:${currentTimeUTC}
DTSTART:${startTimeUTC}
DTEND:${endTimeUTC}
DESCRIPTION:${description}Meeting between ${meeting?.candidate?.name} and ${meeting?.mentor?.name}
LOCATION:Online
STATUS:CONFIRMED
END:VEVENT
END:VCALENDAR`;
} 