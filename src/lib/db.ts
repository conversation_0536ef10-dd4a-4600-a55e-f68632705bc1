// import { PrismaClient } from '@prisma/client'
import { PrismaClient } from '../generated/prisma';

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

// Check if DATABASE_URL is defined
if (!process.env.DATABASE_URL) {
  console.error('DATABASE_URL environment variable is not defined');
  console.error('Please set the DATABASE_URL environment variable in your .env file');
  console.error('Example: DATABASE_URL="postgresql://postgres:postgres@localhost:5432/pathway21?schema=public"');
}

export const prisma = globalForPrisma.prisma ?? new PrismaClient()

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma 