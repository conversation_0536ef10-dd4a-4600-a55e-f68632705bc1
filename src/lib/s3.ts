import { S3Client, PutObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import crypto from 'crypto';

// File access policies
export enum FileAccess {
  PUBLIC = 'public',
  PRIVATE = 'private'
}

// Folder paths in S3
export const S3_FOLDERS = {
  COURSES: 'courses',
  CATEGORIES: 'categories',
  ENTRIES: 'entries',
  PLACEMENTS: 'placements',
  PROFILES: 'profiles',
  GENERAL: 'general',
  FEEDBACK_FORMS: 'feedback-forms',
  SIGNATURES: 'signatures',
  AVATARS: 'avatars',
  COMPETENCY_PLUS: 'competency-plus',
  TEMPLATES: 'templates'
};

const PRIVATE_BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME || 'pathways21-uploads';
const PUBLIC_BUCKET_NAME = process.env.AWS_S3_BUCKET_PUBLIC_NAME || 'pathways21-public';

// Public URL for course images
export const PUBLIC_URL = process.env.AWS_S3_PUBLIC_URL || `https://${PUBLIC_BUCKET_NAME}.s3.eu-west-1.amazonaws.com`;

const s3Client = new S3Client({
  region: 'eu-west-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

export function generateUniqueFileName(originalName: string): string {
  const timestamp = Date.now();
  const randomString = crypto.randomBytes(8).toString('hex');
  const extension = originalName.split('.').pop();
  return `${timestamp}-${randomString}.${extension}`;
}

export async function uploadToS3(
  file: Buffer,
  fileName: string,
  contentType: string,
  folder: string = S3_FOLDERS.GENERAL,
  access: FileAccess = FileAccess.PRIVATE
): Promise<string> {
  const key = `${folder}/${fileName}`;
  const bucketName = access === FileAccess.PUBLIC ? PUBLIC_BUCKET_NAME : PRIVATE_BUCKET_NAME;

  console.log('S3 Upload attempt:', {
    bucket: bucketName,
    key,
    contentType,
    access,
    publicUrl: access === FileAccess.PUBLIC ? `${PUBLIC_URL}/${key}` : null
  });

  const command = new PutObjectCommand({
    Bucket: bucketName,
    Key: key,
    Body: file,
    ContentType: contentType,
    ...(access === FileAccess.PUBLIC && {
      ACL: 'public-read',
      CacheControl: contentType.startsWith('video/') ? 'max-age=3600' : 'max-age=31536000',
      ContentDisposition: 'inline',
      // For video files, add headers to support range requests
      ...(contentType.startsWith('video/') && {
        ContentEncoding: undefined, // Ensure no encoding that might interfere
        Metadata: {
          'original-name': fileName
        }
      })
    })
  });

  try {
    await s3Client.send(command);
    console.log('S3 Upload successful');
  } catch (error) {
    console.error('S3 Upload failed:', error);
    throw error;
  }
  
  // Return public URL for public files, otherwise return the key
  return access === FileAccess.PUBLIC ? 
    `${PUBLIC_URL}/${key}` : 
    key;
}

export async function getSignedDownloadUrl(key: string, expiresIn: number = 3600): Promise<string> {
  const command = new GetObjectCommand({
    Bucket: PRIVATE_BUCKET_NAME,
    Key: key,
  });

  // URL expires in specified seconds (default 1 hour)
  return getSignedUrl(s3Client, command, { expiresIn });
}

export function getContentType(fileName: string): string {
  const extension = fileName.split('.').pop()?.toLowerCase();
  const contentTypes: { [key: string]: string } = {
    // Images
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    png: 'image/png',
    gif: 'image/gif',
    webp: 'image/webp',
    // Videos
    mp4: 'video/mp4',
    webm: 'video/webm',
    mov: 'video/quicktime',
    avi: 'video/x-msvideo',
    mkv: 'video/x-matroska',
    // Documents
    pdf: 'application/pdf',
    doc: 'application/msword',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    // Audio
    mp3: 'audio/mpeg',
    wav: 'audio/wav',
    ogg: 'audio/ogg',
  };

  return contentTypes[extension || ''] || 'application/octet-stream';
} 