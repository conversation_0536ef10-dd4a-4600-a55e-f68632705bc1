import { prisma } from '@/lib/prisma';

export interface CreateNotificationData {
  userId: string;
  title: string;
  message: string;
  type: string;
  url?: string;
}

export async function createNotification(data: CreateNotificationData) {
  try {
    const notification = await prisma.notification.create({
      data: {
        userId: data.userId,
        title: data.title,
        message: data.message,
        type: data.type,
        url: data.url,
      },
    });
    
    return notification;
  } catch (error) {
    console.error('Error creating notification:', error);
    return null;
  }
}

export async function createNotificationForUserByEmail(
  userEmail: string,
  title: string,
  message: string,
  type: string,
  url?: string
) {
  try {
    const user = await prisma.user.findUnique({
      where: { email: userEmail },
      select: { id: true },
    });
    
    if (!user) {
      console.warn(`User not found for email: ${userEmail}`);
      return null;
    }
    
    console.log('Notification creating:', user);
    return await createNotification({
      userId: user.id,
      title,
      message,
      type,
      url,
    });
  } catch (error) {
    console.error('Error creating notification for user by email:', error);
    return null;
  }
}

export async function createMultipleNotifications(
  notifications: CreateNotificationData[]
) {
  try {
    const createdNotifications = await prisma.notification.createMany({
      data: notifications,
    });
    
    return createdNotifications;
  } catch (error) {
    console.error('Error creating multiple notifications:', error);
    return null;
  }
} 