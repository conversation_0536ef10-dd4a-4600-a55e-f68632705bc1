export const formatDate = (date: string | Date) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};


export const viewDocument = async (documentKey: string) => {
  try {
    const response = await fetch('/api/files', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ documentKey }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to get document URL');
    }
    
    const data = await response.json();
    
    if (!data.url) {
      throw new Error('No URL returned');
    }
    
    window.open(data.url, '_blank');
  } catch (error) {
    console.error('Error viewing document:', error);
  }
};

export function generatePassword(length = 12): string {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
  let password = '';
  
  password += charset.match(/[a-z]/)![0];
  password += charset.match(/[A-Z]/)![0];
  password += charset.match(/[0-9]/)![0];
  password += charset.match(/[!@#$%^&*]/)![0];
  
  for (let i = password.length; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length);
    password += charset[randomIndex];
  }
  
  return password.split('').sort(() => Math.random() - 0.5).join('');
}

export function generateResetToken(length = 32): string {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let token = '';
  
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length);
    token += charset[randomIndex];
  }
  
  return token;
}
