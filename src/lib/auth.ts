import { NextAuthOptions } from 'next-auth';
import { PrismaAdapter } from '@next-auth/prisma-adapter';
import { prisma } from './db';
import { UserRole } from '@/types';
import CredentialsProvider from 'next-auth/providers/credentials';
import { compare } from 'bcryptjs';
import { User } from '@/types';

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60,
    updateAge: 24 * 60 * 60,
  },
  pages: {
    signIn: '/auth/signin',
  },
  callbacks: {
    async session({ session, token }) {
      if (token) {
        session.user = {
          ...session.user,
          id: token.id,
          role: token.role,
          qualificationRoute: token.qualificationRoute,
          qualificationRouteDismissed: token.qualificationRouteDismissed,
          qualificationRouteUpdatedAt: token.qualificationRouteUpdatedAt,
          mentorId: token.mentorId,
          mentor: token.mentor,
          name: token.name,
          email: token.email,
          image: token.image,
          phoneNumber: token.phoneNumber,
          address: token.address,
          isTrainingPrincipal: token.isTrainingPrincipal,
          traineeLevel: token.traineeLevel,
        };
        
        if (token.isImpersonated) {
          session.user.isImpersonated = token.isImpersonated;
          session.user.impersonationId = token.impersonationId;
          session.user.impersonatedBy = token.impersonatedBy;
        }
      }
      return session;
    },

    async jwt({ token, user, trigger }) {
      if (user) {
        token.id = user.id;
        token.role = user.role;
        token.qualificationRoute = user.qualificationRoute;
        token.qualificationRouteDismissed = user.qualificationRouteDismissed;
        token.qualificationRouteUpdatedAt = user.qualificationRouteUpdatedAt;
        token.mentorId = user.mentorId;
        token.mentor = user.mentor;
        token.name = user.name;
        token.email = user.email;
        token.image = user.image;
        token.phoneNumber = user.phoneNumber;
        token.address = user.address;
        token.isTrainingPrincipal = user.isTrainingPrincipal;
        token.traineeLevel = user.traineeLevel;
        
        if ('isImpersonated' in user && user.isImpersonated) {
          token.isImpersonated = user.isImpersonated;
          token.impersonationId = user.impersonationId;
          token.impersonatedBy = user.impersonatedBy;

          token.temporarySession = true;
        }
      }
      
      if (user) {
        if ('isImpersonated' in user && user.isImpersonated) {
          token.isImpersonated = user.isImpersonated;
          token.impersonationId = user.impersonationId;
          token.impersonatedBy = user.impersonatedBy;
        }
      }

      if (trigger === 'update' && token?.id) {
        const updatedUser = await prisma.user.findUnique({
          where: { id: token.id },
          include: {
            mentor: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        });

        if (updatedUser) {
          token.name = updatedUser.name || '';
          token.email = updatedUser.email || '';
          token.role = updatedUser.role;
          token.image = updatedUser.image || undefined;
          token.phoneNumber = updatedUser.phoneNumber || undefined;
          token.address = updatedUser.address || undefined;
          token.qualificationRoute = updatedUser.qualificationRoute || undefined;
          token.qualificationRouteDismissed = updatedUser.qualificationRouteDismissed || false;
          token.qualificationRouteUpdatedAt = updatedUser.qualificationRouteUpdatedAt || undefined;
          token.mentorId = updatedUser.mentorId || undefined;
          token.traineeLevel = updatedUser.traineeLevel || undefined;
          token.isTrainingPrincipal = updatedUser.isTrainingPrincipal || false;
          token.mentor = updatedUser.mentor ? {
            id: updatedUser.mentor.id,
            name: updatedUser.mentor.name || '',
            email: updatedUser.mentor.email || ''
          } : undefined;
        }
      }

      return token;
    },
  },
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
        impersonationData: { label: "Impersonation Data", type: "text" },
        adminReturn: { label: "Admin Return", type: "text" }
      },
      async authorize(credentials) {
        if (credentials?.impersonationData) {
          try {
            const impersonationData = JSON.parse(credentials.impersonationData);
            
            if (!impersonationData.isImpersonated || !impersonationData.impersonationId) {
              throw new Error('Invalid impersonation data');
            }
            
            impersonationData.temporarySession = true;
            
            return impersonationData as User;
          } catch (error) {
            console.error('Error parsing impersonation data:', error);
            throw new Error('Invalid impersonation data');
          }
        }

        if (credentials?.adminReturn === 'true' && credentials?.email) {
          try {
            if (!credentials.password) {
              throw new Error('Password is required to return to admin account');
            }

            const admin = await prisma.user.findUnique({
              where: { email: credentials.email },
              include: {
                mentor: {
                  select: {
                    id: true,
                    name: true,
                    email: true
                  }
                }
              }
            });

            if (!admin || admin.role !== UserRole.ADMIN) {
              throw new Error('Invalid admin account');
            }

            if (!admin.password) {
              throw new Error('Admin account has no password');
            }

            const isPasswordValid = await compare(credentials.password, admin.password);
            if (!isPasswordValid) {
              throw new Error('Invalid admin password');
            }

            return {
              id: admin.id,
              email: admin.email,
              name: admin.name || '',
              role: admin.role,
              qualificationRoute: admin.qualificationRoute || undefined,
              qualificationRouteDismissed: admin.qualificationRouteDismissed || false,
              qualificationRouteUpdatedAt: admin.qualificationRouteUpdatedAt || undefined,
              image: admin.image || undefined,
              phoneNumber: admin.phoneNumber || undefined,
              address: admin.address || undefined,
              mentorId: admin.mentorId || undefined,
              traineeLevel: admin.traineeLevel || undefined,
              mentor: admin.mentor ? {
                id: admin.mentor.id,
                name: admin.mentor.name,
                email: admin.mentor.email
              } : undefined,
              isTrainingPrincipal: admin.isTrainingPrincipal || false
            } as User;
          } catch (error) {
            console.error('Error during admin return:', error);
            throw new Error(error instanceof Error ? error.message : 'Failed to return to admin account');
          }
        }

        if (!credentials?.email || !credentials?.password) {
          throw new Error('Please enter an email and password');
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email
          },
          include: {
            mentor: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        });

        if (!user || !user.password || !user.email) {
          throw new Error('No user found with this email');
        }

        const isPasswordValid = await compare(credentials.password, user.password);

        if (!isPasswordValid) {
          throw new Error('Invalid password');
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name || '',
          role: user.role,
          qualificationRoute: user.qualificationRoute || '',
          qualificationRouteDismissed: user.qualificationRouteDismissed || false,
          qualificationRouteUpdatedAt: user.qualificationRouteUpdatedAt || undefined,
          image: user.image || undefined,
          phoneNumber: user.phoneNumber || undefined,
          address: user.address || undefined,
          mentorId: user.mentorId || undefined,
          traineeLevel: user.traineeLevel || undefined,
          mentor: user.mentor ? {
            id: user.mentor.id,
            name: user.mentor.name,
            email: user.mentor.email
          } : undefined,
          isTrainingPrincipal: user.isTrainingPrincipal || false
        } as User;
      }
    })
  ],
};