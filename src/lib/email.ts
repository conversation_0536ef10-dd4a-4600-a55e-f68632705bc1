import nodemailer from 'nodemailer';
import fs from 'fs/promises';
import path from 'path';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { createNotificationForUserByEmail } from './notifications';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.tz.setDefault('Europe/London');

const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: Number(process.env.SMTP_PORT),
  secure: true,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
});

interface EmailTemplate {
  subject: string;
  html: string;
}

interface MeetingEmailData {
  FName?: string;
  CName?: string;
  MName?: string;
  Date: string;
  Time: string;
  LoginURL: string;
  TimestampURL?: string;
}

async function loadEmailTemplate(templateName: string, data: MeetingEmailData): Promise<string> {
  const templatesDir = path.join(process.cwd(), 'src', 'app', 'api', 'email', 'templates');
  
  let template = await fs.readFile(path.join(templatesDir, templateName), 'utf-8');
  
  Object.entries(data).forEach(([key, value]) => {
    if (value) {
      template = template.replace(new RegExp(`{{.Data.${key}}}`, 'g'), value);
    }
  });

  const generalTemplate = await fs.readFile(path.join(templatesDir, 'general.html'), 'utf-8');
  
  const contentMatch = template.match(/{{define "content"}}([\s\S]*?){{end}}/);
  if (!contentMatch) {
    throw new Error(`Template ${templateName} is missing content block`);
  }
  
  const mainContent = contentMatch[1].trim();
  const headerMatch = generalTemplate.match(/{{define "header"}}([\s\S]*?){{end}}/);
  const footerMatch = generalTemplate.match(/{{define "footer"}}([\s\S]*?){{end}}/);
  
  if (!headerMatch || !footerMatch) {
    throw new Error('General template is missing required sections');
  }

  const finalTemplate = `${headerMatch[1]}${mainContent}${footerMatch[1]}`;

  return finalTemplate;
}

const EMAIL_LAYOUT = (content: string) => {
  return `
<tr>
  <td width="100%" align="center">
    <table width="450px" style="border-collapse:collapse;border-spacing:0px;width:450px;height:100%;background:repeat center top;margin:0;padding:0">
      <tbody>
        <tr style="border-bottom:1px solid #f0f3f7;padding-bottom:20px" height="100px">
          <td align="center">
            <img width="133" src="https://print-manager-media.s3.amazonaws.com/accutrainee-new-logo.png" style="width:133px" alt="Accutrainee Logo">
          </td>
        </tr>
        <tr style="padding-bottom:20px" height="150px">
          <td>
            ${content}
          </td>
        </tr>
        <tr>
          <td style="color:#4f5a68">
            Kind regards, <br>
            Accutrainee Admin <br> <br>
          </td>
        </tr>
        <tr style="border-top:1px solid #f0f3f7;padding:10px 0px">
          <td>
            <p style="color:#8593a6;font-size:12px">
              Be green and keep it on screen. Please consider the
              environment before printing this email.<br><br>
            </p>
          </td>
        </tr>
      </tbody>
    </table>
  </td>
</tr>
`;
};

export const EMAIL_TEMPLATES = {
  RESET_PASSWORD: async (name: string, resetUrl: string): Promise<EmailTemplate> => {
    const templatesDir = path.join(process.cwd(), 'src', 'app', 'api', 'email', 'templates');
    
    let template = await fs.readFile(path.join(templatesDir, 'resetPassword.html'), 'utf-8');
    template = template.replace(/{{\.Data\.name}}/g, name || 'User');
    template = template.replace(/{{\.Data\.resetUrl}}/g, resetUrl);
    
    const generalTemplate = await fs.readFile(path.join(templatesDir, 'general.html'), 'utf-8');
    
    const contentMatch = template.match(/{{define "content"}}([\s\S]*?){{end}}/);
    if (!contentMatch) {
      throw new Error('Template is missing content block');
    }
    
    const mainContent = contentMatch[1].trim();
    const headerMatch = generalTemplate.match(/{{define "header"}}([\s\S]*?){{end}}/);
    const footerMatch = generalTemplate.match(/{{define "footer"}}([\s\S]*?){{end}}/);
    
    if (!headerMatch || !footerMatch) {
      throw new Error('General template is missing required sections');
    }

    return {
      subject: 'Reset Your Password',
      html: `${headerMatch[1]}${mainContent}${footerMatch[1]}`
    };
  },

  TRAINEE_SUBMISSION_TO_SUPERVISOR: (traineeName: string, viewLink: string): EmailTemplate => ({
    subject: `Entries Submitted to Supervisor`,
    html: EMAIL_LAYOUT(`
      <p style="color:#4f5a68">
        Dear ${traineeName}, <br><br>

        Your portfolio entries have been successfully submitted to your Supervisor for review. You can view your submission here. <a href="${viewLink}" target="_blank">View Submission</a>.<br><br>

        If you need to make changes to your entries, you <strong>do not need to resubmit them</strong> once you have made the necessary amendments. You can edit the content of the entries on Pathways, and they will <strong>automatically update</strong> on the Supervisor's account.<br><br>

        Once the Supervisor is happy with the content, they will be able to approve them.<br><br>
      </p>
    `),
  }),

  TRAINEE_SUBMISSION_TO_MENTOR: (traineeName: string, viewLink: string): EmailTemplate => ({
    subject: `Entries Submitted to Mentor`,
    html: EMAIL_LAYOUT(`
      <p style="color:#4f5a68">
        Dear ${traineeName}, <br><br>

        Your portfolio entries have been successfully submitted to your Mentor for review. You can view your submission here. <a href="${viewLink}" target="_blank">View Submission</a>.<br><br>
      </p>
    `),
  }),

  SUPERVISOR_WELCOME:async (supervisorName: string, password: string): Promise<EmailTemplate> => {
    const loginUrl = `${process.env.NEXTAUTH_URL}/auth/login`;
    const templatesDir = path.join(process.cwd(), 'src', 'app', 'api', 'email', 'templates');
    
    let template = await fs.readFile(path.join(templatesDir, 'managerFinalise.html'), 'utf-8');
    template = template.replace(/{{\.Data\.FName}}/g, supervisorName);
    template = template.replace(/{{\.Data\.URL}}/g, loginUrl);
    template = template.replace(/{{\.Data\.Password}}/g, password);
    
    let generalTemplate = await fs.readFile(path.join(templatesDir, 'general.html'), 'utf-8');
    const logoUrl = 'https://print-manager-media.s3.amazonaws.com/accutrainee-new-logo.png';
    generalTemplate = generalTemplate.replace(/{{\.Data\.Logo}}/g, logoUrl);
      
    const contentMatch = template.match(/{{define "content"}}([\s\S]*?){{end}}/);
    if (!contentMatch) {
      throw new Error('Template is missing content block');
    }
    
    const mainContent = contentMatch[1].trim();
    const headerMatch = generalTemplate.match(/{{define "header"}}([\s\S]*?){{end}}/);
    const footerMatch = generalTemplate.match(/{{define "footer"}}([\s\S]*?){{end}}/);
    
    if (!headerMatch || !footerMatch) {
      throw new Error('General template is missing required sections');
    }

    return {
      subject: 'Welcome to Pathways - Your Account Details',
      html: `${headerMatch[1]}${mainContent}${footerMatch[1]}`
    };
  },

  MENTOR_ENTRY_REVIEW: (mentorName: string, traineeName: string, viewLink: string): EmailTemplate => ({
    subject: 'New Entry for Review',
    html: EMAIL_LAYOUT(`
      <h2 style="color:#0c152e">New Entry for Review</h2>
      <p style="color:#4f5a68">
        Dear ${mentorName}, <br><br>

        ${traineeName} has created a new entry for your review.<br>
        You can view the entry by clicking this link: <a href="${viewLink}" target="_blank">View Entry</a><br><br>

        Please review the entry and provide any feedback directly to ${traineeName} if needed.<br><br>
      </p>
    `),
  }),

  SUPERVISOR_REVIEWER_REQUEST: (reviewerName: string, traineeName: string, viewLink: string, approveLink: string): EmailTemplate => ({
    subject: 'Trainee Portfolio Review Request',
    html: EMAIL_LAYOUT(`
      <h2 style="color:#0c152e">Trainee Portfolio Review Request</h2>
      <p style="color:#4f5a68">
        Dear ${reviewerName}, <br><br>

        ${traineeName} has submitted portfolio entries for your review.<br>
        You can view the portfolio entries by clicking this link: <a href="${viewLink}" target="_blank">View Entries</a><br>
        If you are happy with the attached entries, please follow this link to approve them: <a href="${approveLink}" target="_blank">Approve Entries</a><br><br>

        If you feel any of the entries need amending, please contact ${traineeName} to discuss suggestions to make the necessary changes before approval.<br><br>
      </p>
    `),
  }),

  MENTOR_REVIEWER_REQUEST: (reviewerName: string, traineeName: string, viewLink: string, approveLink: string): EmailTemplate => ({
    subject: 'Trainee Portfolio Review Request',
    html: EMAIL_LAYOUT(`
      <h2 style="color:#0c152e">Trainee Portfolio Review Request</h2>
      <p style="color:#4f5a68">
        Dear ${reviewerName}, <br><br>

        ${traineeName} has submitted portfolio entries for your review.<br>
        You can view the portfolio entries by clicking this link: <a href="${viewLink}" target="_blank">View Entries</a><br>
      </p>
    `),
  }),

  TRAINEE_SUBMISSION_CONFIRMATION: (traineeName: string, viewLink: string): EmailTemplate => ({
    subject: 'Portfolio Submission Confirmation',
    html: EMAIL_LAYOUT(`
      <h2 style="color:#0c152e">Portfolio Submission Confirmation</h2>
      <p style="color:#4f5a68">
        Dear ${traineeName}, <br><br>

        Your portfolio entries have been successfully submitted for review.<br>
        You can view your submission here: <a href="${viewLink}" target="_blank">View Submission</a><br><br>

        You will be notified once your submission has been reviewed.<br><br>
      </p>
    `),
  }),
};

export async function sendEmail(
  to: string, 
  template: EmailTemplate & { attachments?: Array<{ filename: string; content: string; contentType: string }> }
) {
  try {
    const mailOptions = {
      from: process.env.SMTP_FROM || '<EMAIL>',
      to,
      subject: template.subject,
      html: template.html,
      attachments: template.attachments
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Email sent:', info.messageId);
    return true;
  } catch (error) {
    console.error('Error sending email:', error);
    return false;
  }
}

export async function sendSubmissionNotifications(
  traineeName: string,
  traineeEmail: string,
  supervisorEmail: string | null,
  supervisorName: string | null,
  mentorEmail: string | null,
  mentorName: string | null,
  viewLink: string,
  approveLink: string
) {
  const emailPromises = [];
  console.log('email data', traineeEmail, supervisorEmail, mentorEmail,viewLink, approveLink);
  // Send confirmation to trainee
  emailPromises.push(
    sendEmail(
      traineeEmail,
      EMAIL_TEMPLATES.TRAINEE_SUBMISSION_CONFIRMATION(traineeName, viewLink)
    )
  );

  // Send to supervisor if exists
  if (supervisorEmail && supervisorName) {
    emailPromises.push(
      sendEmail(
        supervisorEmail,
        EMAIL_TEMPLATES.SUPERVISOR_REVIEWER_REQUEST(supervisorName, traineeName, viewLink, approveLink)
      )
    );
  }

  // Send to mentor if exists
  if (mentorEmail && mentorName) {
    emailPromises.push(
      sendEmail(
        mentorEmail,
        EMAIL_TEMPLATES.MENTOR_REVIEWER_REQUEST(mentorName, traineeName, viewLink, approveLink)
      )
    );
  }

  return Promise.all(emailPromises);
}

export async function sendMeetingRequestEmails(
  mentorEmail: string,
  mentorName: string,
  candidateEmail: string,
  candidateName: string,
  meetingDate: Date,
  loginUrl: string
) {
  const emailData: MeetingEmailData = {
    MName: mentorName,
    CName: candidateName,
    Date: dayjs(meetingDate).tz('Europe/London').format('DD/MM/YYYY'),
    Time: dayjs(meetingDate).tz('Europe/London').format('HH:mm'),
    LoginURL: loginUrl,
    TimestampURL: `${loginUrl}#${meetingDate.getTime()}`
  };

  const emailPromises = [];

  // Send email to mentor
  const mentorTemplate = await loadEmailTemplate('meetingRequested.html', {
    ...emailData,
    FName: mentorName
  });
  emailPromises.push(
    sendEmail(mentorEmail, {
      subject: 'Accutrainee Meeting Request',
      html: mentorTemplate
    })
  );

  // Send email to candidate
  const candidateTemplate = await loadEmailTemplate('meetingRequestedCandidate.html', {
    ...emailData,
    FName: candidateName
  });
  emailPromises.push(
    sendEmail(candidateEmail, {
      subject: 'Accutrainee Meeting Request Confirmation',
      html: candidateTemplate
    })
  );

  return Promise.all(emailPromises);
}

export async function sendMeetingAcceptedEmails(
  mentorEmail: string,
  mentorName: string,
  candidateEmail: string,
  candidateName: string,
  meetingDate: Date,
  loginUrl: string,
  icsContent: string
) {
  const emailData: MeetingEmailData = {
    FName: '',
    CName: candidateName,
    MName: mentorName,
    Date: dayjs(meetingDate).tz('Europe/London').format('DD/MM/YYYY'),
    Time: dayjs(meetingDate).tz('Europe/London').format('HH:mm'),
    LoginURL: loginUrl,
    TimestampURL: `${loginUrl}#${meetingDate.getTime()}`
  };

  const emailPromises = [];

  // Send email to candidate
  const candidateTemplate = await loadEmailTemplate('meetingRequestedAccepted.html', {
    ...emailData,
    FName: candidateName
  });
  emailPromises.push(
    sendEmail(candidateEmail, {
      subject: 'Accutrainee Meeting Accepted',
      html: candidateTemplate,
      attachments: [{
        filename: 'meeting.ics',
        content: icsContent,
        contentType: 'text/calendar'
      }]
    })
  );

  // Send email to mentor
  const mentorTemplate = await loadEmailTemplate('meetingRequestedAcceptedMentor.html', {
    ...emailData,
    FName: mentorName
  });
  emailPromises.push(
    sendEmail(mentorEmail, {
      subject: 'Accutrainee Meeting Confirmation',
      html: mentorTemplate,
      attachments: [{
        filename: 'meeting.ics',
        content: icsContent,
        contentType: 'text/calendar'
      }]
    })
  );

  return Promise.all(emailPromises);
}

export async function sendMeetingRejectedEmail(
  candidateEmail: string,
  candidateName: string,
  mentorName: string,
  meetingDate: Date,
  loginUrl: string
) {
  const emailData: MeetingEmailData = {
    FName: candidateName,
    CName: candidateName,
    MName: mentorName,
    Date: dayjs(meetingDate).tz('Europe/London').format('DD/MM/YYYY'),
    Time: dayjs(meetingDate).tz('Europe/London').format('HH:mm'),
    LoginURL: loginUrl
  };

  const template = await loadEmailTemplate('meetingRequestedRejected.html', emailData);
  
  return sendEmail(candidateEmail, {
    subject: 'Accutrainee Meeting Declined',
    html: template
  });
}

export async function sendMentorAvailabilityNotification(
  trainees: Array<{ name: string; email: string }>,
  loginUrl: string
) {
  const emailPromises = trainees.map(trainee => {
    const emailData = {
      Name: trainee.name,
      LoginURL: loginUrl,
      // Required by MeetingEmailData but not used in this template
      FName: trainee.name,
      CName: '',
      Date: '',
      Time: '',
    };

    return loadEmailTemplate('mentorAvailabilityUploadedNoti.html', emailData)
      .then(html => sendEmail(trainee.email, {
        subject: 'Your mentor has uploaded their availability',
        html
      }));
  });

  return Promise.all(emailPromises);
}

export async function sendSupervisorWelcomeEmail(supervisorName: string, supervisorEmail: string, password: string) {
  const template = await EMAIL_TEMPLATES.SUPERVISOR_WELCOME(supervisorName, password);
  return sendEmail(supervisorEmail, template);
}

// export async function sendEntryCreationEmailsWithNotifications(
//   traineeEmail: string,
//   traineeName: string,
//   mentorEmail: string | null,
//   mentorName: string | null,
//   startDate: string,
//   endDate: string,
//   viewLink: string
// ) {
//   const emailPromises = [];
//   const notificationPromises = [];

//   // Send notification to mentor if exists
//   if (mentorEmail && mentorName) {
//     emailPromises.push(
//       sendEmail(
//         mentorEmail,
//         EMAIL_TEMPLATES.MENTOR_ENTRY_REVIEW(mentorName, traineeName, viewLink)
//       )
//     );
    
//     notificationPromises.push(
//       createNotificationForUserByEmail(
//         mentorEmail,
//         'New Entry for Review',
//         `${traineeName} has created a new entry for your review.`,
//         'entry_review',
//         viewLink
//       )
//     );
//   }

//   await Promise.all([...emailPromises, ...notificationPromises]);
// }

export async function sendSubmissionNotificationsWithNotifications(
  traineeName: string,
  traineeEmail: string,
  supervisorEmail: string | null,
  supervisorName: string | null,
  mentorEmail: string | null,
  mentorName: string | null,
  viewLink: string,
  approveLink: string,
  startDate: string,
  endDate: string,
  reviewerRole: string
) {
  const emailPromises = [];
  const notificationPromises = [];
  // Send confirmation to trainee
  if (reviewerRole === 'supervisor') {
  emailPromises.push(
    sendEmail(
      traineeEmail,
        EMAIL_TEMPLATES.TRAINEE_SUBMISSION_TO_SUPERVISOR(traineeName, viewLink)
      )
    );
  } else if (reviewerRole === 'mentor') {
    emailPromises.push(
      sendEmail(
        traineeEmail,
        EMAIL_TEMPLATES.TRAINEE_SUBMISSION_TO_MENTOR(traineeName, viewLink)
      )
    );
  }
  
  // notificationPromises.push(
  //   createNotificationForUserByEmail(
  //     traineeEmail,
  //     'Portfolio Submission Confirmation',
  //     'Your portfolio entries have been successfully submitted for review.',
  //     'submission_confirmation',
  //     viewLink
  //   )
  // );

  // Send to supervisor if exists
  if (supervisorEmail && supervisorName) {
    emailPromises.push(
      sendEmail(
        supervisorEmail,
        await EMAIL_TEMPLATES.SUPERVISOR_REVIEWER_REQUEST(supervisorName, traineeName, viewLink, approveLink)
      )
    );
    
    // notificationPromises.push(
    //   createNotificationForUserByEmail(
    //     supervisorEmail,
    //     'Trainee Portfolio Review Request',
    //     `${traineeName} has submitted portfolio entries for your review.`,
    //     'submission_supervisor_review',
    //     approveLink
    //   )
    // );
  }

  // Send to mentor if exists
  if (mentorEmail && mentorName) {
    emailPromises.push(
      sendEmail(
        mentorEmail,
        EMAIL_TEMPLATES.MENTOR_REVIEWER_REQUEST(mentorName, traineeName, viewLink, approveLink)
      )
    );
    
    // notificationPromises.push(
    //   createNotificationForUserByEmail(
    //     mentorEmail,
    //     'Trainee Portfolio Review Request',
    //     `${traineeName} has submitted portfolio entries for your review.`,
    //     'submission_mentor_review',
    //     viewLink
    //   )
    // );
  }

  await Promise.all([...emailPromises]);
}

export async function sendMeetingRequestEmailsWithNotifications(
  mentorEmail: string,
  mentorName: string,
  candidateEmail: string,
  candidateName: string,
  meetingDate: Date,
  loginUrl: string
) {
  const emailData: MeetingEmailData = {
    MName: mentorName,
    CName: candidateName,
    Date: dayjs(meetingDate).tz('Europe/London').format('DD/MM/YYYY'),
    Time: dayjs(meetingDate).tz('Europe/London').format('HH:mm'),
    LoginURL: loginUrl,
    TimestampURL: `${loginUrl}#${meetingDate.getTime()}`
  };

  const emailPromises = [];
  const notificationPromises = [];

  // Send email to mentor
  const mentorTemplate = await loadEmailTemplate('meetingRequested.html', {
    ...emailData,
    FName: mentorName
  });
  emailPromises.push(
    sendEmail(mentorEmail, {
      subject: 'Accutrainee Meeting Request',
      html: mentorTemplate
    })
  );
  
  // notificationPromises.push(
  //   createNotificationForUserByEmail(
  //     mentorEmail,
  //     'Meeting Request',
  //     `${candidateName} has requested a meeting with you.`,
  //     'meeting_request',
  //     loginUrl
  //   )
  // );

  // Send email to candidate
  const candidateTemplate = await loadEmailTemplate('meetingRequestedCandidate.html', {
    ...emailData,
    FName: candidateName
  });
  emailPromises.push(
    sendEmail(candidateEmail, {
      subject: 'Accutrainee Meeting Request Confirmation',
      html: candidateTemplate
    })
  );
  
  // notificationPromises.push(
  //   createNotificationForUserByEmail(
  //     candidateEmail,
  //     'Meeting Request Confirmation',
  //     `Your meeting request with ${mentorName} has been sent.`,
  //     'meeting_request_confirmation',
  //     loginUrl
  //   )
  // );

  await Promise.all([...emailPromises]);
}

export async function sendMeetingAcceptedEmailsWithNotifications(
  mentorEmail: string,
  mentorName: string,
  candidateEmail: string,
  candidateName: string,
  meetingDate: Date,
  loginUrl: string,
  icsContent: string
) {
  const emailData: MeetingEmailData = {
    FName: '',
    CName: candidateName,
    MName: mentorName,
    Date: dayjs(meetingDate).tz('Europe/London').format('DD/MM/YYYY'),
    Time: dayjs(meetingDate).tz('Europe/London').format('HH:mm'),
    LoginURL: loginUrl,
    TimestampURL: `${loginUrl}#${meetingDate.getTime()}`
  };

  const emailPromises = [];
  const notificationPromises = [];

  // Send email to candidate
  const candidateTemplate = await loadEmailTemplate('meetingRequestedAccepted.html', {
    ...emailData,
    FName: candidateName
  });
  emailPromises.push(
    sendEmail(candidateEmail, {
      subject: 'Accutrainee Meeting Accepted',
      html: candidateTemplate,
      attachments: [{
        filename: 'meeting.ics',
        content: icsContent,
        contentType: 'text/calendar'
      }]
    })
  );
  
  // notificationPromises.push(
  //   createNotificationForUserByEmail(
  //     candidateEmail,
  //     'Meeting Accepted',
  //     `${mentorName} has accepted your meeting request.`,
  //     'meeting_accepted',
  //     loginUrl
  //   )
  // );

  // Send email to mentor
  const mentorTemplate = await loadEmailTemplate('meetingRequestedAcceptedMentor.html', {
    ...emailData,
    FName: mentorName
  });
  emailPromises.push(
    sendEmail(mentorEmail, {
      subject: 'Accutrainee Meeting Confirmation',
      html: mentorTemplate,
      attachments: [{
        filename: 'meeting.ics',
        content: icsContent,
        contentType: 'text/calendar'
      }]
    })
  );
  
  // notificationPromises.push(
  //   createNotificationForUserByEmail(
  //     mentorEmail,
  //     'Meeting Confirmation',
  //     `Your meeting with ${candidateName} has been confirmed.`,
  //     'meeting_confirmation',
  //     loginUrl
  //   )
  // );

  await Promise.all([...emailPromises]);
}

export async function sendMeetingRejectedEmailWithNotification(
  candidateEmail: string,
  candidateName: string,
  mentorName: string,
  meetingDate: Date,
  loginUrl: string
) {
  const emailData: MeetingEmailData = {
    FName: candidateName,
    CName: candidateName,
    MName: mentorName,
    Date: dayjs(meetingDate).tz('Europe/London').format('DD/MM/YYYY'),
    Time: dayjs(meetingDate).tz('Europe/London').format('HH:mm'),
    LoginURL: loginUrl
  };

  const template = await loadEmailTemplate('meetingRequestedRejected.html', emailData);
  
  await Promise.all([
    sendEmail(candidateEmail, {
      subject: 'Accutrainee Meeting Declined',
      html: template
    }),
    createNotificationForUserByEmail(
      candidateEmail,
      'Meeting Declined',
      `${mentorName} has declined your meeting request.`,
      'meeting_declined',
      loginUrl
    )
  ]);
}

export async function sendMentorAvailabilityNotificationWithNotifications(
  trainees: Array<{ name: string; email: string }>,
  loginUrl: string
) {
  const emailPromises = trainees.map(trainee => {
    const emailData = {
      Name: trainee.name,
      LoginURL: loginUrl,
      FName: trainee.name,
      CName: '',
      Date: '',
      Time: '',
    };

    return loadEmailTemplate('mentorAvailabilityUploadedNoti.html', emailData)
      .then(html => sendEmail(trainee.email, {
        subject: 'Your mentor has uploaded their availability',
        html
      }));
  });

  // const notificationPromises = trainees.map(trainee =>
  //   createNotificationForUserByEmail(
  //     trainee.email,
  //     'Mentor Availability Updated',
  //     'Your mentor has uploaded their availability for meetings.',
  //     'mentor_availability',
  //     loginUrl
  //   )
  // );

  await Promise.all([...emailPromises]);
}

export async function sendSupervisorWelcomeEmailWithNotification(supervisorName: string, supervisorEmail: string, password: string) {
  const template = await EMAIL_TEMPLATES.SUPERVISOR_WELCOME(supervisorName, password);
  
  await Promise.all([
    sendEmail(supervisorEmail, template),
    createNotificationForUserByEmail(
      supervisorEmail,
      'Welcome to Pathways',
      'Your supervisor account has been created successfully.',
      'welcome',
      `${process.env.NEXTAUTH_URL}/auth/login`
    )
  ]);
}