
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.6.0
 * Query Engine version: f676762280b54cd07c770017ed3711ddde35f37a
 */
Prisma.prismaVersion = {
  client: "6.6.0",
  engine: "f676762280b54cd07c770017ed3711ddde35f37a"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.AccountScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  provider: 'provider',
  providerAccountId: 'providerAccountId',
  refresh_token: 'refresh_token',
  access_token: 'access_token',
  expires_at: 'expires_at',
  token_type: 'token_type',
  scope: 'scope',
  id_token: 'id_token',
  session_state: 'session_state'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  sessionToken: 'sessionToken',
  userId: 'userId',
  expires: 'expires'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  emailVerified: 'emailVerified',
  image: 'image',
  password: 'password',
  role: 'role',
  traineeLevel: 'traineeLevel',
  phoneNumber: 'phoneNumber',
  address: 'address',
  qualificationRoute: 'qualificationRoute',
  mentorId: 'mentorId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  skillGroupType: 'skillGroupType',
  isTrainingPrincipal: 'isTrainingPrincipal',
  qualificationRouteDismissed: 'qualificationRouteDismissed',
  qualificationRouteUpdatedAt: 'qualificationRouteUpdatedAt',
  resetToken: 'resetToken',
  resetTokenExpiry: 'resetTokenExpiry'
};

exports.Prisma.VerificationTokenScalarFieldEnum = {
  identifier: 'identifier',
  token: 'token',
  expires: 'expires'
};

exports.Prisma.ClientScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  phone: 'phone',
  address: 'address',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ClientSupervisorScalarFieldEnum = {
  clientId: 'clientId',
  supervisorId: 'supervisorId',
  assignedAt: 'assignedAt'
};

exports.Prisma.PlacementScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  name: 'name',
  startDate: 'startDate',
  endDate: 'endDate',
  orgPosition: 'orgPosition',
  orgSraNumber: 'orgSraNumber',
  supervisorName: 'supervisorName',
  supervisorEmail: 'supervisorEmail',
  isFullTime: 'isFullTime',
  partTimeDays: 'partTimeDays',
  daysMissed: 'daysMissed',
  documentKey: 'documentKey',
  documentName: 'documentName',
  userId: 'userId',
  clientId: 'clientId',
  supervisorId: 'supervisorId',
  mentorId: 'mentorId',
  qweConfirmingPerson: 'qweConfirmingPerson',
  qweSraNumber: 'qweSraNumber',
  qweEmail: 'qweEmail'
};

exports.Prisma.PracticeAreaScalarFieldEnum = {
  id: 'id',
  name: 'name',
  createdAt: 'createdAt'
};

exports.Prisma.PracticeSkillScalarFieldEnum = {
  id: 'id',
  practiceAreaId: 'practiceAreaId',
  name: 'name',
  createdAt: 'createdAt'
};

exports.Prisma.PracticeSkillGroupScalarFieldEnum = {
  id: 'id',
  practiceSkillId: 'practiceSkillId',
  name: 'name',
  createdAt: 'createdAt'
};

exports.Prisma.PracticeSubSkillScalarFieldEnum = {
  id: 'id',
  practiceSkillGroupId: 'practiceSkillGroupId',
  name: 'name',
  practiceSubSkillType: 'practiceSubSkillType',
  minSuggestedEntryCount: 'minSuggestedEntryCount',
  order: 'order',
  createdAt: 'createdAt'
};

exports.Prisma.EntrySubSkillScalarFieldEnum = {
  entryId: 'entryId',
  subSkillId: 'subSkillId'
};

exports.Prisma.EntryScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  title: 'title',
  startDate: 'startDate',
  endDate: 'endDate',
  experience: 'experience',
  evidence: 'evidence',
  learnt: 'learnt',
  moreExperience: 'moreExperience',
  needMoreExperience: 'needMoreExperience',
  contentiousType: 'contentiousType',
  taskedBy: 'taskedBy',
  documentKey: 'documentKey',
  documentName: 'documentName',
  practiceAreas: 'practiceAreas',
  submittedAt: 'submittedAt',
  reviewedAt: 'reviewedAt',
  feedback: 'feedback',
  placementId: 'placementId',
  reviewerId: 'reviewerId',
  submissionId: 'submissionId',
  status: 'status'
};

exports.Prisma.SubmissionScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  startDate: 'startDate',
  endDate: 'endDate',
  status: 'status',
  submittedAt: 'submittedAt',
  traineeId: 'traineeId',
  reviewerId: 'reviewerId',
  supervisorId: 'supervisorId',
  title: 'title',
  reviewerRole: 'reviewerRole',
  placementId: 'placementId'
};

exports.Prisma.EntrySubmissionScalarFieldEnum = {
  id: 'id',
  entryId: 'entryId',
  submissionId: 'submissionId',
  status: 'status',
  submittedAt: 'submittedAt',
  reviewedAt: 'reviewedAt',
  feedback: 'feedback',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TraineeSkillScalarFieldEnum = {
  id: 'id',
  doneEntryCount: 'doneEntryCount',
  minSuggestedEntryCount: 'minSuggestedEntryCount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  traineeId: 'traineeId',
  subSkillId: 'subSkillId'
};

exports.Prisma.QualificationScalarFieldEnum = {
  id: 'id',
  type: 'type',
  name: 'name',
  fileUrl: 'fileUrl',
  fileName: 'fileName',
  startDate: 'startDate',
  endDate: 'endDate',
  grade: 'grade',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
};

exports.Prisma.CharacterSuitabilityScalarFieldEnum = {
  id: 'id',
  rules: 'rules',
  infoForQualifiedSolicitors: 'infoForQualifiedSolicitors',
  externalLinks: 'externalLinks',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CharacterSuitabilityCompletionScalarFieldEnum = {
  id: 'id',
  completed: 'completed',
  completedAt: 'completedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  characterSuitabilityId: 'characterSuitabilityId'
};

exports.Prisma.MentorAvailabilityScalarFieldEnum = {
  id: 'id',
  mentorId: 'mentorId',
  startTime: 'startTime',
  endTime: 'endTime',
  isBooked: 'isBooked',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  meetingMethod: 'meetingMethod',
  meetingLocation: 'meetingLocation',
  duration: 'duration',
  bufferTime: 'bufferTime',
  meetingMessage: 'meetingMessage'
};

exports.Prisma.MeetingScalarFieldEnum = {
  id: 'id',
  type: 'type',
  candidateId: 'candidateId',
  mentorId: 'mentorId',
  availabilityId: 'availabilityId',
  status: 'status',
  proposedTime: 'proposedTime',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  meetingUrl: 'meetingUrl',
  meetingLocation: 'meetingLocation'
};

exports.Prisma.FeedbackFormScalarFieldEnum = {
  id: 'id',
  monthlyReviewId: 'monthlyReviewId',
  coveringMonth: 'coveringMonth',
  menteeName: 'menteeName',
  meetingDate: 'meetingDate',
  pointsToDiscuss: 'pointsToDiscuss',
  wasGoodWork: 'wasGoodWork',
  goodWorkComments: 'goodWorkComments',
  hasFeltResponsible: 'hasFeltResponsible',
  responsibilityComments: 'responsibilityComments',
  workConcerns: 'workConcerns',
  isWorkingWellSupervisor: 'isWorkingWellSupervisor',
  workingWellSupervisorComments: 'workingWellSupervisorComments',
  hadInformalMonthlyFeedback: 'hadInformalMonthlyFeedback',
  informalMonthlyFeedbackComments: 'informalMonthlyFeedbackComments',
  skillStandardsComments: 'skillStandardsComments',
  anyConcerns: 'anyConcerns',
  furtherTraining: 'furtherTraining',
  appraisalMeeting: 'appraisalMeeting',
  qualifyingArea: 'qualifyingArea',
  mentorNotes: 'mentorNotes',
  furtherAction: 'furtherAction',
  pointsToNote: 'pointsToNote',
  signatureKey: 'signatureKey',
  signatureName: 'signatureName',
  printName: 'printName',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MonthlyReviewScalarFieldEnum = {
  id: 'id',
  documentName: 'documentName',
  completionDate: 'completionDate',
  action: 'action',
  s3Key: 's3Key',
  documentType: 'documentType',
  referredToTP: 'referredToTP',
  candidateId: 'candidateId',
  mentorId: 'mentorId',
  meetingId: 'meetingId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CourseScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  imageUrl: 'imageUrl',
  externalLink: 'externalLink',
  pricing: 'pricing',
  categoryId: 'categoryId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CourseCategoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  color: 'color',
  imageUrl: 'imageUrl',
  isClassroom: 'isClassroom',
  isSecondCategory: 'isSecondCategory',
  faqs: 'faqs',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CourseSecondCategoryScalarFieldEnum = {
  id: 'id',
  courseId: 'courseId',
  categoryId: 'categoryId',
  createdAt: 'createdAt'
};

exports.Prisma.CourseClickScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  courseId: 'courseId',
  clickedAt: 'clickedAt',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent'
};

exports.Prisma.AdminImpersonationScalarFieldEnum = {
  id: 'id',
  adminId: 'adminId',
  userId: 'userId',
  userRole: 'userRole',
  reason: 'reason',
  startedAt: 'startedAt',
  endedAt: 'endedAt',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  title: 'title',
  message: 'message',
  type: 'type',
  url: 'url',
  isRead: 'isRead',
  createdAt: 'createdAt'
};

exports.Prisma.CompetencyPlusScalarFieldEnum = {
  id: 'id',
  title: 'title',
  shortDescription: 'shortDescription',
  content: 'content',
  externalLinks: 'externalLinks',
  audioUrl: 'audioUrl',
  videoUrl: 'videoUrl',
  bannerImageUrl: 'bannerImageUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  published: 'published',
  publishedAt: 'publishedAt'
};

exports.Prisma.MeetingTemplateScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  fileName: 'fileName',
  s3Key: 's3Key',
  version: 'version',
  isActive: 'isActive',
  uploadedBy: 'uploadedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MentorMenteeHistoryScalarFieldEnum = {
  id: 'id',
  mentorId: 'mentorId',
  menteeId: 'menteeId',
  startDate: 'startDate',
  endDate: 'endDate',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SupervisorTraineeHistoryScalarFieldEnum = {
  id: 'id',
  supervisorId: 'supervisorId',
  traineeId: 'traineeId',
  placementId: 'placementId',
  startDate: 'startDate',
  endDate: 'endDate',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.UserRole = exports.$Enums.UserRole = {
  ADMIN: 'ADMIN',
  SUPERVISOR: 'SUPERVISOR',
  MENTOR: 'MENTOR',
  TRAINEE: 'TRAINEE'
};

exports.PracticeSubSkillType = exports.$Enums.PracticeSubSkillType = {
  sqe: 'sqe',
  tc: 'tc',
  both: 'both'
};

exports.QualificationType = exports.$Enums.QualificationType = {
  A_LEVEL: 'A_LEVEL',
  DEGREE: 'DEGREE',
  GDL: 'GDL',
  LPC: 'LPC',
  PSC: 'PSC',
  SQE1: 'SQE1',
  SQE2: 'SQE2',
  QUALIFICATION_CERT: 'QUALIFICATION_CERT',
  PRACTICING_CERT: 'PRACTICING_CERT',
  OTHER: 'OTHER'
};

exports.QualificationStatus = exports.$Enums.QualificationStatus = {
  PENDING: 'PENDING',
  VERIFIED: 'VERIFIED',
  REJECTED: 'REJECTED'
};

exports.MeetingFormat = exports.$Enums.MeetingFormat = {
  IN_PERSON: 'IN_PERSON',
  VIRTUAL: 'VIRTUAL'
};

exports.MeetingType = exports.$Enums.MeetingType = {
  MONTHLY: 'MONTHLY',
  TPM: 'TPM',
  APPRAISAL: 'APPRAISAL'
};

exports.MeetingStatus = exports.$Enums.MeetingStatus = {
  PENDING: 'PENDING',
  ACCEPTED: 'ACCEPTED',
  DECLINED: 'DECLINED',
  COMPLETED: 'COMPLETED'
};

exports.FeedbackFormStatus = exports.$Enums.FeedbackFormStatus = {
  DRAFT: 'DRAFT',
  SUBMITTED: 'SUBMITTED',
  APPROVED: 'APPROVED'
};

exports.DocumentType = exports.$Enums.DocumentType = {
  MONTHLY_REVIEW: 'MONTHLY_REVIEW',
  FEEDBACK_FORM: 'FEEDBACK_FORM',
  OTHER: 'OTHER',
  APPRAISAL_FORM: 'APPRAISAL_FORM',
  TPM_MEETING_FORM: 'TPM_MEETING_FORM',
  MENTOR_REFERRAL_FORM: 'MENTOR_REFERRAL_FORM'
};

exports.Prisma.ModelName = {
  Account: 'Account',
  Session: 'Session',
  User: 'User',
  VerificationToken: 'VerificationToken',
  Client: 'Client',
  ClientSupervisor: 'ClientSupervisor',
  Placement: 'Placement',
  PracticeArea: 'PracticeArea',
  PracticeSkill: 'PracticeSkill',
  PracticeSkillGroup: 'PracticeSkillGroup',
  PracticeSubSkill: 'PracticeSubSkill',
  EntrySubSkill: 'EntrySubSkill',
  Entry: 'Entry',
  Submission: 'Submission',
  EntrySubmission: 'EntrySubmission',
  TraineeSkill: 'TraineeSkill',
  Qualification: 'Qualification',
  CharacterSuitability: 'CharacterSuitability',
  CharacterSuitabilityCompletion: 'CharacterSuitabilityCompletion',
  MentorAvailability: 'MentorAvailability',
  Meeting: 'Meeting',
  FeedbackForm: 'FeedbackForm',
  MonthlyReview: 'MonthlyReview',
  Course: 'Course',
  CourseCategory: 'CourseCategory',
  CourseSecondCategory: 'CourseSecondCategory',
  CourseClick: 'CourseClick',
  AdminImpersonation: 'AdminImpersonation',
  Notification: 'Notification',
  CompetencyPlus: 'CompetencyPlus',
  MeetingTemplate: 'MeetingTemplate',
  MentorMenteeHistory: 'MentorMenteeHistory',
  SupervisorTraineeHistory: 'SupervisorTraineeHistory'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
