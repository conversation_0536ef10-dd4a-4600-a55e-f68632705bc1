generator client {
  provider      = "prisma-client-js"
  output        = "../src/generated/prisma"
  binaryTargets = ["native", "rhel-openssl-1.0.x", "linux-arm64-openssl-1.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id                                                 String                           @id @default(cuid())
  name                                               String?
  email                                              String?                          @unique
  emailVerified                                      DateTime?
  image                                              String?
  password                                           String?
  role                                               UserRole                         @default(TRAINEE)
  traineeLevel                                       Int?
  phoneNumber                                        String?
  address                                            String?
  qualificationRoute                                 String?
  mentorId                                           String?
  createdAt                                          DateTime                         @default(now())
  updatedAt                                          DateTime                         @updatedAt
  skillGroupType                                     String?                          @default("TRADITIONAL")
  isTrainingPrincipal                                Boolean?                         @default(false)
  qualificationRouteDismissed                        Boolean?
  qualificationRouteUpdatedAt                        DateTime?                        @db.Date
  resetToken                                         String?                          @unique
  resetTokenExpiry                                   DateTime?
  accounts                                           Account[]
  impersonations                                     AdminImpersonation[]             @relation("AdminImpersonator")
  AdminImpersonation_AdminImpersonation_userIdToUser AdminImpersonation[]             @relation("AdminImpersonation_userIdToUser")
  characterSuitabilityCompletions                    CharacterSuitabilityCompletion[]
  supervisedClients                                  ClientSupervisor[]               @relation("SupervisorToClient")
  reviewedEntries                                    Entry[]                          @relation("EntryReviewer")
  candidateMeetings                                  Meeting[]                        @relation("CandidateMeetings")
  mentorMeetings                                     Meeting[]                        @relation("MentorMeetings")
  mentorAvailability                                 MentorAvailability[]             @relation("MentorAvailability")
  MonthlyReview_MonthlyReview_candidateIdToUser      MonthlyReview[]                  @relation("MonthlyReview_candidateIdToUser")
  MonthlyReview_MonthlyReview_mentorIdToUser         MonthlyReview[]                  @relation("MonthlyReview_mentorIdToUser")
  mentoredPlacements                                 Placement[]                      @relation("MentorPlacements")
  supervisedPlacements                               Placement[]                      @relation("SupervisorPlacements")
  placements                                         Placement[]
  qualifications                                     Qualification[]
  sessions                                           Session[]
  reviewedSubmissions                                Submission[]                     @relation("SubmissionReviewer")
  supervisedSubmissions                              Submission[]                     @relation("SubmissionSupervisor")
  submissions                                        Submission[]
  traineeSkills                                      TraineeSkill[]
  uploadedMeetingTemplates                           MeetingTemplate[]                @relation("MeetingTemplateUploader")
  courseClicks                                       CourseClick[]
  mentor                                             User?                            @relation("TraineeMentor", fields: [mentorId], references: [id])
  mentees                                            User[]                           @relation("TraineeMentor")
  notifications                                      Notification[]
  mentorHistory                                      MentorMenteeHistory[]            @relation("MentorHistory")
  menteeHistory                                      MentorMenteeHistory[]            @relation("MenteeHistory")
  supervisorHistory                                  SupervisorTraineeHistory[]       @relation("SupervisorHistory")
  traineeHistory                                     SupervisorTraineeHistory[]       @relation("TraineeHistory")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Client {
  id          String             @id @default(cuid())
  name        String
  email       String
  phone       String?
  address     String?
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  supervisors ClientSupervisor[] @relation("ClientToSupervisor")
  placements  Placement[]
}

model ClientSupervisor {
  clientId     String
  supervisorId String
  assignedAt   DateTime @default(now())
  client       Client   @relation("ClientToSupervisor", fields: [clientId], references: [id])
  supervisor   User     @relation("SupervisorToClient", fields: [supervisorId], references: [id])

  @@id([clientId, supervisorId])
}

model Placement {
  id                       String                     @id @default(cuid())
  createdAt                DateTime                   @default(now())
  updatedAt                DateTime                   @updatedAt
  name                     String
  startDate                DateTime
  endDate                  DateTime?
  orgPosition              String?
  orgSraNumber             String?
  supervisorName           String
  supervisorEmail          String
  isFullTime               Boolean                    @default(true)
  partTimeDays             Int[]
  daysMissed               Int                        @default(0)
  documentKey              String?
  documentName             String?
  userId                   String
  clientId                 String
  supervisorId             String?
  mentorId                 String?
  qweConfirmingPerson      String?
  qweSraNumber             String?
  qweEmail                 String?
  entries                  Entry[]
  client                   Client                     @relation(fields: [clientId], references: [id])
  mentor                   User?                      @relation("MentorPlacements", fields: [mentorId], references: [id])
  supervisor               User?                      @relation("SupervisorPlacements", fields: [supervisorId], references: [id])
  user                     User                       @relation(fields: [userId], references: [id])
  supervisorTraineeHistory SupervisorTraineeHistory[]
}

model PracticeArea {
  id             String          @id @default(uuid())
  name           String
  createdAt      DateTime        @default(now())
  practiceSkills PracticeSkill[]
}

model PracticeSkill {
  id                  String               @id @default(uuid())
  practiceAreaId      String
  name                String
  createdAt           DateTime             @default(now())
  practiceArea        PracticeArea         @relation(fields: [practiceAreaId], references: [id])
  practiceSkillGroups PracticeSkillGroup[]
}

model PracticeSkillGroup {
  id                String             @id @default(uuid())
  practiceSkillId   String
  name              String
  createdAt         DateTime           @default(now())
  practiceSkill     PracticeSkill      @relation(fields: [practiceSkillId], references: [id])
  practiceSubSkills PracticeSubSkill[]
}

model PracticeSubSkill {
  id                     String               @id @default(uuid())
  practiceSkillGroupId   String
  name                   String
  practiceSubSkillType   PracticeSubSkillType
  minSuggestedEntryCount Int
  order                  Int
  createdAt              DateTime             @default(now())
  entrySubSkills         EntrySubSkill[]
  practiceSkillGroup     PracticeSkillGroup   @relation(fields: [practiceSkillGroupId], references: [id])
  traineeSkills          TraineeSkill[]
}

model EntrySubSkill {
  entryId    String
  subSkillId String
  entry      Entry            @relation(fields: [entryId], references: [id])
  subSkill   PracticeSubSkill @relation(fields: [subSkillId], references: [id])

  @@id([entryId, subSkillId])
}

model Entry {
  id                 String          @id @default(uuid())
  createdAt          DateTime        @default(now())
  updatedAt          DateTime        @updatedAt
  title              String
  startDate          DateTime
  endDate            DateTime
  experience         String
  evidence           String
  learnt             String
  moreExperience     String?
  needMoreExperience String?
  contentiousType    String
  taskedBy           String?
  documentKey        String?
  documentName       String?
  practiceAreas      String[]
  submittedAt        DateTime?
  reviewedAt         DateTime?
  feedback           String?
  placementId        String
  reviewerId         String?
  submissionId       String?
  status             String          @default("draft")
  placement          Placement       @relation(fields: [placementId], references: [id])
  reviewer           User?           @relation("EntryReviewer", fields: [reviewerId], references: [id])
  submission         Submission?     @relation(fields: [submissionId], references: [id])
  entrySubSkills     EntrySubSkill[]
}

model Submission {
  id           String   @id @default(cuid())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  startDate    DateTime
  endDate      DateTime
  status       String   @default("pending")
  submittedAt  DateTime @default(now())
  traineeId    String
  reviewerId   String?
  supervisorId String?
  title        String   @default("Untitled Submission")
  entries      Entry[]
  reviewer     User?    @relation("SubmissionReviewer", fields: [reviewerId], references: [id])
  supervisor   User?    @relation("SubmissionSupervisor", fields: [supervisorId], references: [id])
  trainee      User     @relation(fields: [traineeId], references: [id])
}

model TraineeSkill {
  id                     String           @id @default(cuid())
  doneEntryCount         Int              @default(0)
  minSuggestedEntryCount Int              @default(1)
  createdAt              DateTime         @default(now())
  updatedAt              DateTime         @updatedAt
  traineeId              String
  subSkillId             String
  subSkill               PracticeSubSkill @relation(fields: [subSkillId], references: [id])
  trainee                User             @relation(fields: [traineeId], references: [id])

  @@unique([traineeId, subSkillId])
}

model Qualification {
  id        String              @id @default(cuid())
  type      QualificationType
  name      String
  fileUrl   String?
  fileName  String?
  startDate DateTime?
  endDate   DateTime?
  grade     String?
  status    QualificationStatus @default(PENDING)
  createdAt DateTime            @default(now())
  updatedAt DateTime            @updatedAt
  userId    String
  user      User                @relation(fields: [userId], references: [id])
}

model CharacterSuitability {
  id                         String                           @id @default(cuid())
  rules                      String
  infoForQualifiedSolicitors String
  externalLinks              Json
  createdAt                  DateTime                         @default(now())
  updatedAt                  DateTime                         @updatedAt
  userCompletions            CharacterSuitabilityCompletion[]
}

model CharacterSuitabilityCompletion {
  id                     String               @id @default(cuid())
  completed              Boolean              @default(false)
  completedAt            DateTime?
  createdAt              DateTime             @default(now())
  updatedAt              DateTime             @updatedAt
  userId                 String
  characterSuitabilityId String
  characterSuitability   CharacterSuitability @relation(fields: [characterSuitabilityId], references: [id])
  user                   User                 @relation(fields: [userId], references: [id])

  @@unique([userId, characterSuitabilityId])
}

model MentorAvailability {
  id              String        @id @default(cuid())
  mentorId        String
  startTime       DateTime
  endTime         DateTime
  isBooked        Boolean       @default(false)
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  meetingMethod   MeetingFormat @default(VIRTUAL)
  meetingLocation String?
  duration        Int?          @default(30)
  bufferTime      Int?          @default(10)
  meetingMessage  String?
  meetings        Meeting[]
  mentor          User          @relation("MentorAvailability", fields: [mentorId], references: [id])
}

model Meeting {
  id              String              @id @default(cuid())
  type            MeetingType
  candidateId     String
  mentorId        String
  availabilityId  String?
  status          MeetingStatus       @default(PENDING)
  proposedTime    DateTime
  notes           String?
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt
  meetingUrl      String?
  meetingLocation String?
  availability    MentorAvailability? @relation(fields: [availabilityId], references: [id])
  candidate       User                @relation("CandidateMeetings", fields: [candidateId], references: [id])
  mentor          User                @relation("MentorMeetings", fields: [mentorId], references: [id])
  monthlyReview   MonthlyReview?      @relation("MeetingMonthlyReview")
}

model FeedbackForm {
  id                              String             @id @default(cuid())
  monthlyReviewId                 String             @unique
  coveringMonth                   String?
  menteeName                      String
  meetingDate                     DateTime
  pointsToDiscuss                 String?
  wasGoodWork                     Boolean            @default(false)
  goodWorkComments                String?
  hasFeltResponsible              Boolean            @default(false)
  responsibilityComments          String?
  workConcerns                    String?
  isWorkingWellSupervisor         Boolean            @default(false)
  workingWellSupervisorComments   String?
  hadInformalMonthlyFeedback      Boolean            @default(false)
  informalMonthlyFeedbackComments String?
  skillStandardsComments          String?
  anyConcerns                     String?
  furtherTraining                 String?
  appraisalMeeting                String?
  qualifyingArea                  String?
  mentorNotes                     String?
  furtherAction                   String?
  pointsToNote                    String?
  signatureKey                    String?
  signatureName                   String?
  printName                       String?
  status                          FeedbackFormStatus @default(DRAFT)
  createdAt                       DateTime           @default(now())
  updatedAt                       DateTime           @updatedAt
  monthlyReview                   MonthlyReview      @relation(fields: [monthlyReviewId], references: [id])
}

model MonthlyReview {
  id             String        @id @default(cuid())
  documentName   String?
  completionDate DateTime
  action         String?
  s3Key          String?
  documentType   DocumentType  @default(MONTHLY_REVIEW)
  referredToTP   Boolean       @default(false)
  candidateId    String
  mentorId       String?
  meetingId      String?       @unique
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  feedbackForm   FeedbackForm?
  candidate      User          @relation("MonthlyReview_candidateIdToUser", fields: [candidateId], references: [id])
  meeting        Meeting?      @relation("MeetingMonthlyReview", fields: [meetingId], references: [id])
  mentor         User?         @relation("MonthlyReview_mentorIdToUser", fields: [mentorId], references: [id])
}

model Course {
  id               String                 @id @default(cuid())
  name             String
  description      String
  imageUrl         String?
  externalLink     String
  pricing          String?
  categoryId       String
  createdAt        DateTime               @default(now())
  updatedAt        DateTime               @updatedAt
  category         CourseCategory         @relation(fields: [categoryId], references: [id])
  courseClicks     CourseClick[]
  secondCategories CourseSecondCategory[]
}

model CourseCategory {
  id                     String                 @id @default(cuid())
  name                   String                 @unique
  description            String?
  color                  String?
  imageUrl               String?
  isClassroom            Boolean                @default(false)
  isSecondCategory       Boolean                @default(false)
  faqs                   Json?                  @default("[]")
  createdAt              DateTime               @default(now())
  updatedAt              DateTime               @updatedAt
  courses                Course[]
  courseSecondCategories CourseSecondCategory[]
}

model CourseSecondCategory {
  id         String         @id @default(cuid())
  courseId   String
  categoryId String
  createdAt  DateTime       @default(now())
  course     Course         @relation(fields: [courseId], references: [id], onDelete: Cascade)
  category   CourseCategory @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@unique([courseId, categoryId])
}

model CourseClick {
  id        String   @id @default(cuid())
  userId    String
  courseId  String
  clickedAt DateTime @default(now())
  ipAddress String?
  userAgent String?
  user      User     @relation(fields: [userId], references: [id])
  course    Course   @relation(fields: [courseId], references: [id])

  @@index([courseId])
  @@index([userId])
  @@index([clickedAt])
}

model AdminImpersonation {
  id                                   String    @id @default(cuid())
  adminId                              String
  userId                               String
  userRole                             UserRole
  reason                               String?
  startedAt                            DateTime  @default(now())
  endedAt                              DateTime?
  ipAddress                            String?
  userAgent                            String?
  admin                                User      @relation("AdminImpersonator", fields: [adminId], references: [id])
  User_AdminImpersonation_userIdToUser User      @relation("AdminImpersonation_userIdToUser", fields: [userId], references: [id])

  @@index([adminId])
  @@index([userId])
}

model Notification {
  id        String   @id @default(cuid())
  userId    String
  title     String
  message   String
  type      String
  url       String?
  isRead    Boolean  @default(false)
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, isRead])
}

enum UserRole {
  ADMIN
  SUPERVISOR
  MENTOR
  TRAINEE
}

enum EntryStatus {
  draft
  submitted
  signedoff
  rejected
}

enum PracticeSubSkillType {
  sqe
  tc
  both
}

enum QualificationType {
  A_LEVEL
  DEGREE
  GDL
  LPC
  PSC
  SQE1
  SQE2
  QUALIFICATION_CERT
  PRACTICING_CERT
  OTHER
}

enum QualificationStatus {
  PENDING
  VERIFIED
  REJECTED
}

enum MeetingType {
  MONTHLY
  TPM
  APPRAISAL
}

enum MeetingStatus {
  PENDING
  ACCEPTED
  DECLINED
  COMPLETED
}

enum MeetingFormat {
  IN_PERSON
  VIRTUAL
}

enum DocumentType {
  MONTHLY_REVIEW
  FEEDBACK_FORM
  OTHER
  APPRAISAL_FORM
  TPM_MEETING_FORM
  MENTOR_REFERRAL_FORM
}

enum FeedbackFormStatus {
  DRAFT
  SUBMITTED
  APPROVED
}

model CompetencyPlus {
  id               String    @id @default(cuid())
  title            String
  shortDescription String
  content          String
  externalLinks    Json?     @default("[]")
  audioUrl         String?
  videoUrl         String?
  bannerImageUrl   String?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  published        Boolean   @default(false)
  publishedAt      DateTime?
}

model MeetingTemplate {
  id          String   @id @default(cuid())
  name        String
  description String?
  fileName    String
  s3Key       String
  version     Int      @default(1)
  isActive    Boolean  @default(true)
  uploadedBy  String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  uploader    User     @relation("MeetingTemplateUploader", fields: [uploadedBy], references: [id])

  @@index([isActive])
  @@index([createdAt])
}

model MentorMenteeHistory {
  id        String    @id @default(cuid())
  mentorId  String
  menteeId  String
  startDate DateTime  @default(now())
  endDate   DateTime?
  isActive  Boolean   @default(true)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  mentor    User      @relation("MentorHistory", fields: [mentorId], references: [id], onDelete: Cascade)
  mentee    User      @relation("MenteeHistory", fields: [menteeId], references: [id], onDelete: Cascade)

  @@index([mentorId, isActive])
  @@index([menteeId, isActive])
  @@index([startDate])
  @@index([endDate])
}

model SupervisorTraineeHistory {
  id           String    @id @default(cuid())
  supervisorId String
  traineeId    String
  placementId  String
  startDate    DateTime  @default(now())
  endDate      DateTime?
  isActive     Boolean   @default(true)
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  supervisor   User      @relation("SupervisorHistory", fields: [supervisorId], references: [id], onDelete: Cascade)
  trainee      User      @relation("TraineeHistory", fields: [traineeId], references: [id], onDelete: Cascade)
  placement    Placement @relation(fields: [placementId], references: [id], onDelete: Cascade)

  @@index([supervisorId, isActive])
  @@index([traineeId, isActive])
  @@index([placementId])
  @@index([startDate])
  @@index([endDate])
}
