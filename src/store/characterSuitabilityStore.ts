import { create } from 'zustand';
import { CharacterSuitability, CharacterSuitabilityCompletion } from '@/types/character-suitability';

interface CharacterSuitabilityState {
  characterSuitability: CharacterSuitability | null;
  completions: CharacterSuitabilityCompletion[];
  loading: boolean;
  error: string | null;
  fetchCharacterSuitability: () => Promise<void>;
  fetchCompletions: () => Promise<void>;
  updateCharacterSuitability: (data: Partial<CharacterSuitability>) => Promise<void>;
  updateCompletion: (characterSuitabilityId: string, completed: boolean) => Promise<void>;
}

const useCharacterSuitabilityStore = create<CharacterSuitabilityState>((set, get) => ({
  characterSuitability: null,
  completions: [],
  loading: false,
  error: null,

  fetchCharacterSuitability: async () => {
    set({ loading: true, error: null });
    try {
      const response = await fetch('/api/character-suitability');
      if (!response.ok) throw new Error('Failed to fetch character suitability');
      const data = await response.json();
      set({ characterSuitability: data });
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'An error occurred' });
    } finally {
      set({ loading: false });
    }
  },

  fetchCompletions: async () => {
    set({ loading: true, error: null });
    try {
      const response = await fetch('/api/character-suitability/completion');
      if (!response.ok) throw new Error('Failed to fetch completions');
      const data = await response.json();
      set({ completions: data });
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'An error occurred' });
    } finally {
      set({ loading: false });
    }
  },

  updateCharacterSuitability: async (data) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch('/api/character-suitability', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      if (!response.ok) throw new Error('Failed to update character suitability');
      const updatedData = await response.json();
      set({ characterSuitability: updatedData });
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'An error occurred' });
    } finally {
      set({ loading: false });
    }
  },

  updateCompletion: async (characterSuitabilityId: string, completed: boolean) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch('/api/character-suitability/completion', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ characterSuitabilityId, completed }),
      });
      if (!response.ok) throw new Error('Failed to update completion status');
      await get().fetchCharacterSuitability();
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'An error occurred' });
    } finally {
      set({ loading: false });
    }
  },
}));

export default useCharacterSuitabilityStore; 