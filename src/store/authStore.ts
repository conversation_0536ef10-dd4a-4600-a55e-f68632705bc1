import { create } from 'zustand';
import { User } from '@/types';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  setUser: (user: User | null) => void;
  setLoading: (loading: boolean) => void;
  logout: () => void;
  initAuth: () => void;
}

const STORAGE_KEY = 'auth_user';

export const useAuthStore = create<AuthState>((set) => {
  const handleStorageChange = (event: StorageEvent) => {
    if (event.key === STORAGE_KEY) {
      if (event.newValue) {
        const user = JSON.parse(event.newValue);
        set({ user, isAuthenticated: true });
      } else {
        set({ user: null, isAuthenticated: false });
      }
    }
  };

  if (typeof window !== 'undefined') {
    window.addEventListener('storage', handleStorageChange);
  }

  return {
    user: null,
    isAuthenticated: false,
    isLoading: true,
    setUser: (user) => {
      if (user) {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(user));
      } else {
        localStorage.removeItem(STORAGE_KEY);
      }
      set({ user, isAuthenticated: !!user });
    },
    setLoading: (loading) => set({ isLoading: loading }),
    logout: () => {
      localStorage.removeItem(STORAGE_KEY);
      sessionStorage.clear();
      set({ user: null, isAuthenticated: false });
    },
    initAuth: () => {
      try {
        const storedUser = localStorage.getItem(STORAGE_KEY);
        if (storedUser) {
          const user = JSON.parse(storedUser);
          set({ user, isAuthenticated: true });
        } else {
          set({ user: null, isAuthenticated: false });
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        localStorage.removeItem(STORAGE_KEY);
        sessionStorage.clear();
        set({ user: null, isAuthenticated: false });
      }
      set({ isLoading: false });
    },
  };
}); 