import { create } from 'zustand';
import type { Placement } from '@/types';

interface PlacementResponse {
  placements: {
    edges: Placement[];
    pageInfo: {
      total: number;
      offset: number;
      limit: number;
    };
  };
}

interface PlacementState {
  placements: Placement[];
  loading: boolean;
  error: string | null;
  pageInfo: {
    total: number;
    offset: number;
    limit: number;
  };
  searchQuery: string;
  // Actions
  fetchPlacements: (params: {
    offset?: number;
    limit?: number;
    search?: string;
    sortField?: string;
    sortOrder?: 'ascend' | 'descend';
  }) => Promise<void>;
  setSearchQuery: (query: string) => void;
}

export const usePlacementStore = create<PlacementState>((set) => ({
  placements: [],
  loading: false,
  error: null,
  pageInfo: {
    total: 0,
    offset: 0,
    limit: 10,
  },
  searchQuery: '',

  fetchPlacements: async (params) => {
    set({ loading: true, error: null });
    try {
      const queryParams = new URLSearchParams({
        offset: params.offset?.toString() || '0',
        limit: params.limit?.toString() || '10',
        ...(params.search && { search: params.search }),
        ...(params.sortField && { sortField: params.sortField }),
        ...(params.sortOrder && { sortOrder: params.sortOrder }),
      });

      const response = await fetch(`/api/placements?${queryParams}`);

      if (!response.ok) {
        throw new Error('Failed to fetch placements');
      }

      const data: PlacementResponse = await response.json();

      set({
        placements: data.placements.edges,
        pageInfo: data.placements.pageInfo,
        loading: false,
      });
    } catch (error) {
      console.error('Error fetching placements:', error);
    }
  },

  setSearchQuery: (query) => set({ searchQuery: query }),
}));
