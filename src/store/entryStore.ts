import { create } from 'zustand';
import { App } from 'antd';
import type { Entry, EntryFormData, EntryFilters, PaginationParams } from '@/types';

interface EntryState {
  entries: Entry[];
  loading: boolean;
  error: string | null;
  selectedEntry: Entry | null;
  filters: EntryFilters;
  pagination: PaginationParams;
  
  // Actions
  setSelectedEntry: (entry: Entry | null) => void;
  setFilters: (filters: EntryFilters) => void;
  setPagination: (pagination: Partial<PaginationParams>) => void;
  
  // API Actions
  fetchEntries: (params?: {
    page?: number;
    pageSize?: number;
    filters?: EntryFilters;
    sort?: { [key: string]: 'ascend' | 'descend' };
    search?: string;
  }) => Promise<void>;
  
  createEntry: (data: EntryFormData) => Promise<void>;
  updateEntry: (id: string, data: Partial<EntryFormData>) => Promise<void>;
  deleteEntry: (id: string) => Promise<void>;
  uploadDocuments: (files: File[]) => Promise<string[]>;
}

export const useEntryStore = create<EntryState>((set, get) => {
  const { message } = App.useApp();
  return {
  entries: [],
  loading: false,
  error: null,
  selectedEntry: null,
  filters: {},
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0,
  },

  setSelectedEntry: (entry) => set({ selectedEntry: entry }),
  setFilters: (filters) => set({ filters }),
  setPagination: (pagination) => set({ 
    pagination: { ...get().pagination, ...pagination } 
  }),

  fetchEntries: async (params) => {
    set({ loading: true, error: null });
    try {
      const { filters, pagination } = get();
      const queryParams = new URLSearchParams();

      queryParams.append('page', String(params?.page || pagination.current));
      queryParams.append('pageSize', String(params?.pageSize || pagination.pageSize));

      if (params?.filters) {
        Object.entries(params.filters).forEach(([key, value]) => {
          if (value !== undefined) {
            queryParams.append(key, String(value));
          }
        });
      }

      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, String(value));
        }
      });

      if (params?.sort) {
        Object.entries(params.sort).forEach(([key, value]) => {
          queryParams.append(`sort[${key}]`, value);
        });
      }

      if (params?.search) {
        queryParams.append('search', params.search);
      }

      const response = await fetch(`/api/entries?${queryParams.toString()}`);
      if (!response.ok) throw new Error('Failed to fetch entries');

      const data = await response.json();
      set({
        entries: data.entries,
        pagination: {
          ...pagination,
          total: data.total,
        },
        loading: false,
      });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch entries',
        loading: false,
      });
    }
  },

  createEntry: async (data) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch('/api/entries', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...data,
          practiceSubSkillIds: data.practiceSubSkillIds
        }),
      });

      if (!response.ok) throw new Error('Failed to create entry');

      const newEntry = await response.json();
      set((state) => ({
        entries: [newEntry, ...state.entries],
        loading: false,
      }));

      message.success('Entry created successfully');
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to create entry',
        loading: false,
      });
      message.error('Failed to create entry');
    }
  },

  updateEntry: async (id, data) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch(`/api/entries/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...data,
          practiceSubSkillIds: data.practiceSubSkillIds
        }),
      });

      if (!response.ok) throw new Error('Failed to update entry');

      const updatedEntry = await response.json();
      set((state) => ({
        entries: state.entries.map((entry) =>
          entry.id === id ? updatedEntry : entry
        ),
        loading: false,
      }));

      message.success('Entry updated successfully');
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to update entry',
        loading: false,
      });
      message.error('Failed to update entry');
    }
  },

  deleteEntry: async (id) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch(`/api/entries/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Failed to delete entry');

      set((state) => ({
        entries: state.entries.filter((entry) => entry.id !== id),
        loading: false,
      }));

      message.success('Entry deleted successfully');
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to delete entry',
        loading: false,
      });
      message.error('Failed to delete entry');
    }
  },

  uploadDocuments: async (files: File[]): Promise<string[]> => {
    const urls: string[] = [];
    for (const file of files) {
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        throw new Error(`File ${file.name} exceeds 10MB limit`);
      }

      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Failed to upload ${file.name}`);
      }

      const { url } = await response.json();
      urls.push(url);
    }
    return urls;
  },
  };
});
