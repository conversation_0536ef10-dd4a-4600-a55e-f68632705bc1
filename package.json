{"name": "pathways-2.1", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --no-lint", "start": "next start", "lint": "next lint", "prepare-db": "prisma generate && prisma db push", "seed-db": "prisma db seed", "local-db": "cd db-local && docker-compose up", "postinstall": "prisma generate"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@aws-sdk/client-s3": "^3.797.0", "@aws-sdk/s3-request-presigner": "^3.797.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.8.2", "@tailwindcss/postcss": "^4.1.5", "@types/node": "^22.15.12", "@uiw/react-md-editor": "^4.0.7", "antd": "^5.24.8", "bcryptjs": "^3.0.2", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "moment": "^2.30.1", "next": "15.3.1", "next-auth": "^4.24.11", "nodemailer": "^6.6.5", "pg": "^8.15.5", "prisma": "^6.6.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "tailwindcss": "^4", "xlsx": "^0.18.5", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/bcryptjs": "^2.4.6", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.3.1", "ts-node": "^10.9.2", "typescript": "^5"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "resolutions": {"rollup": "npm:@rollup/wasm-node"}}