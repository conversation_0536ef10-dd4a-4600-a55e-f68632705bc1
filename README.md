# Role-Based Placement Management Web App

A web application for managing placements, entries, and skill sets with role-based access control.

## Features

- Role-based access control (<PERSON><PERSON>, Supervisor, Men<PERSON>, Candidate)
- Client management
- User management
- Placement management
- Skill set management
- Entry submission and review
- File upload support
- Modern UI with Ant Design

## Tech Stack

- Frontend: Next.js with TypeScript
- UI Library: Ant Design
- State Management: Zustand
- Authentication: NextAuth.js
- Database: PostgreSQL with Prisma

## Prerequisites

Before you begin, ensure you have the following installed:
- Node.js (v18 or higher)
- npm (v9 or higher)
- Docker and Docker Compose
- Git

## Getting Started

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd pathways-2.1
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   ```bash
   # Copy the example environment file
   cp .env.example .env
   ```
   
   Edit the `.env` file and update the following variables:
   ```
   # Database
   DATABASE_URL="postgresql://postgres:postgres@localhost:5432/pathways?schema=public"
   
   # Authentication
   NEXTAUTH_URL=http://localhost:3000
   NEXTAUTH_SECRET=your-secret-key
   
   # Add other required environment variables
   ```

4. Start the local database:
   ```bash
   npm run local-db
   ```
   This will start a PostgreSQL database in Docker.

5. Prepare the database:
   ```bash
   npm run prepare-db
   ```
   This command will:
   - Generate the Prisma client
   - Push the database schema
   - Seed the database with initial data

6. Run the development server:
   ```bash
   npm run dev
   ```

7. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Development Workflow

### Database Management

- To start the local database: `npm run local-db`
- To stop the local database: `cd db-local && docker-compose down`
- To reset the database: `npm run prepare-db`

### Available Scripts

- `npm run dev` - Start the development server
- `npm run build` - Build the application for production
- `npm run start` - Start the production server
- `npm run lint` - Run ESLint to check code quality
- `npm run prepare-db` - Set up the database (generate client, push schema, seed data)
- `npm run local-db` - Start the local PostgreSQL database in Docker

## Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── admin/             # Admin routes
│   ├── candidate/         # Candidate routes
│   ├── supervisor/        # Supervisor routes
│   ├── mentor/           # Mentor routes
│   ├── api/              # API routes
│   └── login/            # Authentication
├── components/            # Reusable components
│   └── layouts/          # Layout components
├── store/                # Zustand store
├── types/                # TypeScript types
└── utils/                # Utility functions
```

## Role-Based Access

### Admin
- Create & manage clients, supervisors, mentors, candidates
- Assign supervisors/mentors to placements
- Modify all profiles and reset passwords
- Create and manage skill sets
- View and manage all entries

### Supervisor
- Review candidate placements assigned to them
- Approve/reject candidate entries

### Mentor
- Same as supervisor (specific to their role)

### Candidate
- View assigned placement and related supervisors/mentors
- Upload documents (entry) and tag with skills
- Submit entries for supervisor/mentor approval

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Ensure Docker is running
   - Check if the database container is up: `docker ps`
   - Verify DATABASE_URL in .env matches the Docker configuration

2. **Prisma Errors**
   - Run `npm run prepare-db` to regenerate the client and update the schema
   - Check the Prisma schema file for any syntax errors

3. **Port Conflicts**
   - If port 3000 is in use, the server will automatically use the next available port
   - Check the console output for the actual port number

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
