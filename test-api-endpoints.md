# API Endpoints Testing for Entry Submission Feature

## Updated Endpoints

### 1. `/api/submissions` (Main submissions API)
**Changes Made:**
- Updated SUPERVISOR filtering to only use `reviewerId`
- MENTOR filtering already uses `reviewerId` 
- Added `entrySubmissions` include in response

**Test Cases:**
- [ ] TRAINEE: Should see all their submissions
- [ ] SUPERVISOR: Should only see submissions sent to them as reviewer
- [ ] MENTOR: Should only see submissions sent to them as reviewer
- [ ] ADMIN: Should see all submissions

### 2. `/api/supervisor/submissions` (Supervisor-specific)
**Changes Made:**
- Simplified filtering to only submissions sent to supervisor as reviewer
- Added `entrySubmissions` include
- Added `placement` include

**Test Cases:**
- [ ] Should only return submissions where `reviewerId = supervisor.id`
- [ ] Should include current trainees only
- [ ] Should include entrySubmissions data
- [ ] Should include placement information

### 3. `/api/mentor/submissions` (New endpoint)
**Changes Made:**
- Created new endpoint for mentors
- Filters submissions sent to mentor as reviewer
- Includes current mentees only

**Test Cases:**
- [ ] Should only return submissions where `reviewerId = mentor.id`
- [ ] Should include current mentees only
- [ ] Should include entrySubmissions data
- [ ] Should include placement information

### 4. `/api/trainees/[id]/submissions` (Trainee submissions for supervisor)
**Changes Made:**
- Updated filtering to use `reviewerId` instead of placement-based filtering
- Added `entrySubmissions` include

**Test Cases:**
- [ ] Should only return submissions sent to the requesting supervisor
- [ ] Should include entrySubmissions data

### 5. `/api/entries` (Entry listing)
**Changes Made:**
- Added `entrySubmissions` include with submission details

**Test Cases:**
- [ ] Should include entrySubmissions array
- [ ] Should show which reviewers each entry has been sent to

## Frontend Components Updated

### 1. `PlacementDetails.tsx`
**Changes Made:**
- Updated availability logic to check specific reviewer
- Updated UI to show per-reviewer status
- Updated selection logic

**Test Cases:**
- [ ] Entry shows "Available" for reviewers it hasn't been sent to
- [ ] Entry shows "Already sent to [Reviewer Name]" for specific reviewer
- [ ] Entry can be selected for available reviewers only
- [ ] Correct count of available entries per reviewer

### 2. `src/app/mentor/entries/review/page.tsx`
**Changes Made:**
- Updated to use `/api/mentor/submissions`
- Updated placement rendering logic

**Test Cases:**
- [ ] Shows only submissions sent to the mentor
- [ ] Displays placement information correctly
- [ ] Links work correctly

### 3. `src/components/submissions/SubmissionDetail.tsx`
**Changes Made:**
- Updated to use entrySubmissions when available
- Fallback to entries for backward compatibility

**Test Cases:**
- [ ] Shows correct entry count
- [ ] Displays entries correctly from entrySubmissions or entries

## Database Verification

### EntrySubmission Records
```sql
-- Check EntrySubmission records are created correctly
SELECT 
  es.id,
  es.entryId,
  es.submissionId,
  es.status,
  s.reviewerId,
  s.reviewerRole,
  u.name as reviewerName
FROM "EntrySubmission" es
JOIN "Submission" s ON es.submissionId = s.id
JOIN "User" u ON s.reviewerId = u.id
ORDER BY es.createdAt DESC;
```

### Submission Records
```sql
-- Check submissions have correct reviewerRole and placementId
SELECT 
  id,
  title,
  reviewerId,
  reviewerRole,
  placementId,
  traineeId
FROM "Submission"
ORDER BY createdAt DESC;
```

### Entry Availability Check
```sql
-- Check which entries are available for which reviewers
SELECT 
  e.id as entryId,
  e.title,
  e.placementId,
  p.supervisorId,
  u.mentorId,
  COUNT(es.id) as submissionCount,
  STRING_AGG(s.reviewerId, ', ') as sentToReviewers
FROM "Entry" e
JOIN "Placement" p ON e.placementId = p.id
JOIN "User" u ON p.userId = u.id
LEFT JOIN "EntrySubmission" es ON e.id = es.entryId
LEFT JOIN "Submission" s ON es.submissionId = s.id
GROUP BY e.id, e.title, e.placementId, p.supervisorId, u.mentorId
ORDER BY e.createdAt DESC;
```

## Manual Testing Steps

### 1. Create Test Entry
- [ ] Login as trainee
- [ ] Create a new entry
- [ ] Verify entry shows as "Available" for both supervisor and mentor

### 2. Send to Supervisor
- [ ] Send entry to supervisor
- [ ] Verify entry shows "Already sent to [Supervisor Name]" for supervisor
- [ ] Verify entry still shows "Available" for mentor
- [ ] Check database: EntrySubmission record created with correct reviewerId

### 3. Send to Mentor
- [ ] Send same entry to mentor
- [ ] Verify entry shows "Already sent to [Mentor Name]" for mentor
- [ ] Verify entry still shows "Already sent to [Supervisor Name]" for supervisor
- [ ] Check database: Second EntrySubmission record created

### 4. Supervisor View
- [ ] Login as supervisor
- [ ] Check `/api/supervisor/submissions` returns only submissions sent to them
- [ ] Verify supervisor dashboard shows correct submissions

### 5. Mentor View
- [ ] Login as mentor
- [ ] Check `/api/mentor/submissions` returns only submissions sent to them
- [ ] Verify mentor review page shows correct submissions

## Expected Results

### Before Implementation
- Entry could only be sent to one reviewer
- Once sent, entry became unavailable for all reviewers
- Supervisor saw all submissions from their trainees regardless of reviewer

### After Implementation
- Entry can be sent to multiple reviewers independently
- Entry shows specific availability per reviewer
- Supervisor only sees submissions sent to them as reviewer
- Mentor only sees submissions sent to them as reviewer
- Clear separation of reviewer-specific data
