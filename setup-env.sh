#!/bin/bash

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
  echo "Creating .env file..."
  cat > .env << EOL
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/pathway21?schema=public"
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
EOL
  echo ".env file created successfully!"
else
  echo ".env file already exists."
fi

# Check if DATABASE_URL is set
if grep -q "DATABASE_URL" .env; then
  echo "DATABASE_URL is already set in .env file."
else
  echo "Adding DATABASE_URL to .env file..."
  echo 'DATABASE_URL="postgresql://postgres:postgres@localhost:5432/pathway21?schema=public"' >> .env
  echo "DATABASE_URL added to .env file."
fi

# Check if NEXTAUTH_SECRET is set
if grep -q "NEXTAUTH_SECRET" .env; then
  echo "NEXTAUTH_SECRET is already set in .env file."
else
  echo "Adding NEXTAUTH_SECRET to .env file..."
  echo 'NEXTAUTH_SECRET="your-secret-key"' >> .env
  echo "NEXTAUTH_SECRET added to .env file."
fi

# Check if NEXTAUTH_URL is set
if grep -q "NEXTAUTH_URL" .env; then
  echo "NEXTAUTH_URL is already set in .env file."
else
  echo "Adding NEXTAUTH_URL to .env file..."
  echo 'NEXTAUTH_URL="http://localhost:3000"' >> .env
  echo "NEXTAUTH_URL added to .env file."
fi

echo "Environment setup complete!"
echo "Please restart your application for the changes to take effect." 